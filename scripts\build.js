const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting build process...');

// Clean previous builds
console.log('🧹 Cleaning previous builds...');
if (fs.existsSync('dist')) {
  fs.rmSync('dist', { recursive: true, force: true });
}
if (fs.existsSync('out')) {
  fs.rmSync('out', { recursive: true, force: true });
}
if (fs.existsSync('release')) {
  fs.rmSync('release', { recursive: true, force: true });
}

try {
  // Build Next.js app
  console.log('📦 Building Next.js application...');
  execSync('npm run build-app', { stdio: 'inherit' });

  // Build Electron main process
  console.log('⚡ Building Electron main process...');
  execSync('npm run build-electron', { stdio: 'inherit' });

  // Create Electron distribution
  console.log('🔧 Creating Electron distribution...');
  execSync('npm run dist-win', { stdio: 'inherit' });

  console.log('✅ Build completed successfully!');
  console.log('📁 Check the "release" folder for the installer.');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}
