import React, { useState, useEffect } from 'react';
import { Question, TestAttempt } from '../../types/cbt.types';

interface AdaptiveQuestionEngineProps {
  questions: Question[];
  currentAnswers: Record<string, any>;
  studentPerformance: {
    averageScore: number;
    strongAreas: string[];
    weakAreas: string[];
    responseTime: number;
  };
  onQuestionRecommendation: (questionIds: string[]) => void;
}

interface QuestionDifficulty {
  easy: Question[];
  medium: Question[];
  hard: Question[];
}

interface AdaptiveSettings {
  enableAdaptiveOrdering: boolean;
  difficultyAdjustmentThreshold: number;
  timeBasedAdjustment: boolean;
  performanceBasedSelection: boolean;
}

export const AdaptiveQuestionEngine: React.FC<AdaptiveQuestionEngineProps> = ({
  questions,
  currentAnswers,
  studentPerformance,
  onQuestionRecommendation
}) => {
  const [adaptiveSettings] = useState<AdaptiveSettings>({
    enableAdaptiveOrdering: true,
    difficultyAdjustmentThreshold: 0.7,
    timeBasedAdjustment: true,
    performanceBasedSelection: true
  });

  const [questionDifficulties, setQuestionDifficulties] = useState<QuestionDifficulty>({
    easy: [],
    medium: [],
    hard: []
  });

  const [recommendedQuestions, setRecommendedQuestions] = useState<string[]>([]);

  useEffect(() => {
    // Categorize questions by difficulty
    const categorized = questions.reduce((acc, question) => {
      const difficulty = question.difficulty || 'medium';
      acc[difficulty as keyof QuestionDifficulty].push(question);
      return acc;
    }, { easy: [], medium: [], hard: [] } as QuestionDifficulty);

    setQuestionDifficulties(categorized);
  }, [questions]);

  useEffect(() => {
    if (adaptiveSettings.enableAdaptiveOrdering) {
      generateAdaptiveQuestionOrder();
    }
  }, [currentAnswers, studentPerformance, questionDifficulties, adaptiveSettings]);

  const calculateCurrentPerformance = (): number => {
    const answeredQuestions = Object.keys(currentAnswers);
    if (answeredQuestions.length === 0) return 0.5; // Default to medium difficulty

    let correctCount = 0;
    answeredQuestions.forEach(questionId => {
      const question = questions.find(q => q.id === questionId);
      if (question && isAnswerCorrect(question, currentAnswers[questionId])) {
        correctCount++;
      }
    });

    return correctCount / answeredQuestions.length;
  };

  const isAnswerCorrect = (question: Question, answer: any): boolean => {
    switch (question.type) {
      case 'multiple_choice':
        const correctOption = question.options?.find(opt => opt.isCorrect);
        return correctOption?.id === answer;
      
      case 'true_false':
        return question.correctAnswer === answer;
      
      case 'numeric':
        const numAnswer = parseFloat(answer);
        const correctNum = parseFloat(question.correctAnswer as string);
        const tolerance = question.tolerance || 0;
        return Math.abs(numAnswer - correctNum) <= tolerance;
      
      case 'fill_blank':
        const acceptableAnswers = question.acceptableAnswers ||
          (typeof question.correctAnswer === 'string' ? [question.correctAnswer] : []);
        return acceptableAnswers.some(acceptable =>
          typeof acceptable === 'string' && typeof answer === 'string' &&
          acceptable.toLowerCase().trim() === answer.toLowerCase().trim()
        );
      
      default:
        return false; // Essay questions need manual grading
    }
  };

  const getDifficultyBasedOnPerformance = (performance: number): 'easy' | 'medium' | 'hard' => {
    if (performance >= 0.8) return 'hard';
    if (performance >= 0.6) return 'medium';
    return 'easy';
  };

  const getTimeBasedDifficultyAdjustment = (): number => {
    // If student is answering quickly and correctly, increase difficulty
    // If student is taking too long, decrease difficulty
    const avgResponseTime = studentPerformance.responseTime;
    const expectedTime = 120; // 2 minutes average per question
    
    if (avgResponseTime < expectedTime * 0.5) return 0.2; // Increase difficulty
    if (avgResponseTime > expectedTime * 1.5) return -0.2; // Decrease difficulty
    return 0; // No adjustment
  };

  const generateAdaptiveQuestionOrder = (): void => {
    const currentPerformance = calculateCurrentPerformance();
    let targetDifficulty = getDifficultyBasedOnPerformance(currentPerformance);
    
    // Apply time-based adjustment
    if (adaptiveSettings.timeBasedAdjustment) {
      const timeAdjustment = getTimeBasedDifficultyAdjustment();
      if (timeAdjustment > 0 && targetDifficulty !== 'hard') {
        targetDifficulty = targetDifficulty === 'easy' ? 'medium' : 'hard';
      } else if (timeAdjustment < 0 && targetDifficulty !== 'easy') {
        targetDifficulty = targetDifficulty === 'hard' ? 'medium' : 'easy';
      }
    }

    // Select questions based on target difficulty and student's weak areas
    const availableQuestions = questionDifficulties[targetDifficulty];
    const unansweredQuestions = availableQuestions.filter(q => !currentAnswers[q.id]);
    
    let selectedQuestions = unansweredQuestions;

    // Prioritize questions in weak areas
    if (adaptiveSettings.performanceBasedSelection && studentPerformance.weakAreas.length > 0) {
      const weakAreaQuestions = unansweredQuestions.filter(q => 
        q.category && studentPerformance.weakAreas.includes(q.category)
      );
      
      if (weakAreaQuestions.length > 0) {
        selectedQuestions = weakAreaQuestions;
      }
    }

    // Shuffle and select top questions
    const shuffled = selectedQuestions.sort(() => Math.random() - 0.5);
    const recommended = shuffled.slice(0, 5).map(q => q.id);
    
    setRecommendedQuestions(recommended);
    onQuestionRecommendation(recommended);
  };

  const getAdaptiveInsights = () => {
    const currentPerformance = calculateCurrentPerformance();
    const answeredCount = Object.keys(currentAnswers).length;
    
    return {
      currentPerformance: Math.round(currentPerformance * 100),
      recommendedDifficulty: getDifficultyBasedOnPerformance(currentPerformance),
      questionsAnswered: answeredCount,
      adaptiveRecommendations: recommendedQuestions.length,
      strongAreas: studentPerformance.strongAreas,
      weakAreas: studentPerformance.weakAreas
    };
  };

  const insights = getAdaptiveInsights();

  return (
    <div className="bg-black/60 backdrop-blur-xl rounded-xl p-4 border border-gray-600/50">
      <h3 className="text-lg font-bold text-white mb-4 font-mono">Adaptive Learning Engine</h3>
      
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-gray-400 text-sm font-mono">Current Performance</span>
          <span className={`font-mono font-bold ${
            insights.currentPerformance >= 80 ? 'text-green-400' :
            insights.currentPerformance >= 60 ? 'text-yellow-400' : 'text-red-400'
          }`}>
            {insights.currentPerformance}%
          </span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-gray-400 text-sm font-mono">Recommended Level</span>
          <span className={`px-2 py-1 rounded text-xs font-mono ${
            insights.recommendedDifficulty === 'hard' ? 'bg-red-900/30 text-red-400' :
            insights.recommendedDifficulty === 'medium' ? 'bg-yellow-900/30 text-yellow-400' :
            'bg-green-900/30 text-green-400'
          }`}>
            {insights.recommendedDifficulty.toUpperCase()}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-gray-400 text-sm font-mono">Questions Answered</span>
          <span className="text-white font-mono">{insights.questionsAnswered}</span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-gray-400 text-sm font-mono">Adaptive Suggestions</span>
          <span className="text-white font-mono">{insights.adaptiveRecommendations}</span>
        </div>

        {insights.strongAreas.length > 0 && (
          <div>
            <span className="text-gray-400 text-xs font-mono block mb-1">Strong Areas</span>
            <div className="flex flex-wrap gap-1">
              {insights.strongAreas.map(area => (
                <span key={area} className="px-2 py-1 bg-green-900/30 text-green-400 rounded text-xs font-mono">
                  {area}
                </span>
              ))}
            </div>
          </div>
        )}

        {insights.weakAreas.length > 0 && (
          <div>
            <span className="text-gray-400 text-xs font-mono block mb-1">Focus Areas</span>
            <div className="flex flex-wrap gap-1">
              {insights.weakAreas.map(area => (
                <span key={area} className="px-2 py-1 bg-red-900/30 text-red-400 rounded text-xs font-mono">
                  {area}
                </span>
              ))}
            </div>
          </div>
        )}

        <div className="pt-2 border-t border-gray-700">
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${
              adaptiveSettings.enableAdaptiveOrdering ? 'bg-green-400' : 'bg-gray-600'
            }`} />
            <span className="text-gray-400 text-xs font-mono">
              Adaptive Ordering {adaptiveSettings.enableAdaptiveOrdering ? 'Enabled' : 'Disabled'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdaptiveQuestionEngine;
