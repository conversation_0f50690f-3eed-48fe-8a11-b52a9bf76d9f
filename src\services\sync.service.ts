import { EventEmitter } from 'events';
import { databaseService } from './database.service';

export type SyncMethod = 'wifi' | 'bluetooth' | 'usb' | 'file';
export type SyncStatus = 'idle' | 'discovering' | 'connecting' | 'syncing' | 'success' | 'error';

export interface SyncDevice {
  id: string;
  name: string;
  type: SyncMethod;
  address: string;
  isOnline: boolean;
  lastSeen: Date;
  capabilities: string[];
}

export interface SyncOperation {
  id: string;
  method: SyncMethod;
  deviceId: string;
  status: SyncStatus;
  progress: number;
  startTime: Date;
  endTime?: Date;
  error?: string;
  dataTypes: string[];
  recordsCount: number;
}

export interface SyncData {
  timestamp: Date;
  source: string;
  type: 'users' | 'tests' | 'attempts' | 'questions' | 'modules';
  data: any[];
  checksum: string;
}

class SyncService extends EventEmitter {
  private operations: Map<string, SyncOperation> = new Map();
  private devices: Map<string, SyncDevice> = new Map();
  private isDiscovering = false;
  private syncQueue: SyncOperation[] = [];

  constructor() {
    super();
    this.initializeSync();
  }

  private async initializeSync() {
    // Initialize sync capabilities
    await this.detectAvailableMethods();
    this.startDeviceDiscovery();
  }

  // Device Discovery
  async detectAvailableMethods(): Promise<SyncMethod[]> {
    const methods: SyncMethod[] = ['file']; // File sync always available

    try {
      // Check WiFi capability
      if (navigator.onLine) {
        methods.push('wifi');
      }

      // Check Bluetooth capability (Web Bluetooth API)
      if ('bluetooth' in navigator) {
        methods.push('bluetooth');
      }

      // Check USB capability (WebUSB API)
      if ('usb' in navigator) {
        methods.push('usb');
      }

      this.emit('methods-detected', methods);
      return methods;
    } catch (error) {
      console.error('Error detecting sync methods:', error);
      return methods;
    }
  }

  async startDeviceDiscovery(): Promise<void> {
    if (this.isDiscovering) return;

    this.isDiscovering = true;
    this.emit('discovery-started');

    try {
      // Discover WiFi devices (local network scan)
      await this.discoverWiFiDevices();

      // Discover Bluetooth devices
      await this.discoverBluetoothDevices();

      // Discover USB devices
      await this.discoverUSBDevices();

    } catch (error) {
      console.error('Device discovery error:', error);
      this.emit('discovery-error', error);
    } finally {
      this.isDiscovering = false;
      this.emit('discovery-completed');
    }
  }

  private async discoverWiFiDevices(): Promise<void> {
    try {
      // Scan local network for CBT devices
      const networkRange = this.getLocalNetworkRange();
      const promises = [];

      for (let i = 1; i <= 254; i++) {
        const ip = `${networkRange}.${i}`;
        promises.push(this.pingDevice(ip));
      }

      const results = await Promise.allSettled(promises);
      
      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          const ip = `${networkRange}.${index + 1}`;
          this.addDevice({
            id: `wifi-${ip}`,
            name: `CBT Device (${ip})`,
            type: 'wifi',
            address: ip,
            isOnline: true,
            lastSeen: new Date(),
            capabilities: ['sync', 'realtime']
          });
        }
      });
    } catch (error) {
      console.error('WiFi discovery error:', error);
    }
  }

  private async discoverBluetoothDevices(): Promise<void> {
    try {
      if (!('bluetooth' in navigator)) return;

      const device = await (navigator as any).bluetooth.requestDevice({
        filters: [{ namePrefix: 'CBT-' }],
        optionalServices: ['battery_service']
      });

      if (device) {
        this.addDevice({
          id: `bluetooth-${device.id}`,
          name: device.name || 'Unknown Bluetooth Device',
          type: 'bluetooth',
          address: device.id,
          isOnline: true,
          lastSeen: new Date(),
          capabilities: ['sync']
        });
      }
    } catch (error) {
      console.error('Bluetooth discovery error:', error);
    }
  }

  private async discoverUSBDevices(): Promise<void> {
    try {
      if (!('usb' in navigator)) return;

      const devices = await (navigator as any).usb.getDevices();
      
      devices.forEach((device: any) => {
        this.addDevice({
          id: `usb-${device.serialNumber || device.productId}`,
          name: device.productName || 'USB Device',
          type: 'usb',
          address: device.serialNumber || device.productId.toString(),
          isOnline: true,
          lastSeen: new Date(),
          capabilities: ['sync', 'file-transfer']
        });
      });
    } catch (error) {
      console.error('USB discovery error:', error);
    }
  }

  // Sync Operations
  async syncWithDevice(deviceId: string, dataTypes: string[] = ['all']): Promise<string> {
    const device = this.devices.get(deviceId);
    if (!device) {
      throw new Error('Device not found');
    }

    const operationId = this.generateId();
    const operation: SyncOperation = {
      id: operationId,
      method: device.type,
      deviceId,
      status: 'connecting',
      progress: 0,
      startTime: new Date(),
      dataTypes,
      recordsCount: 0
    };

    this.operations.set(operationId, operation);
    this.emit('sync-started', operation);

    try {
      // Connect to device
      await this.connectToDevice(device);
      operation.status = 'syncing';
      operation.progress = 10;
      this.emit('sync-progress', operation);

      // Perform sync based on method
      switch (device.type) {
        case 'wifi':
          await this.syncViaWiFi(device, operation);
          break;
        case 'bluetooth':
          await this.syncViaBluetooth(device, operation);
          break;
        case 'usb':
          await this.syncViaUSB(device, operation);
          break;
        case 'file':
          await this.syncViaFile(device, operation);
          break;
      }

      operation.status = 'success';
      operation.progress = 100;
      operation.endTime = new Date();
      this.emit('sync-completed', operation);

    } catch (error) {
      operation.status = 'error';
      operation.error = error instanceof Error ? error.message : 'Unknown error';
      operation.endTime = new Date();
      this.emit('sync-error', operation);
      throw error;
    }

    return operationId;
  }

  private async syncViaWiFi(device: SyncDevice, operation: SyncOperation): Promise<void> {
    // WiFi sync implementation
    const endpoint = `http://${device.address}:8080/api/sync`;
    
    try {
      // Get local data to sync
      const localData = await this.getLocalSyncData(operation.dataTypes);
      operation.progress = 30;
      this.emit('sync-progress', operation);

      // Send data to remote device
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(localData)
      });

      if (!response.ok) {
        throw new Error(`WiFi sync failed: ${response.statusText}`);
      }

      operation.progress = 60;
      this.emit('sync-progress', operation);

      // Receive data from remote device
      const remoteData = await response.json();
      await this.processReceivedData(remoteData);
      
      operation.recordsCount = localData.length + remoteData.length;
      operation.progress = 90;
      this.emit('sync-progress', operation);

    } catch (error) {
      throw new Error(`WiFi sync error: ${error}`);
    }
  }

  private async syncViaBluetooth(device: SyncDevice, operation: SyncOperation): Promise<void> {
    // Bluetooth sync implementation
    try {
      const bluetoothDevice = await (navigator as any).bluetooth.requestDevice({
        filters: [{ name: device.name }]
      });

      const server = await bluetoothDevice.gatt.connect();
      operation.progress = 40;
      this.emit('sync-progress', operation);

      // Implement Bluetooth data transfer protocol
      const localData = await this.getLocalSyncData(operation.dataTypes);
      
      // Send data via Bluetooth characteristics
      // This is a simplified implementation
      operation.recordsCount = localData.length;
      operation.progress = 80;
      this.emit('sync-progress', operation);

    } catch (error) {
      throw new Error(`Bluetooth sync error: ${error}`);
    }
  }

  private async syncViaUSB(device: SyncDevice, operation: SyncOperation): Promise<void> {
    // USB sync implementation
    try {
      const usbDevice = await (navigator as any).usb.requestDevice({
        filters: [{ productName: device.name }]
      });

      await usbDevice.open();
      operation.progress = 40;
      this.emit('sync-progress', operation);

      // Implement USB data transfer
      const localData = await this.getLocalSyncData(operation.dataTypes);
      
      // Transfer data via USB
      operation.recordsCount = localData.length;
      operation.progress = 80;
      this.emit('sync-progress', operation);

    } catch (error) {
      throw new Error(`USB sync error: ${error}`);
    }
  }

  private async syncViaFile(device: SyncDevice, operation: SyncOperation): Promise<void> {
    // File-based sync implementation
    try {
      const localData = await this.getLocalSyncData(operation.dataTypes);
      operation.progress = 50;
      this.emit('sync-progress', operation);

      // Create sync file
      const syncData = {
        timestamp: new Date(),
        source: 'local',
        data: localData,
        checksum: this.calculateChecksum(localData)
      };

      // Download sync file
      const blob = new Blob([JSON.stringify(syncData, null, 2)], { 
        type: 'application/json' 
      });
      
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `cbt-sync-${Date.now()}.json`;
      a.click();
      URL.revokeObjectURL(url);

      operation.recordsCount = localData.length;
      operation.progress = 90;
      this.emit('sync-progress', operation);

    } catch (error) {
      throw new Error(`File sync error: ${error}`);
    }
  }

  // Helper methods
  private async connectToDevice(device: SyncDevice): Promise<void> {
    // Simulate connection delay
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  private async getLocalSyncData(dataTypes: string[]): Promise<any[]> {
    const data: any[] = [];
    
    if (dataTypes.includes('all') || dataTypes.includes('users')) {
      // Get users data from database
      data.push({ type: 'users', records: [] });
    }
    
    if (dataTypes.includes('all') || dataTypes.includes('tests')) {
      // Get tests data from database
      data.push({ type: 'tests', records: [] });
    }

    return data;
  }

  private async processReceivedData(data: any[]): Promise<void> {
    for (const item of data) {
      await databaseService.syncToMongoDB(item.records, item.type);
    }
  }

  private addDevice(device: SyncDevice): void {
    this.devices.set(device.id, device);
    this.emit('device-discovered', device);
  }

  private getLocalNetworkRange(): string {
    // Get local network range (simplified)
    return '192.168.1'; // Default to common range
  }

  private async pingDevice(ip: string): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 1000);

      const response = await fetch(`http://${ip}:8080/ping`, {
        method: 'GET',
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch {
      return false;
    }
  }

  private calculateChecksum(data: any): string {
    // Simple checksum calculation
    return btoa(JSON.stringify(data)).slice(0, 16);
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  // Public API
  getDevices(): SyncDevice[] {
    return Array.from(this.devices.values());
  }

  getOperations(): SyncOperation[] {
    return Array.from(this.operations.values());
  }

  getOperation(id: string): SyncOperation | undefined {
    return this.operations.get(id);
  }
}

export const syncService = new SyncService();
