generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// Note: SQLite doesn't support enums, so we use strings with constraints

// Models
model User {
  id            String   @id @default(cuid())
  email         String   @unique
  username      String   @unique
  passwordHash  String   @map("password_hash")
  firstName     String   @map("first_name")
  lastName      String   @map("last_name")
  role          String @default("student") // admin, instructor, student
  avatar        String?
  institution   String?
  department    String?
  studentId     String?  @map("student_id")
  employeeId    String?  @map("employee_id")
  phone         String?
  bio           String?
  address       String?
  permissions   String?  // JSON as string for SQLite
  isActive      Boolean  @default(true) @map("is_active")
  isVerified    <PERSON>olean  @default(false) @map("is_verified")
  kycStatus     String @default("pending") @map("kyc_status") // pending, verified, rejected
  walletBalance Float    @default(0.00) @map("wallet_balance")
  lastLogin     DateTime? @map("last_login")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")
  
  // Sync metadata
  syncTimestamp DateTime @default(now()) @map("sync_timestamp")
  syncNodeId    String?  @map("sync_node_id")
  syncVersion   Int      @default(1) @map("sync_version")
  deletedAt     DateTime? @map("deleted_at")

  // Relations
  instructorModules   Module[] @relation("InstructorModules")
  enrollments         ModuleEnrollment[]
  createdExams        Exam[]
  examSessions        ExamSession[]
  examAttempts        ExamAttempt[]
  studentAnswers      StudentAnswer[]
  createdQuestions    Question[]
  auditLogs           AuditLog[]

  @@map("users")
}

model Module {
  id               String   @id @default(cuid())
  title            String
  description      String?
  code             String   @unique
  instructorId     String   @map("instructor_id")
  semester         String?
  academicYear     String?  @map("academic_year")
  difficulty       String @default("medium") // easy, medium, hard
  status           String @default("draft") // active, inactive, archived, draft
  estimatedDuration String? @map("estimated_duration")
  maxStudents      Int      @default(0) @map("max_students")
  isActive         Boolean  @default(true) @map("is_active")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")
  
  // Sync metadata
  syncTimestamp    DateTime @default(now()) @map("sync_timestamp")
  syncNodeId       String?  @map("sync_node_id")
  syncVersion      Int      @default(1) @map("sync_version")
  deletedAt        DateTime? @map("deleted_at")

  // Relations
  instructor       User @relation("InstructorModules", fields: [instructorId], references: [id])
  enrollments      ModuleEnrollment[]
  exams            Exam[]

  @@map("modules")
}

model ModuleEnrollment {
  id         String   @id @default(cuid())
  moduleId   String   @map("module_id")
  studentId  String   @map("student_id")
  enrolledAt DateTime @default(now()) @map("enrolled_at")
  status     String @default("enrolled") // enrolled, completed, dropped, suspended
  progress   Float    @default(0.00)
  
  // Relations
  module     Module @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  student    User @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@map("module_enrollments")
  @@unique([moduleId, studentId])
}

model Exam {
  id              String   @id @default(cuid())
  title           String
  description     String?
  instructions    String?
  moduleId        String?  @map("module_id")
  duration        Int      // minutes
  totalPoints     Int      @map("total_points")
  passingScore    Int      @map("passing_score")
  maxAttempts     Int      @default(1) @map("max_attempts")
  shuffleQuestions Boolean @default(false) @map("shuffle_questions")
  shuffleOptions  Boolean  @default(false) @map("shuffle_options")
  showResults     Boolean  @default(true) @map("show_results")
  allowReview     Boolean  @default(false) @map("allow_review")
  isPublished     Boolean  @default(false) @map("is_published")
  startDate       DateTime? @map("start_date")
  endDate         DateTime? @map("end_date")
  difficulty      String @default("medium") // easy, medium, hard
  estimatedTime   Int?     @map("estimated_time") // minutes
  createdBy       String   @map("created_by")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  
  // Sync metadata
  syncTimestamp   DateTime @default(now()) @map("sync_timestamp")
  syncNodeId      String?  @map("sync_node_id")
  syncVersion     Int      @default(1) @map("sync_version")
  deletedAt       DateTime? @map("deleted_at")

  // Relations
  module          Module? @relation(fields: [moduleId], references: [id])
  creator         User @relation(fields: [createdBy], references: [id])
  examQuestions   ExamQuestion[]
  examSessions    ExamSession[]
  examAttempts    ExamAttempt[]
  analytics       ExamAnalytics?

  @@map("exams")
}

model Question {
  id            String      @id @default(cuid())
  type          String // multiple_choice, true_false, essay, fill_blank, matching, ordering, numeric, file_upload
  title         String?
  content       String
  points        Int         @default(1)
  difficulty    String  @default("medium") // easy, medium, hard
  timeLimit     Int?        @map("time_limit") // seconds
  explanation   String?
  maxWords      Int?        @map("max_words") // for essay questions
  rubric        String?     // for essay questions
  workRequired  Boolean     @default(false) @map("work_required") // for numeric
  units         String?     // for numeric questions
  tolerance     Float?      // for numeric questions
  tags          String?     // JSON as string for SQLite
  category      String?
  createdBy     String      @map("created_by")
  createdAt     DateTime    @default(now()) @map("created_at")
  updatedAt     DateTime    @updatedAt @map("updated_at")
  
  // Sync metadata
  syncTimestamp DateTime @default(now()) @map("sync_timestamp")
  syncNodeId    String?  @map("sync_node_id")
  syncVersion   Int      @default(1) @map("sync_version")
  deletedAt     DateTime? @map("deleted_at")

  // Relations
  creator       User @relation(fields: [createdBy], references: [id])
  options       QuestionOption[]
  examQuestions ExamQuestion[]
  mediaFiles    MediaFile[]
  studentAnswers StudentAnswer[]
  analytics     QuestionAnalytics?

  @@map("questions")
}

model QuestionOption {
  id          String   @id @default(cuid())
  questionId  String   @map("question_id")
  text        String
  isCorrect   Boolean  @default(false) @map("is_correct")
  orderIndex  Int      @map("order_index")
  mediaFileId String?  @map("media_file_id")
  createdAt   DateTime @default(now()) @map("created_at")

  // Relations
  question    Question @relation(fields: [questionId], references: [id], onDelete: Cascade)

  @@map("question_options")
}

model ExamQuestion {
  id            String   @id @default(cuid())
  testId        String   @map("test_id")
  questionId    String   @map("question_id")
  orderIndex    Int      @map("order_index")
  pointsOverride Int?    @map("points_override")

  // Relations
  exam          Exam @relation(fields: [testId], references: [id], onDelete: Cascade)
  question      Question @relation(fields: [questionId], references: [id], onDelete: Cascade)

  @@map("exam_questions")
  @@unique([testId, questionId])
}

model MediaFile {
  id           String    @id @default(cuid())
  questionId   String?   @map("question_id")
  type         String // image, video, audio, document
  filename     String
  originalName String    @map("original_name")
  filePath     String    @map("file_path")
  fileSize     Int       @map("file_size")
  mimeType     String    @map("mime_type")
  createdAt    DateTime  @default(now()) @map("created_at")

  // Relations
  question     Question? @relation(fields: [questionId], references: [id], onDelete: Cascade)

  @@map("media_files")
}

model ExamSession {
  id              String   @id @default(cuid())
  testId          String   @map("test_id")
  studentId       String   @map("student_id")
  sessionToken    String   @unique @map("session_token")
  startTime       DateTime @map("start_time")
  endTime         DateTime? @map("end_time")
  expiryTime      DateTime @map("expiry_time")
  isActive        Boolean  @default(true) @map("is_active")
  currentQuestionIndex Int @default(0) @map("current_question_index")
  timeRemaining   Int?     @map("time_remaining") // seconds
  securitySettings String? @map("security_settings") // JSON as string
  ipAddress       String?  @map("ip_address")
  userAgent       String?  @map("user_agent")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Sync metadata
  syncTimestamp   DateTime @default(now()) @map("sync_timestamp")
  syncNodeId      String?  @map("sync_node_id")
  syncVersion     Int      @default(1) @map("sync_version")
  deletedAt       DateTime? @map("deleted_at")

  // Relations
  exam            Exam @relation(fields: [testId], references: [id])
  student         User @relation(fields: [studentId], references: [id])
  studentAnswers  StudentAnswer[]
  examAttempt     ExamAttempt?
  securityEvents  SecurityEvent[]

  @@map("exam_sessions")
}

model StudentAnswer {
  id          String   @id @default(cuid())
  sessionId   String   @map("session_id")
  questionId  String   @map("question_id")
  studentId   String   @map("student_id")
  answer      String?
  answerData  String?  @map("answer_data") // JSON as string for complex answer types
  timeSpent   Int      @map("time_spent") // seconds
  isCorrect   Boolean? @map("is_correct")
  pointsEarned Float?  @map("points_earned")
  isFlagged   Boolean  @default(false) @map("is_flagged")
  submittedAt DateTime @default(now()) @map("submitted_at")

  // Relations
  session     ExamSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  question    Question @relation(fields: [questionId], references: [id])
  student     User @relation(fields: [studentId], references: [id])

  @@map("student_answers")
  @@unique([sessionId, questionId])
}

model ExamAttempt {
  id               String   @id @default(cuid())
  testId           String   @map("test_id")
  studentId        String   @map("student_id")
  sessionId        String   @unique @map("session_id")
  attemptNumber    Int      @map("attempt_number")
  startTime        DateTime @map("start_time")
  endTime          DateTime? @map("end_time")
  duration         Int?     // actual time taken in seconds
  score            Float
  totalQuestions   Int      @map("total_questions")
  correctAnswers   Int      @map("correct_answers")
  incorrectAnswers Int      @map("incorrect_answers")
  skippedQuestions Int      @map("skipped_questions")
  percentage       Float
  passed           Boolean
  status           String @default("in_progress") // in_progress, completed, abandoned, submitted
  ipAddress        String?  @map("ip_address")
  userAgent        String?  @map("user_agent")
  submittedAt      DateTime? @map("submitted_at")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // Sync metadata
  syncTimestamp    DateTime @default(now()) @map("sync_timestamp")
  syncNodeId       String?  @map("sync_node_id")
  syncVersion      Int      @default(1) @map("sync_version")
  deletedAt        DateTime? @map("deleted_at")

  // Relations
  exam             Exam @relation(fields: [testId], references: [id])
  student          User @relation(fields: [studentId], references: [id])
  session          ExamSession @relation(fields: [sessionId], references: [id])

  @@map("exam_attempts")
}

model SecurityEvent {
  id        String   @id @default(cuid())
  sessionId String   @map("session_id")
  type      String // tab_switch, window_blur, copy_attempt, paste_attempt, right_click, key_combination, fullscreen_exit, suspicious_activity
  details   String?
  severity  String @default("medium") // low, medium, high
  timestamp DateTime @default(now())

  // Relations
  session   ExamSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@map("security_events")
}

model NetworkNode {
  id           String   @id @default(cuid())
  nodeId       String   @unique @map("node_id")
  name         String
  ipAddress    String   @map("ip_address")
  port         Int
  role         String @default("slave") // master, slave
  isOnline     Boolean  @default(false) @map("is_online")
  lastSeen     DateTime @default(now()) @map("last_seen")
  version      String
  capabilities String?  // JSON as string
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Sync metadata
  syncTimestamp DateTime @default(now()) @map("sync_timestamp")
  syncNodeId    String?  @map("sync_node_id")
  syncVersion   Int      @default(1) @map("sync_version")
  deletedAt     DateTime? @map("deleted_at")

  @@map("network_nodes")
}

model SyncOperation {
  id          String   @id @default(cuid())
  operationId String   @unique @map("operation_id")
  tableName   String   @map("table_name")
  operation   String @map("operation") // INSERT, UPDATE, DELETE
  recordId    String   @map("record_id")
  data        String   // JSON as string
  nodeId      String   @map("node_id")
  timestamp   DateTime
  applied     Boolean  @default(false)
  appliedAt   DateTime? @map("applied_at")
  createdAt   DateTime @default(now()) @map("created_at")

  @@map("sync_operations")
}

model SyncConflict {
  id          String   @id @default(cuid())
  tableName   String   @map("table_name")
  recordId    String   @map("record_id")
  conflictType String  @map("conflict_type")
  localData   String   @map("local_data") // JSON as string
  remoteData  String   @map("remote_data") // JSON as string
  nodeId      String   @map("node_id")
  resolved    Boolean  @default(false)
  resolution  String?
  resolvedAt  DateTime? @map("resolved_at")
  createdAt   DateTime @default(now()) @map("created_at")

  @@map("sync_conflicts")
}

model ExamAnalytics {
  id               String   @id @default(cuid())
  testId           String   @unique @map("test_id")
  totalAttempts    Int      @default(0) @map("total_attempts")
  averageScore     Float    @default(0.00) @map("average_score")
  passRate         Float    @default(0.00) @map("pass_rate")
  averageDuration  Int      @default(0) @map("average_duration") // seconds
  difficultyRating Float    @default(0.00) @map("difficulty_rating")
  lastCalculated   DateTime @default(now()) @map("last_calculated")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // Relations
  exam             Exam @relation(fields: [testId], references: [id], onDelete: Cascade)

  @@map("exam_analytics")
}

model QuestionAnalytics {
  id                  String   @id @default(cuid())
  questionId          String   @unique @map("question_id")
  correctAnswers      Int      @default(0) @map("correct_answers")
  incorrectAnswers    Int      @default(0) @map("incorrect_answers")
  totalAttempts       Int      @default(0) @map("total_attempts")
  averageTimeSpent    Float    @default(0.00) @map("average_time_spent")
  difficultyRating    Float    @default(0.00) @map("difficulty_rating")
  discriminationIndex Float    @default(0.00) @map("discrimination_index")
  lastCalculated      DateTime @default(now()) @map("last_calculated")
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  // Relations
  question            Question @relation(fields: [questionId], references: [id], onDelete: Cascade)

  @@map("question_analytics")
}

model SystemSetting {
  id          String   @id @default(cuid())
  settingKey  String   @unique @map("setting_key")
  settingValue String? @map("setting_value")
  settingType String   @default("string") @map("setting_type")
  description String?
  isPublic    Boolean  @default(false) @map("is_public")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("system_settings")
}

model AuditLog {
  id         String   @id @default(cuid())
  userId     String?  @map("user_id")
  action     String
  tableName  String?  @map("table_name")
  recordId   String?  @map("record_id")
  oldValues  String?  @map("old_values") // JSON as string
  newValues  String?  @map("new_values") // JSON as string
  ipAddress  String?  @map("ip_address")
  userAgent  String?  @map("user_agent")
  createdAt  DateTime @default(now()) @map("created_at")

  // Relations
  user       User? @relation(fields: [userId], references: [id])

  @@map("audit_log")
}
