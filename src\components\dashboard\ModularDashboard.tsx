import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowLeft, 
  Home, 
  Settings, 
  User, 
  Bell,
  Search,
  Menu,
  X
} from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { ThemeToggle } from '../shared/ThemeToggle';
import { WelcomeScreen } from '../welcome/WelcomeScreen';
import { CBTDashboard } from '../cbt/CBTDashboard';
import { AdminPanel } from '../admin/AdminPanel';
import { EnhancedThreeBackground } from '../shared/EnhancedThreeBackground';

type ModuleType = 'welcome' | 'cbt' | 'lms' | 'attendance' | 'gradebook' | 'library' | 'fee_management' | 'timetable' | 'calendar' | 'admin';

interface ModularDashboardProps {
  user?: {
    id: string;
    name: string;
    email: string;
    role: string;
    avatar?: string;
  };
}

export const ModularDashboard: React.FC<ModularDashboardProps> = ({ user }) => {
  const { theme, getAnimationDuration } = useTheme();
  const [activeModule, setActiveModule] = useState<ModuleType>('welcome');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Close sidebar on module change
  useEffect(() => {
    setSidebarOpen(false);
  }, [activeModule]);

  const handleModuleSelect = (moduleId: string) => {
    setActiveModule(moduleId as ModuleType);
  };

  const handleBackToWelcome = () => {
    setActiveModule('welcome');
  };

  const getModuleTitle = (module: ModuleType): string => {
    const titles = {
      welcome: 'Welcome Dashboard',
      cbt: 'CBT Exam Module',
      admin: 'Admin Panel',
      lms: 'Learning Management System',
      attendance: 'Attendance Management',
      gradebook: 'Gradebook',
      library: 'Library Management',
      fee_management: 'Fee Management',
      timetable: 'Timetable Management',
      calendar: 'Academic Calendar'
    };
    return titles[module] || 'Dashboard';
  };

  const renderModuleContent = () => {
    switch (activeModule) {
      case 'welcome':
        return <WelcomeScreen onModuleSelect={handleModuleSelect} user={user} />;
      case 'cbt':
        return <CBTDashboard />;
      case 'admin':
        return <AdminPanel />;
      case 'lms':
        return <ComingSoonModule title="Learning Management System" />;
      case 'attendance':
        return <ComingSoonModule title="Attendance Management" />;
      case 'gradebook':
        return <ComingSoonModule title="Gradebook" />;
      case 'library':
        return <ComingSoonModule title="Library Management" />;
      case 'fee_management':
        return <ComingSoonModule title="Fee Management" />;
      case 'timetable':
        return <ComingSoonModule title="Timetable Management" />;
      case 'calendar':
        return <ComingSoonModule title="Academic Calendar" />;
      default:
        return <WelcomeScreen onModuleSelect={handleModuleSelect} user={user} />;
    }
  };

  // Don't show header for welcome screen, CBT module, or admin panel (they have their own headers)
  const showHeader = activeModule !== 'welcome' && activeModule !== 'cbt' && activeModule !== 'admin';

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-black">
      {/* Mobile Sidebar Overlay */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Header - Only show for non-welcome modules */}
      {showHeader && (
        <header className="relative z-30 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              {/* Left side */}
              <div className="flex items-center gap-4">
                <button
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                  className="lg:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                >
                  {sidebarOpen ? <X size={20} /> : <Menu size={20} />}
                </button>

                <motion.button
                  onClick={handleBackToWelcome}
                  className="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors font-mono"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <ArrowLeft size={16} />
                  <span className="hidden sm:inline">Back to Dashboard</span>
                </motion.button>

                <div className="hidden lg:block">
                  <h1 className="text-xl font-bold font-mono text-gray-900 dark:text-white">
                    {getModuleTitle(activeModule)}
                  </h1>
                </div>
              </div>

              {/* Center - Search */}
              <div className="hidden md:flex flex-1 max-w-md mx-8">
                <div className="relative w-full">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                  <input
                    type="text"
                    placeholder="Search..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                  />
                </div>
              </div>

              {/* Right side */}
              <div className="flex items-center gap-4">
                <ThemeToggle size="sm" />
                
                <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors relative">
                  <Bell size={20} className="text-gray-600 dark:text-gray-400" />
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                </button>

                {user && (
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                      {user.avatar ? (
                        <img src={user.avatar} alt={user.name} className="w-full h-full rounded-full object-cover" />
                      ) : (
                        <User size={16} className="text-white" />
                      )}
                    </div>
                    <span className="hidden sm:inline font-mono text-sm text-gray-700 dark:text-gray-300">
                      {user.name}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>
      )}

      {/* Main Content */}
      <main className={showHeader ? 'pt-0' : ''}>
        <AnimatePresence mode="wait">
          <motion.div
            key={activeModule}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: getAnimationDuration('normal') }}
            className="min-h-screen"
          >
            {renderModuleContent()}
          </motion.div>
        </AnimatePresence>
      </main>
    </div>
  );
};

// Coming Soon Module Component
const ComingSoonModule: React.FC<{ title: string }> = ({ title }) => {
  const { getAnimationDuration } = useTheme();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-black relative overflow-hidden flex items-center justify-center">
      <EnhancedThreeBackground 
        particleCount={400} 
        shapeCount={8} 
        lineCount={10} 
        animated 
        theme="coming-soon" 
      />
      
      <motion.div
        className="relative z-10 text-center max-w-md mx-auto px-6"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: getAnimationDuration('normal') }}
      >
        <motion.div
          className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6"
          animate={{ 
            scale: [1, 1.1, 1],
            rotate: [0, 5, -5, 0]
          }}
          transition={{ 
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <Settings className="text-white" size={32} />
        </motion.div>

        <h1 className="text-3xl font-bold font-mono text-gray-900 dark:text-white mb-4">
          {title}
        </h1>
        
        <p className="text-lg text-gray-600 dark:text-gray-400 font-mono mb-6">
          This module is currently under development and will be available soon.
        </p>

        <motion.div
          className="inline-flex items-center gap-2 px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-lg font-mono text-sm"
          animate={{ opacity: [1, 0.7, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
          Coming Soon
        </motion.div>
      </motion.div>
    </div>
  );
};
