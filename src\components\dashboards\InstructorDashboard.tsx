import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Users,
  BookOpen,
  BarChart3,
  Plus,
  FileText,
  Clock,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Calendar,
  Settings,
  Download,
  Upload
} from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { NavigationHeader, generateBreadcrumbs } from '../shared/NavigationHeader';

interface InstructorDashboardProps {
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
    avatar?: string;
  };
  onBack: () => void;
  onLogout: () => void;
}

interface ClassData {
  id: string;
  name: string;
  subject: string;
  students: number;
  activeTests: number;
  averageScore: number;
}

interface TestStats {
  totalTests: number;
  activeTests: number;
  totalStudents: number;
  averageScore: number;
  pendingGrading: number;
}

export const InstructorDashboard: React.FC<InstructorDashboardProps> = ({
  user,
  onBack,
  onLogout
}) => {
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState<'overview' | 'tests' | 'students' | 'analytics'>('overview');
  
  const [stats, setStats] = useState<TestStats>({
    totalTests: 24,
    activeTests: 6,
    totalStudents: 156,
    averageScore: 78.5,
    pendingGrading: 12
  });

  const [classes, setClasses] = useState<ClassData[]>([
    {
      id: '1',
      name: 'Advanced Mathematics',
      subject: 'Mathematics',
      students: 45,
      activeTests: 2,
      averageScore: 82.3
    },
    {
      id: '2',
      name: 'Physics 101',
      subject: 'Physics',
      students: 38,
      activeTests: 1,
      averageScore: 75.8
    },
    {
      id: '3',
      name: 'Chemistry Lab',
      subject: 'Chemistry',
      students: 32,
      activeTests: 3,
      averageScore: 79.2
    }
  ]);

  const StatCard = ({ icon: Icon, title, value, subtitle, color, action }: any) => (
    <motion.div
      whileHover={{ scale: 1.02 }}
      className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700"
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400 font-mono">{title}</p>
          <p className={`text-2xl font-bold ${color} font-mono`}>{value}</p>
          {subtitle && <p className="text-xs text-gray-500 dark:text-gray-500 font-mono">{subtitle}</p>}
        </div>
        <div className={`p-3 rounded-lg ${color.replace('text-', 'bg-').replace('600', '100')} dark:bg-opacity-20`}>
          <Icon size={24} className={color} />
        </div>
      </div>
      {action && (
        <div className="mt-4">
          <button className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 font-mono">
            {action}
          </button>
        </div>
      )}
    </motion.div>
  );

  const breadcrumbs = generateBreadcrumbs([activeTab], user.role);

  const createTestAction = (
    <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors font-mono flex items-center space-x-2">
      <Plus size={16} />
      <span>Create Test</span>
    </button>
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Navigation Header */}
      <NavigationHeader
        title="Instructor Dashboard"
        breadcrumbs={breadcrumbs}
        user={user}
        onBack={onBack}
        onLogout={onLogout}
        notificationCount={stats.pendingGrading}
        actions={createTestAction}
      />

      {/* Navigation Tabs */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview', icon: BarChart3 },
              { id: 'tests', label: 'Tests', icon: BookOpen },
              { id: 'students', label: 'Students', icon: Users },
              { id: 'analytics', label: 'Analytics', icon: TrendingUp }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm font-mono transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
              >
                <tab.icon size={16} />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-8"
          >
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              <StatCard
                icon={BookOpen}
                title="Total Tests"
                value={stats.totalTests}
                color="text-blue-600"
              />
              <StatCard
                icon={Clock}
                title="Active Tests"
                value={stats.activeTests}
                color="text-green-600"
              />
              <StatCard
                icon={Users}
                title="Total Students"
                value={stats.totalStudents}
                color="text-purple-600"
              />
              <StatCard
                icon={TrendingUp}
                title="Average Score"
                value={`${stats.averageScore}%`}
                color="text-orange-600"
              />
              <StatCard
                icon={AlertTriangle}
                title="Pending Grading"
                value={stats.pendingGrading}
                color="text-red-600"
                action="Review now"
              />
            </div>

            {/* Classes Overview */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white font-mono">My Classes</h2>
                  <button className="text-blue-600 hover:text-blue-800 dark:text-blue-400 font-mono text-sm">
                    View All
                  </button>
                </div>
              </div>
              <div className="p-6">
                <div className="grid gap-6">
                  {classes.map((classItem) => (
                    <div key={classItem.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white font-mono">{classItem.name}</h3>
                          <p className="text-gray-600 dark:text-gray-400 font-mono">{classItem.subject}</p>
                        </div>
                        <div className="flex space-x-2">
                          <button className="px-3 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-lg text-sm font-mono">
                            Manage
                          </button>
                          <button className="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-lg text-sm font-mono">
                            Reports
                          </button>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-3 gap-4 mt-4 text-sm">
                        <div>
                          <p className="text-gray-500 dark:text-gray-400 font-mono">Students</p>
                          <p className="font-medium text-gray-900 dark:text-white font-mono">{classItem.students}</p>
                        </div>
                        <div>
                          <p className="text-gray-500 dark:text-gray-400 font-mono">Active Tests</p>
                          <p className="font-medium text-gray-900 dark:text-white font-mono">{classItem.activeTests}</p>
                        </div>
                        <div>
                          <p className="text-gray-500 dark:text-gray-400 font-mono">Avg. Score</p>
                          <p className="font-medium text-gray-900 dark:text-white font-mono">{classItem.averageScore}%</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <motion.button
                whileHover={{ scale: 1.02 }}
                className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700 text-left"
              >
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                    <Plus size={24} className="text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white font-mono">Create New Test</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 font-mono">Design and publish a new assessment</p>
                  </div>
                </div>
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.02 }}
                className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700 text-left"
              >
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
                    <Upload size={24} className="text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white font-mono">Import Questions</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 font-mono">Upload questions from Excel or XML</p>
                  </div>
                </div>
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.02 }}
                className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700 text-left"
              >
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                    <Download size={24} className="text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white font-mono">Export Reports</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 font-mono">Download student performance data</p>
                  </div>
                </div>
              </motion.button>
            </div>
          </motion.div>
        )}

        {/* Add other tab content here */}
      </main>
    </div>
  );
};
