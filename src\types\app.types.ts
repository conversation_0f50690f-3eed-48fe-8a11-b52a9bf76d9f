export interface AppInfo {
  name: string;
  version: string;
  platform: string;
}

export interface WindowControls {
  minimize: () => void;
  maximize: () => void;
  close: () => void;
}

export interface ThreeJSScene {
  scene: THREE.Scene;
  camera: THREE.Camera;
  renderer: THREE.WebGLRenderer;
  controls?: any;
}

export interface NavigationItem {
  id: string;
  label: string;
  icon: string;
  path: string;
  children?: NavigationItem[];
}

export interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalRevenue: number;
  growthRate: number;
}
