import React, { createContext, useContext, useReducer, useEffect } from 'react';

// Theme types
export type ThemeMode = 'light' | 'dark';
export type AnimationSpeed = 'slow' | 'normal' | 'fast';

export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  success: string;
  warning: string;
  error: string;
  info: string;
}

export interface ThemeConfig {
  mode: ThemeMode;
  animationSpeed: AnimationSpeed;
  fontSize: 'small' | 'medium' | 'large';
  reducedMotion: boolean;
  highContrast: boolean;
  colors: ThemeColors;
}

// Light theme colors
const lightColors: ThemeColors = {
  primary: '#2563eb',
  secondary: '#64748b',
  accent: '#0ea5e9',
  background: '#ffffff',
  surface: '#f8fafc',
  text: '#1e293b',
  textSecondary: '#64748b',
  border: '#e2e8f0',
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6'
};

// Dark theme colors
const darkColors: ThemeColors = {
  primary: '#3b82f6',
  secondary: '#94a3b8',
  accent: '#06b6d4',
  background: '#0f172a',
  surface: '#1e293b',
  text: '#f1f5f9',
  textSecondary: '#94a3b8',
  border: '#334155',
  success: '#22c55e',
  warning: '#fbbf24',
  error: '#f87171',
  info: '#60a5fa'
};

// Initial theme state
const initialTheme: ThemeConfig = {
  mode: 'light', // Default to light mode as requested
  animationSpeed: 'normal',
  fontSize: 'medium',
  reducedMotion: false,
  highContrast: false,
  colors: lightColors
};

// Theme actions
type ThemeAction =
  | { type: 'SET_MODE'; payload: ThemeMode }
  | { type: 'SET_ANIMATION_SPEED'; payload: AnimationSpeed }
  | { type: 'SET_FONT_SIZE'; payload: 'small' | 'medium' | 'large' }
  | { type: 'TOGGLE_REDUCED_MOTION' }
  | { type: 'TOGGLE_HIGH_CONTRAST' }
  | { type: 'LOAD_THEME'; payload: ThemeConfig };

// Theme reducer
const themeReducer = (state: ThemeConfig, action: ThemeAction): ThemeConfig => {
  switch (action.type) {
    case 'SET_MODE':
      return {
        ...state,
        mode: action.payload,
        colors: action.payload === 'light' ? lightColors : darkColors
      };
    case 'SET_ANIMATION_SPEED':
      return { ...state, animationSpeed: action.payload };
    case 'SET_FONT_SIZE':
      return { ...state, fontSize: action.payload };
    case 'TOGGLE_REDUCED_MOTION':
      return { ...state, reducedMotion: !state.reducedMotion };
    case 'TOGGLE_HIGH_CONTRAST':
      return { ...state, highContrast: !state.highContrast };
    case 'LOAD_THEME':
      return action.payload;
    default:
      return state;
  }
};

// Theme context
interface ThemeContextType {
  theme: ThemeConfig;
  toggleTheme: () => void;
  setThemeMode: (mode: ThemeMode) => void;
  setAnimationSpeed: (speed: AnimationSpeed) => void;
  setFontSize: (size: 'small' | 'medium' | 'large') => void;
  toggleReducedMotion: () => void;
  toggleHighContrast: () => void;
  getAnimationDuration: (type: 'fast' | 'normal' | 'slow') => number;
  getCSSVariables: () => Record<string, string>;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Theme provider
export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [theme, dispatch] = useReducer(themeReducer, initialTheme);

  // Load theme from localStorage on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem('cbt-theme');
    if (savedTheme) {
      try {
        const parsedTheme = JSON.parse(savedTheme);
        dispatch({ type: 'LOAD_THEME', payload: parsedTheme });
      } catch (error) {
        console.error('Error loading saved theme:', error);
      }
    }
  }, []);

  // Save theme to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('cbt-theme', JSON.stringify(theme));
    
    // Apply CSS variables to document root
    const root = document.documentElement;
    const cssVars = getCSSVariables();
    
    Object.entries(cssVars).forEach(([key, value]) => {
      root.style.setProperty(key, value);
    });
    
    // Apply theme class to body
    document.body.className = `theme-${theme.mode} font-${theme.fontSize} ${
      theme.reducedMotion ? 'reduced-motion' : ''
    } ${theme.highContrast ? 'high-contrast' : ''}`.trim();
    
  }, [theme]);

  const toggleTheme = () => {
    dispatch({ type: 'SET_MODE', payload: theme.mode === 'light' ? 'dark' : 'light' });
  };

  const setThemeMode = (mode: ThemeMode) => {
    dispatch({ type: 'SET_MODE', payload: mode });
  };

  const setAnimationSpeed = (speed: AnimationSpeed) => {
    dispatch({ type: 'SET_ANIMATION_SPEED', payload: speed });
  };

  const setFontSize = (size: 'small' | 'medium' | 'large') => {
    dispatch({ type: 'SET_FONT_SIZE', payload: size });
  };

  const toggleReducedMotion = () => {
    dispatch({ type: 'TOGGLE_REDUCED_MOTION' });
  };

  const toggleHighContrast = () => {
    dispatch({ type: 'TOGGLE_HIGH_CONTRAST' });
  };

  const getAnimationDuration = (type: 'fast' | 'normal' | 'slow'): number => {
    if (theme.reducedMotion) return 0;
    
    const speeds = {
      fast: { slow: 0.1, normal: 0.15, fast: 0.2 },
      normal: { slow: 0.2, normal: 0.3, fast: 0.4 },
      slow: { slow: 0.4, normal: 0.6, fast: 0.8 }
    };
    
    return speeds[type][theme.animationSpeed];
  };

  const getCSSVariables = (): Record<string, string> => {
    const { colors } = theme;
    
    return {
      '--color-primary': colors.primary,
      '--color-secondary': colors.secondary,
      '--color-accent': colors.accent,
      '--color-background': colors.background,
      '--color-surface': colors.surface,
      '--color-text': colors.text,
      '--color-text-secondary': colors.textSecondary,
      '--color-border': colors.border,
      '--color-success': colors.success,
      '--color-warning': colors.warning,
      '--color-error': colors.error,
      '--color-info': colors.info,
      '--animation-duration-fast': `${getAnimationDuration('fast')}s`,
      '--animation-duration-normal': `${getAnimationDuration('normal')}s`,
      '--animation-duration-slow': `${getAnimationDuration('slow')}s`,
      '--font-size-base': theme.fontSize === 'small' ? '14px' : theme.fontSize === 'large' ? '18px' : '16px'
    };
  };

  const contextValue: ThemeContextType = {
    theme,
    toggleTheme,
    setThemeMode,
    setAnimationSpeed,
    setFontSize,
    toggleReducedMotion,
    toggleHighContrast,
    getAnimationDuration,
    getCSSVariables
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use theme
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
