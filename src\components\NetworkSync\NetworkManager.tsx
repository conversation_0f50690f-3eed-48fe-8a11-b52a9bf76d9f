'use client';

import React, { useState, useEffect } from 'react';
import { Wifi, WifiOff, Server, Users, Settings, Refresh<PERSON>w, AlertTriangle, CheckCircle } from 'lucide-react';

interface NetworkNode {
  id: string;
  name: string;
  ip: string;
  port: number;
  role: 'master' | 'slave';
  lastSeen: Date;
  version: string;
  capabilities: string[];
  isCurrent?: boolean;
}

interface NetworkStatus {
  nodeId: string;
  isOnline: boolean;
  isMaster: boolean;
  masterNode: NetworkNode | null;
  connectedNodes: number;
  queueLength: number;
  lastSyncTime: Date | null;
}

export const NetworkManager: React.FC = () => {
  const [nodes, setNodes] = useState<NetworkNode[]>([]);
  const [currentNode, setCurrentNode] = useState<NetworkNode | null>(null);
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedRole, setSelectedRole] = useState<'master' | 'slave'>('slave');

  useEffect(() => {
    fetchNetworkData();
    const interval = setInterval(fetchNetworkData, 5000); // Update every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchNetworkData = async () => {
    try {
      const [nodesResponse, statusResponse] = await Promise.all([
        fetch('/api/sync/nodes'),
        fetch('/api/sync/status')
      ]);

      if (nodesResponse.ok && statusResponse.ok) {
        const nodesData = await nodesResponse.json();
        const statusData = await statusResponse.json();

        setNodes(nodesData.data.discoveredNodes);
        setCurrentNode(nodesData.data.currentNode);
        setNetworkStatus(statusData.data);
        setError(null);
      } else {
        throw new Error('Failed to fetch network data');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const setNodeRole = async (role: 'master' | 'slave') => {
    try {
      const response = await fetch('/api/sync/nodes/role', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ role }),
      });

      if (response.ok) {
        setSelectedRole(role);
        await fetchNetworkData();
      } else {
        throw new Error('Failed to set node role');
      }
    } catch (err: any) {
      setError(err.message);
    }
  };

  const triggerSync = async (type: 'full' | 'incremental') => {
    try {
      const response = await fetch(`/api/sync/trigger/${type}`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error(`Failed to trigger ${type} sync`);
      }

      await fetchNetworkData();
    } catch (err: any) {
      setError(err.message);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin mr-2" />
        <span>Loading network information...</span>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold flex items-center">
          <Wifi className="w-6 h-6 mr-2" />
          Network Sync Manager
        </h2>
        <button
          onClick={fetchNetworkData}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <div className="flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2" />
            {error}
          </div>
        </div>
      )}

      {/* Current Node Status */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <Server className="w-5 h-5 mr-2" />
          Current Node
        </h3>
        
        {currentNode && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm text-gray-600">Name</label>
              <p className="font-medium">{currentNode.name}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">IP Address</label>
              <p className="font-medium">{currentNode.ip}:{currentNode.port}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">Role</label>
              <p className="font-medium capitalize">{currentNode.role}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">Status</label>
              <div className="flex items-center">
                {networkStatus?.isOnline ? (
                  <CheckCircle className="w-4 h-4 text-green-500 mr-1" />
                ) : (
                  <WifiOff className="w-4 h-4 text-red-500 mr-1" />
                )}
                <span className={networkStatus?.isOnline ? 'text-green-600' : 'text-red-600'}>
                  {networkStatus?.isOnline ? 'Online' : 'Offline'}
                </span>
              </div>
            </div>
          </div>
        )}

        <div className="mt-6 flex flex-wrap gap-4">
          <div>
            <label className="block text-sm text-gray-600 mb-2">Node Role</label>
            <select
              value={selectedRole}
              onChange={(e) => setSelectedRole(e.target.value as 'master' | 'slave')}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="slave">Slave Node</option>
              <option value="master">Master Node</option>
            </select>
          </div>
          <div className="flex items-end">
            <button
              onClick={() => setNodeRole(selectedRole)}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              Set Role
            </button>
          </div>
        </div>
      </div>

      {/* Network Statistics */}
      {networkStatus && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Network Statistics</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{networkStatus.connectedNodes + 1}</div>
              <div className="text-sm text-gray-600">Total Nodes</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{networkStatus.queueLength}</div>
              <div className="text-sm text-gray-600">Queue Length</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {networkStatus.isMaster ? 'Master' : 'Slave'}
              </div>
              <div className="text-sm text-gray-600">Current Role</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {networkStatus.lastSyncTime ? 'Recent' : 'Never'}
              </div>
              <div className="text-sm text-gray-600">Last Sync</div>
            </div>
          </div>
        </div>
      )}

      {/* Discovered Nodes */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <Users className="w-5 h-5 mr-2" />
          Discovered Nodes ({nodes.length})
        </h3>
        
        {nodes.length === 0 ? (
          <p className="text-gray-500 text-center py-8">No other nodes discovered on the network</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full table-auto">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">Name</th>
                  <th className="text-left py-2">IP:Port</th>
                  <th className="text-left py-2">Role</th>
                  <th className="text-left py-2">Version</th>
                  <th className="text-left py-2">Last Seen</th>
                  <th className="text-left py-2">Status</th>
                </tr>
              </thead>
              <tbody>
                {nodes.map((node) => (
                  <tr key={node.id} className="border-b">
                    <td className="py-2">{node.name}</td>
                    <td className="py-2">{node.ip}:{node.port}</td>
                    <td className="py-2">
                      <span className={`px-2 py-1 rounded text-xs ${
                        node.role === 'master' 
                          ? 'bg-purple-100 text-purple-800' 
                          : 'bg-blue-100 text-blue-800'
                      }`}>
                        {node.role}
                      </span>
                    </td>
                    <td className="py-2">{node.version}</td>
                    <td className="py-2">
                      {new Date(node.lastSeen).toLocaleTimeString()}
                    </td>
                    <td className="py-2">
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                        <span className="text-green-600">Online</span>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Sync Controls */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <Settings className="w-5 h-5 mr-2" />
          Sync Controls
        </h3>
        
        <div className="flex flex-wrap gap-4">
          <button
            onClick={() => triggerSync('incremental')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Trigger Incremental Sync
          </button>
          <button
            onClick={() => triggerSync('full')}
            className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
          >
            Trigger Full Sync
          </button>
        </div>
        
        <div className="mt-4 text-sm text-gray-600">
          <p>• Incremental sync: Syncs only recent changes</p>
          <p>• Full sync: Syncs all data (use sparingly)</p>
        </div>
      </div>
    </div>
  );
};
