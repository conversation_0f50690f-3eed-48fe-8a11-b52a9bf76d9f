'use client';

import React, { useState } from 'react';
import { NetworkStatus } from '../../components/NetworkSync/NetworkStatus';
import { NetworkManager } from '../../components/NetworkSync/NetworkManager';

export default function TestSystemPage() {
  const [activeTab, setActiveTab] = useState('status');
  const [apiTest, setApiTest] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testBackendConnection = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/auth/verify-token', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      const data = await response.json();
      setApiTest({
        status: response.status,
        success: response.ok,
        data: data,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      setApiTest({
        status: 0,
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    } finally {
      setLoading(false);
    }
  };

  const testNetworkSync = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/network-sync/status');
      const data = await response.json();
      setApiTest({
        status: response.status,
        success: response.ok,
        data: data,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      setApiTest({
        status: 0,
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">CBT System Test Dashboard</h1>
        
        {/* Tab Navigation */}
        <div className="mb-6">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('status')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'status'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Network Status
            </button>
            <button
              onClick={() => setActiveTab('manager')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'manager'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Network Manager
            </button>
            <button
              onClick={() => setActiveTab('api')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'api'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              API Tests
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'status' && (
          <div className="space-y-6">
            <NetworkStatus />
            
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">System Information</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="font-medium">Frontend:</span>
                  <div>Next.js + Electron</div>
                </div>
                <div>
                  <span className="font-medium">Backend:</span>
                  <div>Node.js + Express</div>
                </div>
                <div>
                  <span className="font-medium">Database:</span>
                  <div>MySQL</div>
                </div>
                <div>
                  <span className="font-medium">Sync:</span>
                  <div>WebSocket + UDP</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'manager' && (
          <NetworkManager />
        )}

        {activeTab === 'api' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">API Connection Tests</h3>
              
              <div className="space-y-4">
                <div className="flex space-x-4">
                  <button
                    onClick={testBackendConnection}
                    disabled={loading}
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                  >
                    {loading ? 'Testing...' : 'Test Backend Connection'}
                  </button>
                  
                  <button
                    onClick={testNetworkSync}
                    disabled={loading}
                    className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
                  >
                    {loading ? 'Testing...' : 'Test Network Sync'}
                  </button>
                </div>

                {apiTest && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-2">Test Results:</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex">
                        <span className="font-medium w-20">Status:</span>
                        <span className={apiTest.success ? 'text-green-600' : 'text-red-600'}>
                          {apiTest.status} - {apiTest.success ? 'Success' : 'Failed'}
                        </span>
                      </div>
                      <div className="flex">
                        <span className="font-medium w-20">Time:</span>
                        <span>{new Date(apiTest.timestamp).toLocaleString()}</span>
                      </div>
                      {apiTest.error && (
                        <div className="flex">
                          <span className="font-medium w-20">Error:</span>
                          <span className="text-red-600">{apiTest.error}</span>
                        </div>
                      )}
                      {apiTest.data && (
                        <div>
                          <span className="font-medium">Response:</span>
                          <pre className="mt-2 p-2 bg-white rounded text-xs overflow-auto">
                            {JSON.stringify(apiTest.data, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50">
                  <div className="font-medium">Register User</div>
                  <div className="text-sm text-gray-600">Test user registration</div>
                </button>
                <button className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50">
                  <div className="font-medium">Login Test</div>
                  <div className="text-sm text-gray-600">Test authentication</div>
                </button>
                <button className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50">
                  <div className="font-medium">Create Test</div>
                  <div className="text-sm text-gray-600">Test creation flow</div>
                </button>
                <button className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50">
                  <div className="font-medium">Sync Data</div>
                  <div className="text-sm text-gray-600">Test sync operations</div>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
