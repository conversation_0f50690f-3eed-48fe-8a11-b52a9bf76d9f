import { getMySQLConnection, getMongoConnection } from '../config/database.config';
import { CBTUser, Test, TestAttempt, Question } from '../types/cbt.types';
import { Module } from '../types/module.types';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcryptjs';

export class DatabaseService {
  // User operations
  async createUser(userData: Omit<CBTUser, 'id' | 'createdAt' | 'updatedAt'> & { password: string }): Promise<CBTUser> {
    const pool = await getMySQLConnection();
    const userId = uuidv4();
    const passwordHash = await bcrypt.hash(userData.password, 12);
    
    await pool.execute(`
      INSERT INTO users (
        id, email, password_hash, name, role, avatar, institution,
        department, student_id, employee_id, permissions, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      userId,
      userData.email,
      passwordHash,
      userData.name,
      userData.role,
      userData.avatar || null,
      userData.institution || null,
      userData.department || null,
      userData.studentId || null,
      userData.employeeId || null,
      JSON.stringify(userData.permissions),
      userData.isActive
    ]);

    const user = await this.getUserById(userId);
    if (!user) {
      throw new Error('Failed to create user');
    }
    return user;
  }

  async getUserById(id: string): Promise<CBTUser | null> {
    const pool = await getMySQLConnection();
    const [rows] = await pool.execute(
      'SELECT * FROM users WHERE id = ?',
      [id]
    );
    
    const users = rows as any[];
    if (users.length === 0) return null;
    
    return this.mapUserFromDB(users[0]);
  }

  async getUserByEmail(email: string): Promise<CBTUser | null> {
    const pool = await getMySQLConnection();
    const [rows] = await pool.execute(
      'SELECT * FROM users WHERE email = ?',
      [email]
    );
    
    const users = rows as any[];
    if (users.length === 0) return null;
    
    return this.mapUserFromDB(users[0]);
  }

  async updateUser(id: string, updates: Partial<CBTUser>): Promise<CBTUser | null> {
    const pool = await getMySQLConnection();
    const setClause = [];
    const values = [];
    
    if (updates.name) {
      setClause.push('name = ?');
      values.push(updates.name);
    }
    if (updates.avatar) {
      setClause.push('avatar = ?');
      values.push(updates.avatar);
    }
    if (updates.institution) {
      setClause.push('institution = ?');
      values.push(updates.institution);
    }
    if (updates.department) {
      setClause.push('department = ?');
      values.push(updates.department);
    }
    if (updates.permissions) {
      setClause.push('permissions = ?');
      values.push(JSON.stringify(updates.permissions));
    }
    if (typeof updates.isActive === 'boolean') {
      setClause.push('is_active = ?');
      values.push(updates.isActive);
    }
    
    if (setClause.length === 0) return this.getUserById(id);
    
    values.push(id);
    await pool.execute(
      `UPDATE users SET ${setClause.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
      values
    );
    
    return this.getUserById(id);
  }

  async updateLastLogin(id: string): Promise<void> {
    const pool = await getMySQLConnection();
    await pool.execute(
      'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?',
      [id]
    );
  }

  async verifyPassword(email: string, password: string): Promise<CBTUser | null> {
    const pool = await getMySQLConnection();
    const [rows] = await pool.execute(
      'SELECT * FROM users WHERE email = ? AND is_active = TRUE',
      [email]
    );
    
    const users = rows as any[];
    if (users.length === 0) return null;
    
    const user = users[0];
    const isValid = await bcrypt.compare(password, user.password_hash);
    
    if (!isValid) return null;
    
    return this.mapUserFromDB(user);
  }

  // Module operations
  async createModule(moduleData: Omit<Module, 'id' | 'createdAt' | 'updatedAt'>): Promise<Module> {
    const pool = await getMySQLConnection();
    const moduleId = uuidv4();
    
    await pool.execute(`
      INSERT INTO modules (id, name, description, type, icon, is_active, settings, created_by)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      moduleId,
      moduleData.name,
      moduleData.description || null,
      moduleData.type,
      moduleData.icon || null,
      moduleData.isActive ?? true,
      JSON.stringify(moduleData.settings || {}),
      moduleData.createdBy || null
    ]);

    const module = await this.getModuleById(moduleId);
    if (!module) {
      throw new Error('Failed to create module');
    }
    return module;
  }

  async getModuleById(id: string): Promise<Module | null> {
    const pool = await getMySQLConnection();
    const [rows] = await pool.execute(
      'SELECT * FROM modules WHERE id = ?',
      [id]
    );
    
    const modules = rows as any[];
    if (modules.length === 0) return null;
    
    return this.mapModuleFromDB(modules[0]);
  }

  async getModulesByType(type: string): Promise<Module[]> {
    const pool = await getMySQLConnection();
    const [rows] = await pool.execute(
      'SELECT * FROM modules WHERE type = ? AND is_active = TRUE ORDER BY name',
      [type]
    );
    
    const modules = rows as any[];
    return modules.map(module => this.mapModuleFromDB(module));
  }

  async getAllModules(): Promise<Module[]> {
    const pool = await getMySQLConnection();
    const [rows] = await pool.execute(
      'SELECT * FROM modules WHERE is_active = TRUE ORDER BY type, name'
    );

    const modules = rows as any[];
    return modules.map(module => this.mapModuleFromDB(module));
  }

  async updateModule(id: string, updates: Partial<Module>): Promise<Module | null> {
    const pool = await getMySQLConnection();
    const setClause = [];
    const values = [];

    if (updates.name) {
      setClause.push('name = ?');
      values.push(updates.name);
    }
    if (updates.description) {
      setClause.push('description = ?');
      values.push(updates.description);
    }
    if (updates.settings) {
      setClause.push('settings = ?');
      values.push(JSON.stringify(updates.settings));
    }
    if (typeof updates.isActive === 'boolean') {
      setClause.push('is_active = ?');
      values.push(updates.isActive);
    }

    if (setClause.length === 0) return this.getModuleById(id);

    values.push(id);
    await pool.execute(
      `UPDATE modules SET ${setClause.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
      values
    );

    return this.getModuleById(id);
  }

  async createTest(testData: Omit<Test, 'id' | 'createdAt' | 'updatedAt'>): Promise<Test> {
    // Mock implementation - in real app, this would save to database
    const test: Test = {
      ...testData,
      id: Math.random().toString(36).substring(2, 11),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    console.log('Creating test:', test);
    return test;
  }

  // Sync operations
  async syncToMongoDB(data: any, collection: string): Promise<void> {
    try {
      const db = await getMongoConnection();
      const mongoCollection = db.collection(collection);
      
      if (Array.isArray(data)) {
        await mongoCollection.insertMany(data);
      } else {
        await mongoCollection.insertOne({
          ...data,
          syncedAt: new Date(),
          source: 'mysql'
        });
      }
    } catch (error) {
      console.error(`Error syncing to MongoDB collection ${collection}:`, error);
      throw error;
    }
  }

  async syncFromMongoDB(collection: string, lastSyncTime?: Date): Promise<any[]> {
    try {
      const db = await getMongoConnection();
      const mongoCollection = db.collection(collection);
      
      const query = lastSyncTime 
        ? { syncedAt: { $gt: lastSyncTime } }
        : {};
      
      const documents = await mongoCollection.find(query).toArray();
      return documents;
    } catch (error) {
      console.error(`Error syncing from MongoDB collection ${collection}:`, error);
      throw error;
    }
  }

  // Helper methods
  private mapUserFromDB(dbUser: any): CBTUser {
    return {
      id: dbUser.id,
      email: dbUser.email,
      name: dbUser.name,
      role: dbUser.role,
      avatar: dbUser.avatar,
      institution: dbUser.institution,
      department: dbUser.department,
      studentId: dbUser.student_id,
      employeeId: dbUser.employee_id,
      permissions: JSON.parse(dbUser.permissions || '[]'),
      isActive: dbUser.is_active,
      lastLogin: dbUser.last_login,
      createdAt: dbUser.created_at,
      updatedAt: dbUser.updated_at
    };
  }

  private mapModuleFromDB(dbModule: any): Module {
    return {
      id: dbModule.id,
      name: dbModule.name,
      description: dbModule.description,
      type: dbModule.type,
      icon: dbModule.icon,
      version: dbModule.version || '1.0.0',
      isActive: dbModule.is_active,
      isInstalled: true,
      permissions: JSON.parse(dbModule.permissions || '[]'),
      settings: JSON.parse(dbModule.settings || '{}'),
      dependencies: JSON.parse(dbModule.dependencies || '[]'),
      createdBy: dbModule.created_by,
      createdAt: dbModule.created_at,
      updatedAt: dbModule.updated_at
    };
  }
}

export const databaseService = new DatabaseService();
