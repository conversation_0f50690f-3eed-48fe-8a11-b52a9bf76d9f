import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '../../../lib/auth';
import { connectDatabase } from '../../../lib/database';
import prisma from '../../../lib/database';

// GET /api/tests - Get exams (keeping endpoint name for compatibility)
export async function GET(request: NextRequest) {
  try {
    await connectDatabase();

    const authResult = await requireAuth(request);
    if ('error' in authResult) {
      return NextResponse.json(
        { success: false, message: authResult.error },
        { status: authResult.status }
      );
    }

    const { user } = authResult;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const moduleId = searchParams.get('moduleId');
    const published = searchParams.get('published');

    const skip = (page - 1) * limit;

    // Build where clause based on user role
    const where: any = {};
    
    if (user.role === 'student') {
      // Students can only see published tests
      where.isPublished = true;
      if (moduleId) {
        // Check if student is enrolled in the module
        const enrollment = await prisma.moduleEnrollment.findFirst({
          where: {
            moduleId,
            studentId: user.id,
            status: 'enrolled'
          }
        });
        if (!enrollment) {
          return NextResponse.json({
            success: true,
            message: 'Tests retrieved successfully',
            data: { tests: [], pagination: { page, limit, total: 0, pages: 0 } }
          });
        }
        where.moduleId = moduleId;
      }
    } else if (user.role === 'instructor') {
      // Instructors can see their own tests
      where.createdBy = user.id;
      if (moduleId) where.moduleId = moduleId;
      if (published !== null) where.isPublished = published === 'true';
    } else {
      // Admins can see all tests
      if (moduleId) where.moduleId = moduleId;
      if (published !== null) where.isPublished = published === 'true';
    }

    const [exams, total] = await Promise.all([
      prisma.exam.findMany({
        where,
        skip,
        take: limit,
        include: {
          module: {
            select: {
              id: true,
              title: true,
              code: true
            }
          },
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              username: true
            }
          },
          testQuestions: {
            select: {
              id: true,
              orderIndex: true,
              pointsOverride: true,
              question: {
                select: {
                  id: true,
                  type: true,
                  points: true,
                  difficulty: true
                }
              }
            },
            orderBy: { orderIndex: 'asc' }
          },
          _count: {
            select: {
              testAttempts: true,
              testSessions: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.exam.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      message: 'Exams retrieved successfully',
      data: {
        exams,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get tests error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/tests - Create new test (instructor/admin only)
export async function POST(request: NextRequest) {
  try {
    await connectDatabase();

    const authResult = await requireAuth(request, ['instructor', 'admin']);
    if ('error' in authResult) {
      return NextResponse.json(
        { success: false, message: authResult.error },
        { status: authResult.status }
      );
    }

    const { user } = authResult;
    const body = await request.json();
    const {
      title,
      description,
      instructions,
      moduleId,
      duration,
      totalPoints,
      passingScore,
      maxAttempts,
      shuffleQuestions,
      shuffleOptions,
      showResults,
      allowReview,
      startDate,
      endDate,
      difficulty,
      estimatedTime,
      questions
    } = body;

    // Validate required fields
    if (!title || !duration || !totalPoints || !passingScore) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // If moduleId is provided, check if user has access
    if (moduleId && user.role === 'instructor') {
      const module = await prisma.module.findFirst({
        where: {
          id: moduleId,
          instructorId: user.id
        }
      });

      if (!module) {
        return NextResponse.json(
          { success: false, message: 'Module not found or access denied' },
          { status: 404 }
        );
      }
    }

    // Create exam
    const exam = await prisma.exam.create({
      data: {
        title,
        description,
        instructions,
        moduleId,
        duration,
        totalPoints,
        passingScore,
        maxAttempts: maxAttempts || 1,
        shuffleQuestions: shuffleQuestions || false,
        shuffleOptions: shuffleOptions || false,
        showResults: showResults !== false,
        allowReview: allowReview || false,
        startDate: startDate ? new Date(startDate) : null,
        endDate: endDate ? new Date(endDate) : null,
        difficulty: difficulty || 'medium',
        estimatedTime,
        createdBy: user.id,
        isPublished: false
      },
      include: {
        module: {
          select: {
            id: true,
            title: true,
            code: true
          }
        },
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true
          }
        }
      }
    });

    // Add questions if provided
    if (questions && questions.length > 0) {
      const examQuestions = questions.map((q: any, index: number) => ({
        testId: exam.id,
        questionId: q.questionId,
        orderIndex: q.orderIndex || index,
        pointsOverride: q.pointsOverride
      }));

      await prisma.examQuestion.createMany({
        data: examQuestions
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Exam created successfully',
      data: { exam }
    }, { status: 201 });

  } catch (error) {
    console.error('Create test error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
