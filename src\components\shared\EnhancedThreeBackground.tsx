import React, { useEffect, useRef } from 'react';
import * as THREE from 'three';
import { 
  createParticleSystem, 
  animateParticles, 
  createGeometricShapes,
  createNeonLines,
  animateGeometricShapes,
  resize<PERSON>enderer 
} from '../../utils/three.utils';

interface EnhancedThreeBackgroundProps {
  className?: string;
  particleCount?: number;
  shapeCount?: number;
  lineCount?: number;
  animated?: boolean;
  theme?: 'auth' | 'dashboard' | 'welcome' | 'coming-soon';
}

export const EnhancedThreeBackground: React.FC<EnhancedThreeBackgroundProps> = ({
  className = '',
  particleCount = 1000,
  shapeCount = 20,
  lineCount = 30,
  animated = true,
  theme = 'auth'
}) => {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const particlesRef = useRef<THREE.Points | null>(null);
  const shapesRef = useRef<THREE.Group | null>(null);
  const linesRef = useRef<THREE.Group | null>(null);
  const animationIdRef = useRef<number | null>(null);

  useEffect(() => {
    if (!mountRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // Camera setup
    const camera = new THREE.PerspectiveCamera(
      75,
      mountRef.current.clientWidth / mountRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.z = theme === 'auth' ? 100 : 150;
    cameraRef.current = camera;

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({ 
      alpha: true, 
      antialias: true,
      powerPreference: 'high-performance'
    });
    renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);
    renderer.setClearColor(0x000000, 0);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    mountRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Create particles
    const particles = createParticleSystem(particleCount);
    scene.add(particles);
    particlesRef.current = particles;

    // Create geometric shapes
    const shapes = createGeometricShapes(shapeCount);
    scene.add(shapes);
    shapesRef.current = shapes;

    // Create neon lines
    const lines = createNeonLines(lineCount);
    scene.add(lines);
    linesRef.current = lines;

    // Lighting setup
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);

    // Add point lights for more dynamic lighting
    const pointLight1 = new THREE.PointLight(0x00ffff, 0.5, 100);
    pointLight1.position.set(50, 50, 50);
    scene.add(pointLight1);

    const pointLight2 = new THREE.PointLight(0xff00ff, 0.5, 100);
    pointLight2.position.set(-50, -50, 50);
    scene.add(pointLight2);

    // Mouse interaction
    let mouseX = 0;
    let mouseY = 0;
    const handleMouseMove = (event: MouseEvent) => {
      mouseX = (event.clientX / window.innerWidth) * 2 - 1;
      mouseY = -(event.clientY / window.innerHeight) * 2 + 1;
    };
    window.addEventListener('mousemove', handleMouseMove);

    // Animation loop
    const animate = (time: number) => {
      if (animated) {
        // Animate particles
        if (particlesRef.current) {
          animateParticles(particlesRef.current, time);
        }
        
        // Animate geometric shapes
        if (shapesRef.current) {
          animateGeometricShapes(shapesRef.current, time);
        }
        
        // Animate lines
        if (linesRef.current) {
          linesRef.current.rotation.x += 0.001;
          linesRef.current.rotation.y += 0.002;
        }
        
        // Camera movement based on mouse
        if (cameraRef.current) {
          cameraRef.current.position.x += (mouseX * 10 - cameraRef.current.position.x) * 0.05;
          cameraRef.current.position.y += (mouseY * 10 - cameraRef.current.position.y) * 0.05;
          cameraRef.current.lookAt(scene.position);
        }
        
        // Animate point lights
        pointLight1.position.x = Math.sin(time * 0.001) * 50;
        pointLight1.position.y = Math.cos(time * 0.001) * 50;
        
        pointLight2.position.x = Math.cos(time * 0.0015) * 50;
        pointLight2.position.z = Math.sin(time * 0.0015) * 50;
      }
      
      renderer.render(scene, camera);
      animationIdRef.current = requestAnimationFrame(animate);
    };

    animate(0);

    // Handle resize
    const handleResize = () => {
      if (mountRef.current && rendererRef.current && cameraRef.current) {
        resizeRenderer(rendererRef.current, cameraRef.current, mountRef.current);
      }
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('mousemove', handleMouseMove);
      
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }
      
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
      
      renderer.dispose();
      
      // Dispose geometries and materials
      scene.traverse((object) => {
        if (object instanceof THREE.Mesh || object instanceof THREE.Points || object instanceof THREE.Line) {
          object.geometry.dispose();
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose());
          } else {
            object.material.dispose();
          }
        }
      });
    };
  }, [particleCount, shapeCount, lineCount, animated, theme]);

  return (
    <div
      ref={mountRef}
      className={`absolute inset-0 -z-10 ${className}`}
      style={{ pointerEvents: 'none' }}
    />
  );
};
