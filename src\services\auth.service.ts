import { LoginCredentials, RegisterCredentials, AuthResponse, User } from '../types/auth.types';

class AuthService {
  private isElectron = typeof window !== 'undefined' && window.electronAPI;

  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      if (this.isElectron) {
        // Use Electron IPC for authentication
        const response = await window.electronAPI.auth.login(credentials);
        return response;
      } else {
        // Fallback for web version - dummy authentication
        return this.dummyLogin(credentials);
      }
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        message: '<PERSON><PERSON> failed. Please try again.'
      };
    }
  }

  async logout(): Promise<AuthResponse> {
    try {
      if (this.isElectron) {
        const response = await window.electronAPI.auth.logout();
        this.clearLocalStorage();
        return response;
      } else {
        this.clearLocalStorage();
        return { success: true };
      }
    } catch (error) {
      console.error('Logout error:', error);
      return {
        success: false,
        message: 'Logout failed'
      };
    }
  }

  async register(credentials: RegisterCredentials): Promise<AuthResponse> {
    // Dummy registration logic
    const { name, email, password, confirmPassword } = credentials;

    if (password !== confirmPassword) {
      return {
        success: false,
        message: 'Passwords do not match'
      };
    }

    if (password.length < 6) {
      return {
        success: false,
        message: 'Password must be at least 6 characters long'
      };
    }

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    const user: User = {
      id: Date.now().toString(),
      email,
      name,
      role: 'user',
      createdAt: new Date().toISOString()
    };

    return {
      success: true,
      user,
      token: 'dummy-jwt-token',
      message: 'Registration successful'
    };
  }

  private async dummyLogin(credentials: LoginCredentials): Promise<AuthResponse> {
    const { email, password } = credentials;

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Dummy validation
    if (email && password) {
      const user: User = {
        id: '1',
        email,
        name: this.getNameFromEmail(email),
        role: 'user',
        lastLogin: new Date().toISOString()
      };

      return {
        success: true,
        user,
        token: 'dummy-jwt-token'
      };
    } else {
      return {
        success: false,
        message: 'Invalid email or password'
      };
    }
  }

  private getNameFromEmail(email: string): string {
    const name = email.split('@')[0];
    return name.charAt(0).toUpperCase() + name.slice(1);
  }

  private clearLocalStorage(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_user');
    }
  }

  saveAuthData(user: User, token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token);
      localStorage.setItem('auth_user', JSON.stringify(user));
    }
  }

  getStoredAuthData(): { user: User | null; token: string | null } {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('auth_token');
      const userStr = localStorage.getItem('auth_user');
      const user = userStr ? JSON.parse(userStr) : null;
      return { user, token };
    }
    return { user: null, token: null };
  }

  isAuthenticated(): boolean {
    const { token } = this.getStoredAuthData();
    return !!token;
  }
}

export const authService = new AuthService();
