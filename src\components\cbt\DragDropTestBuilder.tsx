import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus, 
  Trash2, 
  GripVertical, 
  Copy, 
  Edit3,
  FileText,
  CheckCircle,
  Circle,
  Type,
  Hash,
  Upload,
  Image as ImageIcon,
  Video,
  Mic
} from 'lucide-react';
import { Question, QuestionType } from '../../types/cbt.types';

interface DragDropItem {
  id: string;
  type: 'question' | 'section' | 'media';
  content: any;
  order: number;
}

interface DragDropTestBuilderProps {
  questions: Question[];
  onQuestionsChange: (questions: Question[]) => void;
  onAddQuestion: (type: QuestionType) => void;
}

const QuestionTypeButton: React.FC<{
  type: QuestionType;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  label: string;
  description: string;
  onClick: () => void;
}> = ({ type, icon: Icon, label, description, onClick }) => {
  return (
    <motion.button
      onClick={onClick}
      className="flex items-center space-x-3 p-4 bg-gray-800/50 hover:bg-gray-700/50 rounded-lg border border-gray-600/50 hover:border-gray-500/50 transition-all duration-200 text-left w-full"
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <div className="w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center">
        <Icon className="text-white" size={20} />
      </div>
      <div className="flex-1">
        <h4 className="text-white font-medium font-mono">{label}</h4>
        <p className="text-gray-400 text-sm font-mono">{description}</p>
      </div>
    </motion.button>
  );
};

const DraggableQuestion: React.FC<{
  question: Question;
  index: number;
  onEdit: () => void;
  onDelete: () => void;
  onDuplicate: () => void;
  isDragging: boolean;
  onDragStart: () => void;
  onDragEnd: () => void;
}> = ({ question, index, onEdit, onDelete, onDuplicate, isDragging, onDragStart, onDragEnd }) => {
  const dragRef = useRef<HTMLDivElement>(null);

  const getQuestionTypeIcon = (type: QuestionType) => {
    switch (type) {
      case 'multiple_choice': return CheckCircle;
      case 'true_false': return Circle;
      case 'essay': return FileText;
      case 'fill_blank': return Type;
      case 'numeric': return Hash;
      default: return FileText;
    }
  };

  const getQuestionPreview = (question: Question) => {
    const maxLength = 100;
    const content = question.content || '';
    return content.length > maxLength ? content.substring(0, maxLength) + '...' : content;
  };

  const Icon = getQuestionTypeIcon(question.type);

  return (
    <motion.div
      ref={dragRef}
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={`bg-black/60 backdrop-blur-xl rounded-lg border border-gray-600/50 p-4 cursor-move ${
        isDragging ? 'shadow-2xl scale-105 z-50' : 'hover:border-gray-500/50'
      } transition-all duration-200`}
      draggable
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}
    >
      <div className="flex items-start space-x-3">
        {/* Drag Handle */}
        <div className="flex items-center space-x-2 mt-1">
          <GripVertical className="text-gray-500 cursor-grab active:cursor-grabbing" size={16} />
          <span className="text-gray-400 font-mono text-sm">{index + 1}</span>
        </div>

        {/* Question Icon */}
        <div className="w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
          <Icon className="text-white" size={16} />
        </div>

        {/* Question Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-2">
            <h4 className="text-white font-medium font-mono truncate">
              {question.title || `Question ${index + 1}`}
            </h4>
            <span className="px-2 py-1 bg-gray-800 text-gray-300 rounded text-xs font-mono">
              {question.type.replace('_', ' ').toUpperCase()}
            </span>
            <span className="px-2 py-1 bg-gray-800 text-gray-300 rounded text-xs font-mono">
              {question.points} pts
            </span>
          </div>
          
          <p className="text-gray-400 text-sm font-mono leading-relaxed">
            {getQuestionPreview(question)}
          </p>

          {/* Question Options Preview */}
          {question.type === 'multiple_choice' && question.options && (
            <div className="mt-2 space-y-1">
              {question.options.slice(0, 2).map((option, idx) => (
                <div key={option.id} className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full border ${
                    option.isCorrect ? 'bg-green-400 border-green-400' : 'border-gray-500'
                  }`} />
                  <span className="text-gray-500 text-xs font-mono truncate">
                    {option.text}
                  </span>
                </div>
              ))}
              {question.options.length > 2 && (
                <span className="text-gray-500 text-xs font-mono">
                  +{question.options.length - 2} more options
                </span>
              )}
            </div>
          )}

          {/* Tags */}
          {question.tags && question.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {question.tags.slice(0, 3).map(tag => (
                <span key={tag} className="px-2 py-1 bg-gray-900/50 text-gray-400 rounded text-xs font-mono">
                  {tag}
                </span>
              ))}
              {question.tags.length > 3 && (
                <span className="text-gray-500 text-xs font-mono">
                  +{question.tags.length - 3} more
                </span>
              )}
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-1 flex-shrink-0">
          <motion.button
            onClick={onEdit}
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Edit3 size={14} />
          </motion.button>
          
          <motion.button
            onClick={onDuplicate}
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Copy size={14} />
          </motion.button>
          
          <motion.button
            onClick={onDelete}
            className="p-2 text-gray-400 hover:text-red-400 hover:bg-red-900/20 rounded transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Trash2 size={14} />
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

export const DragDropTestBuilder: React.FC<DragDropTestBuilderProps> = ({
  questions,
  onQuestionsChange,
  onAddQuestion
}) => {
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [showQuestionTypes, setShowQuestionTypes] = useState(false);

  const questionTypes: Array<{
    type: QuestionType;
    icon: React.ComponentType<{ size?: number; className?: string }>;
    label: string;
    description: string;
  }> = [
    {
      type: 'multiple_choice',
      icon: CheckCircle,
      label: 'Multiple Choice',
      description: 'Single or multiple correct answers'
    },
    {
      type: 'true_false',
      icon: Circle,
      label: 'True/False',
      description: 'Binary choice question'
    },
    {
      type: 'essay',
      icon: FileText,
      label: 'Essay',
      description: 'Long-form written response'
    },
    {
      type: 'fill_blank',
      icon: Type,
      label: 'Fill in the Blank',
      description: 'Complete the missing text'
    },
    {
      type: 'numeric',
      icon: Hash,
      label: 'Numeric',
      description: 'Mathematical or numerical answer'
    }
  ];

  const handleDragStart = (index: number) => {
    setDraggedIndex(index);
  };

  const handleDragEnd = () => {
    setDraggedIndex(null);
  };

  const handleDragOver = (e: React.DragEvent, targetIndex: number) => {
    e.preventDefault();
    
    if (draggedIndex === null || draggedIndex === targetIndex) return;

    const newQuestions = [...questions];
    const draggedQuestion = newQuestions[draggedIndex];
    
    // Remove from old position
    newQuestions.splice(draggedIndex, 1);
    
    // Insert at new position
    newQuestions.splice(targetIndex, 0, draggedQuestion);
    
    onQuestionsChange(newQuestions);
    setDraggedIndex(targetIndex);
  };

  const handleEditQuestion = (index: number) => {
    // This would open a question editor modal
    console.log('Edit question:', index);
  };

  const handleDeleteQuestion = (index: number) => {
    const newQuestions = questions.filter((_, i) => i !== index);
    onQuestionsChange(newQuestions);
  };

  const handleDuplicateQuestion = (index: number) => {
    const questionToDuplicate = questions[index];
    const duplicatedQuestion: Question = {
      ...questionToDuplicate,
      id: 'q_' + Math.random().toString(36).substr(2, 9),
      title: `${questionToDuplicate.title} (Copy)`,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const newQuestions = [...questions];
    newQuestions.splice(index + 1, 0, duplicatedQuestion);
    onQuestionsChange(newQuestions);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-bold text-white font-mono">Test Builder</h3>
          <p className="text-gray-400 font-mono">Drag and drop to reorder questions</p>
        </div>
        
        <motion.button
          onClick={() => setShowQuestionTypes(!showQuestionTypes)}
          className="flex items-center space-x-2 px-4 py-2 bg-black hover:bg-gray-900 text-white rounded-lg transition-colors font-mono border border-gray-600"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Plus size={16} />
          <span>Add Question</span>
        </motion.button>
      </div>

      {/* Question Type Selector */}
      <AnimatePresence>
        {showQuestionTypes && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-black/60 backdrop-blur-xl rounded-xl p-4 border border-gray-600/50"
          >
            <h4 className="text-lg font-bold text-white mb-4 font-mono">Choose Question Type</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {questionTypes.map(({ type, icon, label, description }) => (
                <QuestionTypeButton
                  key={type}
                  type={type}
                  icon={icon}
                  label={label}
                  description={description}
                  onClick={() => {
                    onAddQuestion(type);
                    setShowQuestionTypes(false);
                  }}
                />
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Questions List */}
      <div className="space-y-3">
        {questions.length === 0 ? (
          <div className="text-center py-12 bg-black/60 backdrop-blur-xl rounded-xl border border-gray-600/50">
            <FileText className="mx-auto text-gray-600 mb-4" size={48} />
            <h3 className="text-xl font-bold text-gray-400 mb-2 font-mono">No Questions Yet</h3>
            <p className="text-gray-500 font-mono mb-4">Start building your test by adding questions</p>
            <motion.button
              onClick={() => setShowQuestionTypes(true)}
              className="px-6 py-2 bg-black hover:bg-gray-900 text-white rounded-lg transition-colors font-mono border border-gray-600"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Add First Question
            </motion.button>
          </div>
        ) : (
          <AnimatePresence>
            {questions.map((question, index) => (
              <div
                key={question.id}
                onDragOver={(e) => handleDragOver(e, index)}
                onDrop={(e) => e.preventDefault()}
              >
                <DraggableQuestion
                  question={question}
                  index={index}
                  onEdit={() => handleEditQuestion(index)}
                  onDelete={() => handleDeleteQuestion(index)}
                  onDuplicate={() => handleDuplicateQuestion(index)}
                  isDragging={draggedIndex === index}
                  onDragStart={() => handleDragStart(index)}
                  onDragEnd={handleDragEnd}
                />
              </div>
            ))}
          </AnimatePresence>
        )}
      </div>

      {/* Test Summary */}
      {questions.length > 0 && (
        <div className="bg-black/60 backdrop-blur-xl rounded-xl p-4 border border-gray-600/50">
          <h4 className="text-lg font-bold text-white mb-3 font-mono">Test Summary</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-white font-mono">{questions.length}</p>
              <p className="text-gray-400 text-sm font-mono">Questions</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-white font-mono">
                {questions.reduce((sum, q) => sum + (q.points || 0), 0)}
              </p>
              <p className="text-gray-400 text-sm font-mono">Total Points</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-white font-mono">
                {questions.reduce((sum, q) => sum + (q.timeLimit || 120), 0) / 60}
              </p>
              <p className="text-gray-400 text-sm font-mono">Est. Minutes</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-white font-mono">
                {new Set(questions.map(q => q.type)).size}
              </p>
              <p className="text-gray-400 text-sm font-mono">Question Types</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DragDropTestBuilder;
