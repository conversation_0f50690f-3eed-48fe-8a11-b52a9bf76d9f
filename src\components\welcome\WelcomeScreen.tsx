import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  BookOpen,
  Users,
  Calendar,
  GraduationCap,
  Library,
  CreditCard,
  Clock,
  BarChart3,
  Settings,
  ChevronRight,
  Wifi,
  WifiOff,
  Database,
  RefreshCw
} from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { ThemeToggle } from '../shared/ThemeToggle';
import { EnhancedThreeBackground } from '../shared/EnhancedThreeBackground';

interface ModuleCard {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  isActive: boolean;
  comingSoon?: boolean;
}

interface WelcomeScreenProps {
  onModuleSelect: (moduleId: string) => void;
  user?: {
    name: string;
    role: string;
    avatar?: string;
  };
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onModuleSelect, user }) => {
  const { theme, getAnimationDuration } = useTheme();
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'success' | 'error'>('idle');

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const modules: ModuleCard[] = [
    {
      id: 'cbt',
      name: 'CBT Exam Module',
      description: 'Computer-Based Testing with advanced security features',
      icon: BookOpen,
      color: 'from-blue-500 to-blue-600',
      isActive: true
    },
    {
      id: 'lms',
      name: 'Learning Management',
      description: 'Course management and content delivery system',
      icon: GraduationCap,
      color: 'from-green-500 to-green-600',
      isActive: true,
      comingSoon: true
    },
    {
      id: 'attendance',
      name: 'Attendance Module',
      description: 'Student attendance tracking and reporting',
      icon: Users,
      color: 'from-purple-500 to-purple-600',
      isActive: true,
      comingSoon: true
    },
    {
      id: 'gradebook',
      name: 'Gradebook',
      description: 'Grade management and academic records',
      icon: BarChart3,
      color: 'from-orange-500 to-orange-600',
      isActive: true,
      comingSoon: true
    },
    {
      id: 'library',
      name: 'Library Management',
      description: 'Digital library and resource management',
      icon: Library,
      color: 'from-teal-500 to-teal-600',
      isActive: true,
      comingSoon: true
    },
    {
      id: 'fee_management',
      name: 'Fee Management',
      description: 'Student fee collection and financial tracking',
      icon: CreditCard,
      color: 'from-red-500 to-red-600',
      isActive: true,
      comingSoon: true
    },
    {
      id: 'timetable',
      name: 'Timetable',
      description: 'Class scheduling and timetable management',
      icon: Clock,
      color: 'from-indigo-500 to-indigo-600',
      isActive: true,
      comingSoon: true
    },
    {
      id: 'calendar',
      name: 'Academic Calendar',
      description: 'Events, deadlines, and academic calendar',
      icon: Calendar,
      color: 'from-pink-500 to-pink-600',
      isActive: true,
      comingSoon: true
    },
    {
      id: 'admin',
      name: 'Admin Panel',
      description: 'System administration and monitoring',
      icon: Settings,
      color: 'from-gray-500 to-gray-600',
      isActive: user?.role === 'admin',
      comingSoon: false
    }
  ];

  const handleSync = async () => {
    setSyncStatus('syncing');
    // Simulate sync process
    await new Promise(resolve => setTimeout(resolve, 2000));
    setSyncStatus('success');
    setTimeout(() => setSyncStatus('idle'), 3000);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-black relative overflow-hidden">
      <EnhancedThreeBackground 
        particleCount={600} 
        shapeCount={12} 
        lineCount={15} 
        animated 
        theme="welcome" 
      />
      
      {/* Header */}
      <header className="relative z-10 p-6 flex justify-between items-center">
        <div className="flex items-center gap-4">
          <motion.div
            className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center"
            whileHover={{ scale: 1.05, rotate: 5 }}
            transition={{ duration: getAnimationDuration('fast') }}
          >
            <BookOpen className="text-white" size={24} />
          </motion.div>
          <div>
            <h1 className="text-2xl font-bold font-mono text-gray-900 dark:text-white">
              LMS CBT System
            </h1>
            <p className="text-sm text-gray-600 dark:text-gray-400 font-mono">
              Comprehensive Educational Management Platform
            </p>
          </div>
        </div>

        <div className="flex items-center gap-4">
          {/* Sync Status */}
          <motion.button
            onClick={handleSync}
            className={`
              flex items-center gap-2 px-3 py-2 rounded-lg font-mono text-sm
              ${isOnline 
                ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400' 
                : 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400'
              }
              hover:scale-105 transition-transform
            `}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            disabled={syncStatus === 'syncing'}
          >
            {syncStatus === 'syncing' ? (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              >
                <RefreshCw size={16} />
              </motion.div>
            ) : isOnline ? (
              <Wifi size={16} />
            ) : (
              <WifiOff size={16} />
            )}
            <span>
              {syncStatus === 'syncing' ? 'Syncing...' : 
               syncStatus === 'success' ? 'Synced' :
               isOnline ? 'Online' : 'Offline'}
            </span>
          </motion.button>

          {/* Database Status */}
          <div className="flex items-center gap-2 px-3 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-lg font-mono text-sm">
            <Database size={16} />
            <span>Local DB</span>
          </div>

          <ThemeToggle showLabel />
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 px-6 pb-6">
        {/* Welcome Section */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: getAnimationDuration('normal') }}
        >
          <h2 className="text-4xl font-bold font-mono text-gray-900 dark:text-white mb-2">
            {getGreeting()}{user ? `, ${user.name}` : ''}!
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 font-mono">
            Welcome to your educational management dashboard. Select a module to get started.
          </p>
        </motion.div>

        {/* Module Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <AnimatePresence>
            {modules.map((module, index) => (
              <motion.div
                key={module.id}
                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -20, scale: 0.9 }}
                transition={{ 
                  duration: getAnimationDuration('normal'),
                  delay: index * 0.1 
                }}
                whileHover={{ 
                  scale: 1.02, 
                  y: -5,
                  transition: { duration: getAnimationDuration('fast') }
                }}
                whileTap={{ scale: 0.98 }}
                className={`
                  relative p-6 rounded-2xl cursor-pointer
                  bg-white dark:bg-gray-800 
                  border border-gray-200 dark:border-gray-700
                  shadow-lg hover:shadow-xl
                  transition-all duration-300
                  ${!module.isActive ? 'opacity-50 cursor-not-allowed' : ''}
                `}
                onClick={() => module.isActive && !module.comingSoon && onModuleSelect(module.id)}
              >
                {/* Background Gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${module.color} opacity-5 rounded-2xl`} />
                
                {/* Icon */}
                <div className={`w-12 h-12 bg-gradient-to-br ${module.color} rounded-xl flex items-center justify-center mb-4`}>
                  <module.icon className="text-white" size={24} />
                </div>

                {/* Content */}
                <h3 className="text-xl font-bold font-mono text-gray-900 dark:text-white mb-2">
                  {module.name}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 font-mono text-sm mb-4">
                  {module.description}
                </p>

                {/* Status */}
                <div className="flex items-center justify-between">
                  {module.comingSoon ? (
                    <span className="px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 rounded-md text-xs font-mono">
                      Coming Soon
                    </span>
                  ) : (
                    <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-md text-xs font-mono">
                      Available
                    </span>
                  )}
                  
                  {module.isActive && !module.comingSoon && (
                    <ChevronRight className="text-gray-400" size={20} />
                  )}
                </div>

                {/* Hover Effect */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-2xl opacity-0"
                  whileHover={{ opacity: 1 }}
                  transition={{ duration: getAnimationDuration('fast') }}
                />
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      </main>
    </div>
  );
};
