import React from 'react';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Home, 
  ChevronRight, 
  Bell, 
  User, 
  LogOut,
  Settings,
  Shield,
  BookOpen,
  Users
} from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';

interface BreadcrumbItem {
  label: string;
  path?: string;
  icon?: React.ComponentType<{ size?: number; className?: string }>;
}

interface NavigationHeaderProps {
  title: string;
  breadcrumbs?: BreadcrumbItem[];
  user?: {
    id: string;
    name: string;
    email: string;
    role: string;
    avatar?: string;
  };
  onBack?: () => void;
  onHome?: () => void;
  onLogout?: () => void;
  showNotifications?: boolean;
  notificationCount?: number;
  actions?: React.ReactNode;
}

export const NavigationHeader: React.FC<NavigationHeaderProps> = ({
  title,
  breadcrumbs = [],
  user,
  onBack,
  onHome,
  onLogout,
  showNotifications = true,
  notificationCount = 0,
  actions
}) => {
  const { theme } = useTheme();

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin': return Shield;
      case 'instructor': return BookOpen;
      case 'student': return Users;
      default: return User;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'from-red-500 to-pink-600';
      case 'instructor': return 'from-green-500 to-teal-600';
      case 'student': return 'from-blue-500 to-purple-600';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  const RoleIcon = user ? getRoleIcon(user.role) : User;

  return (
    <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Left side - Navigation */}
          <div className="flex items-center space-x-4">
            {/* Back Button */}
            {onBack && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={onBack}
                className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors font-mono"
              >
                <ArrowLeft size={20} />
                <span className="hidden sm:inline">Back</span>
              </motion.button>
            )}

            {/* Home Button */}
            {onHome && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={onHome}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
              >
                <Home size={20} />
              </motion.button>
            )}

            {/* Separator */}
            {(onBack || onHome) && (
              <div className="h-6 w-px bg-gray-300 dark:bg-gray-600" />
            )}

            {/* Breadcrumbs */}
            {breadcrumbs.length > 0 && (
              <nav className="flex items-center space-x-2">
                {breadcrumbs.map((item, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    {index > 0 && (
                      <ChevronRight size={16} className="text-gray-400 dark:text-gray-500" />
                    )}
                    <div className="flex items-center space-x-1">
                      {item.icon && (
                        <item.icon size={16} className="text-gray-500 dark:text-gray-400" />
                      )}
                      <span className={`text-sm font-mono ${
                        index === breadcrumbs.length - 1
                          ? 'text-gray-900 dark:text-white font-medium'
                          : 'text-gray-500 dark:text-gray-400'
                      }`}>
                        {item.label}
                      </span>
                    </div>
                  </div>
                ))}
              </nav>
            )}

            {/* Title */}
            <h1 className="text-xl font-bold text-gray-900 dark:text-white font-mono">
              {title}
            </h1>
          </div>

          {/* Right side - Actions & User */}
          <div className="flex items-center space-x-4">
            {/* Custom Actions */}
            {actions && (
              <>
                {actions}
                <div className="h-6 w-px bg-gray-300 dark:bg-gray-600" />
              </>
            )}

            {/* Notifications */}
            {showNotifications && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="relative p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
              >
                <Bell size={20} />
                {notificationCount > 0 && (
                  <motion.span
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-mono"
                  >
                    {notificationCount > 9 ? '9+' : notificationCount}
                  </motion.span>
                )}
              </motion.button>
            )}

            {/* User Profile */}
            {user && (
              <div className="flex items-center space-x-3">
                <div className={`w-8 h-8 bg-gradient-to-r ${getRoleColor(user.role)} rounded-full flex items-center justify-center`}>
                  <RoleIcon size={16} className="text-white" />
                </div>
                <div className="hidden sm:block">
                  <p className="text-sm font-medium text-gray-900 dark:text-white font-mono">{user.name}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 font-mono capitalize">{user.role}</p>
                </div>
              </div>
            )}

            {/* Settings */}
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
            >
              <Settings size={20} />
            </motion.button>

            {/* Logout */}
            {onLogout && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={onLogout}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
              >
                <LogOut size={20} />
              </motion.button>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

// Navigation Context for managing navigation state
interface NavigationContextType {
  currentPath: string[];
  pushPath: (path: string) => void;
  popPath: () => void;
  goToPath: (path: string[]) => void;
  clearPath: () => void;
}

const NavigationContext = React.createContext<NavigationContextType | undefined>(undefined);

export const NavigationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentPath, setCurrentPath] = React.useState<string[]>([]);

  const pushPath = (path: string) => {
    setCurrentPath(prev => [...prev, path]);
  };

  const popPath = () => {
    setCurrentPath(prev => prev.slice(0, -1));
  };

  const goToPath = (path: string[]) => {
    setCurrentPath(path);
  };

  const clearPath = () => {
    setCurrentPath([]);
  };

  return (
    <NavigationContext.Provider value={{
      currentPath,
      pushPath,
      popPath,
      goToPath,
      clearPath
    }}>
      {children}
    </NavigationContext.Provider>
  );
};

export const useNavigation = () => {
  const context = React.useContext(NavigationContext);
  if (!context) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
};

// Breadcrumb generator utility
export const generateBreadcrumbs = (path: string[], role?: string): BreadcrumbItem[] => {
  const breadcrumbs: BreadcrumbItem[] = [];

  // Add home based on role
  if (role) {
    breadcrumbs.push({
      label: `${role.charAt(0).toUpperCase() + role.slice(1)} Dashboard`,
      path: '/',
      icon: role === 'admin' ? Shield : role === 'instructor' ? BookOpen : Users
    });
  }

  // Add path segments
  path.forEach((segment, index) => {
    breadcrumbs.push({
      label: segment.charAt(0).toUpperCase() + segment.slice(1).replace('-', ' '),
      path: '/' + path.slice(0, index + 1).join('/')
    });
  });

  return breadcrumbs;
};
