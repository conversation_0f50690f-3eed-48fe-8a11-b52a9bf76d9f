@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Space+Mono:ital,wght@0,400;0,700;1,400;1,700&display=swap');

:root {
  --background: #ffffff;
  --foreground: #000000;
  --primary: #000000;
  --secondary: #1a1a1a;
  --accent: #333333;
  --muted: #666666;
  --border: #e5e5e5;
  --card: #f8f8f8;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-muted: var(--muted);
  --color-border: var(--border);
  --color-card: var(--card);
  --font-sans: 'Space Mono', monospace;
  --font-mono: 'Space Mono', monospace;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #000000;
    --foreground: #ffffff;
    --primary: #ffffff;
    --secondary: #e5e5e5;
    --accent: #cccccc;
    --muted: #999999;
    --border: #333333;
    --card: #1a1a1a;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Space Mono', monospace;
  font-weight: 400;
  line-height: 1.6;
}

/* CBT Specific Styles */
.cbt-container {
  background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%);
  min-height: 100vh;
}

.cbt-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.cbt-button {
  background: linear-gradient(135deg, #000000 0%, #333333 100%);
  color: white;
  border: 1px solid #333333;
  transition: all 0.3s ease;
}

.cbt-button:hover {
  background: linear-gradient(135deg, #333333 0%, #000000 100%);
  border-color: #666666;
}

.cbt-input {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid #333333;
  color: white;
}

.cbt-input:focus {
  border-color: #666666;
  outline: none;
  box-shadow: 0 0 0 2px rgba(102, 102, 102, 0.2);
}
