import React from 'react';
import { motion } from 'framer-motion';
import { Search, Bell, User, Minimize2, Maximize2, X } from 'lucide-react';
import { User as UserType } from '../../types/auth.types';

interface HeaderProps {
  user: UserType;
  onSearch?: (query: string) => void;
  onNotificationClick?: () => void;
  onProfileClick?: () => void;
}

export const Header: React.FC<HeaderProps> = ({
  user,
  onSearch,
  onNotificationClick,
  onProfileClick
}) => {
  const [searchQuery, setSearchQuery] = React.useState('');
  const isElectron = typeof window !== 'undefined' && window.electronAPI;

  const handleWindowControl = async (action: 'minimize' | 'maximize' | 'close') => {
    if (isElectron) {
      try {
        switch (action) {
          case 'minimize':
            await window.electronAPI.window.minimize();
            break;
          case 'maximize':
            await window.electronAPI.window.maximize();
            break;
          case 'close':
            await window.electronAPI.window.close();
            break;
        }
      } catch (error) {
        console.error('Window control error:', error);
      }
    }
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch?.(searchQuery);
  };

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className="bg-white/10 backdrop-blur-lg border-b border-gray-200/20 px-6 py-4"
    >
      <div className="flex items-center justify-between">
        {/* Left side - Search */}
        <div className="flex-1 max-w-md">
          <form onSubmit={handleSearchSubmit} className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-white/10 border border-gray-300/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent"
            />
          </form>
        </div>

        {/* Right side - Actions and Window Controls */}
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <motion.button
            onClick={onNotificationClick}
            className="relative p-2 rounded-lg bg-white/10 hover:bg-white/20 text-gray-300 hover:text-white transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Bell size={20} />
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              3
            </span>
          </motion.button>

          {/* User Profile */}
          <motion.button
            onClick={onProfileClick}
            className="flex items-center space-x-3 p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
              {user.avatar ? (
                <img src={user.avatar} alt={user.name} className="w-full h-full rounded-full object-cover" />
              ) : (
                <User size={16} className="text-white" />
              )}
            </div>
            <div className="text-left hidden sm:block">
              <p className="text-sm font-medium text-white">{user.name}</p>
              <p className="text-xs text-gray-400">{user.role}</p>
            </div>
          </motion.button>

          {/* Window Controls (Electron only) */}
          {isElectron && (
            <div className="flex items-center space-x-1 ml-4">
              <motion.button
                onClick={() => handleWindowControl('minimize')}
                className="p-2 rounded hover:bg-yellow-500/20 text-gray-400 hover:text-yellow-400 transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Minimize2 size={16} />
              </motion.button>
              <motion.button
                onClick={() => handleWindowControl('maximize')}
                className="p-2 rounded hover:bg-green-500/20 text-gray-400 hover:text-green-400 transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Maximize2 size={16} />
              </motion.button>
              <motion.button
                onClick={() => handleWindowControl('close')}
                className="p-2 rounded hover:bg-red-500/20 text-gray-400 hover:text-red-400 transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <X size={16} />
              </motion.button>
            </div>
          )}
        </div>
      </div>
    </motion.header>
  );
};
