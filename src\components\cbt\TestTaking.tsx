import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  Circle, 
  ArrowLeft, 
  ArrowRight,
  Flag,
  Eye,
  EyeOff,
  Shield,
  Pause,
  Play,
  Save,
  Send,
  AlertCircle
} from 'lucide-react';
import { useCBT } from '../../contexts/CBTContext';
import { Question, TestAttempt, QuestionType } from '../../types/cbt.types';

interface QuestionNavigationProps {
  questions: Question[];
  currentQuestionIndex: number;
  answers: Record<string, any>;
  flaggedQuestions: Set<string>;
  onQuestionSelect: (index: number) => void;
  onToggleFlag: (questionId: string) => void;
}

const QuestionNavigation: React.FC<QuestionNavigationProps> = ({
  questions,
  currentQuestionIndex,
  answers,
  flaggedQuestions,
  onQuestionSelect,
  onToggleFlag
}) => {
  const getQuestionStatus = (question: Question, index: number) => {
    const isAnswered = answers[question.id] !== undefined && answers[question.id] !== '';
    const isFlagged = flaggedQuestions.has(question.id);
    const isCurrent = index === currentQuestionIndex;

    if (isCurrent) return 'current';
    if (isFlagged) return 'flagged';
    if (isAnswered) return 'answered';
    return 'unanswered';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'current': return 'bg-white text-black border-white';
      case 'answered': return 'bg-green-900/50 text-green-400 border-green-600';
      case 'flagged': return 'bg-yellow-900/50 text-yellow-400 border-yellow-600';
      default: return 'bg-gray-800 text-gray-400 border-gray-600';
    }
  };

  return (
    <div className="bg-black/80 backdrop-blur-xl rounded-xl p-4 border border-gray-600/50">
      <h3 className="text-lg font-bold text-white mb-4 font-mono">Question Navigation</h3>
      
      <div className="grid grid-cols-5 gap-2 mb-4">
        {questions.map((question, index) => {
          const status = getQuestionStatus(question, index);
          return (
            <motion.button
              key={question.id}
              onClick={() => onQuestionSelect(index)}
              className={`w-10 h-10 rounded-lg border-2 font-mono text-sm font-bold transition-all duration-200 ${getStatusColor(status)}`}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              {index + 1}
              {flaggedQuestions.has(question.id) && (
                <Flag size={8} className="absolute -top-1 -right-1" />
              )}
            </motion.button>
          );
        })}
      </div>

      <div className="space-y-2 text-xs font-mono">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-green-900/50 border border-green-600 rounded"></div>
          <span className="text-gray-400">Answered</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-yellow-900/50 border border-yellow-600 rounded"></div>
          <span className="text-gray-400">Flagged</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-gray-800 border border-gray-600 rounded"></div>
          <span className="text-gray-400">Not Answered</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-white border border-white rounded"></div>
          <span className="text-gray-400">Current</span>
        </div>
      </div>
    </div>
  );
};

interface QuestionDisplayProps {
  question: Question;
  answer: any;
  onAnswerChange: (answer: any) => void;
  questionNumber: number;
  totalQuestions: number;
}

const QuestionDisplay: React.FC<QuestionDisplayProps> = ({
  question,
  answer,
  onAnswerChange,
  questionNumber,
  totalQuestions
}) => {
  const renderQuestionContent = () => {
    switch (question.type) {
      case 'multiple_choice':
        return (
          <div className="space-y-3">
            {question.options?.map((option, index) => (
              <motion.label
                key={option.id}
                className="flex items-center space-x-3 p-3 bg-gray-800/50 rounded-lg cursor-pointer hover:bg-gray-700/50 transition-colors"
                whileHover={{ scale: 1.01 }}
              >
                <input
                  type="radio"
                  name={`question-${question.id}`}
                  value={option.id}
                  checked={answer === option.id}
                  onChange={(e) => onAnswerChange(e.target.value)}
                  className="text-gray-600 focus:ring-gray-500"
                />
                <span className="text-white font-mono flex-1">{option.text}</span>
              </motion.label>
            ))}
          </div>
        );

      case 'true_false':
        return (
          <div className="space-y-3">
            <motion.label
              className="flex items-center space-x-3 p-3 bg-gray-800/50 rounded-lg cursor-pointer hover:bg-gray-700/50 transition-colors"
              whileHover={{ scale: 1.01 }}
            >
              <input
                type="radio"
                name={`question-${question.id}`}
                value="true"
                checked={answer === 'true'}
                onChange={(e) => onAnswerChange(e.target.value)}
                className="text-gray-600 focus:ring-gray-500"
              />
              <span className="text-white font-mono">True</span>
            </motion.label>
            <motion.label
              className="flex items-center space-x-3 p-3 bg-gray-800/50 rounded-lg cursor-pointer hover:bg-gray-700/50 transition-colors"
              whileHover={{ scale: 1.01 }}
            >
              <input
                type="radio"
                name={`question-${question.id}`}
                value="false"
                checked={answer === 'false'}
                onChange={(e) => onAnswerChange(e.target.value)}
                className="text-gray-600 focus:ring-gray-500"
              />
              <span className="text-white font-mono">False</span>
            </motion.label>
          </div>
        );

      case 'essay':
        return (
          <textarea
            value={answer || ''}
            onChange={(e) => onAnswerChange(e.target.value)}
            placeholder="Type your answer here..."
            rows={8}
            className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono resize-none"
          />
        );

      case 'fill_blank':
      case 'numeric':
        return (
          <input
            type={question.type === 'numeric' ? 'number' : 'text'}
            value={answer || ''}
            onChange={(e) => onAnswerChange(e.target.value)}
            placeholder="Enter your answer"
            className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
          />
        );

      default:
        return (
          <div className="text-center py-8">
            <AlertTriangle className="mx-auto text-yellow-400 mb-2" size={32} />
            <p className="text-gray-400 font-mono">Unsupported question type</p>
          </div>
        );
    }
  };

  return (
    <div className="bg-black/80 backdrop-blur-xl rounded-xl p-6 border border-gray-600/50">
      {/* Question Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <span className="text-gray-400 font-mono text-sm">
            Question {questionNumber} of {totalQuestions}
          </span>
          <span className="px-3 py-1 bg-gray-800 text-gray-300 rounded-full text-xs font-mono">
            {question.type.replace('_', ' ').toUpperCase()}
          </span>
          <span className="px-3 py-1 bg-gray-800 text-gray-300 rounded-full text-xs font-mono">
            {question.points} {question.points === 1 ? 'point' : 'points'}
          </span>
          <span className={`px-3 py-1 rounded-full text-xs font-mono ${
            question.difficulty === 'easy' ? 'bg-green-900/30 text-green-400' :
            question.difficulty === 'medium' ? 'bg-yellow-900/30 text-yellow-400' :
            'bg-red-900/30 text-red-400'
          }`}>
            {question.difficulty.toUpperCase()}
          </span>
        </div>
        
        {question.timeLimit && (
          <div className="flex items-center space-x-2 text-gray-400">
            <Clock size={16} />
            <span className="font-mono text-sm">{question.timeLimit}s limit</span>
          </div>
        )}
      </div>

      {/* Question Title */}
      <h2 className="text-xl font-bold text-white mb-4 font-mono">{question.title}</h2>

      {/* Question Content */}
      <div className="mb-6">
        <p className="text-gray-300 font-mono leading-relaxed whitespace-pre-wrap">
          {question.content}
        </p>
      </div>

      {/* Answer Input */}
      <div className="mb-6">
        {renderQuestionContent()}
      </div>

      {/* Question Footer */}
      <div className="flex items-center justify-between text-sm text-gray-400 font-mono">
        <div className="flex items-center space-x-4">
          {question.category && (
            <span>Category: {question.category}</span>
          )}
          {question.tags && question.tags.length > 0 && (
            <span>Tags: {question.tags.join(', ')}</span>
          )}
        </div>
      </div>
    </div>
  );
};

export const TestTaking: React.FC = () => {
  const { state } = useCBT();
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [flaggedQuestions, setFlaggedQuestions] = useState<Set<string>>(new Set());
  const [timeRemaining, setTimeRemaining] = useState(3600); // 1 hour in seconds
  const [isPaused, setIsPaused] = useState(false);
  const [showSubmitConfirm, setShowSubmitConfirm] = useState(false);
  const [autoSaveStatus, setAutoSaveStatus] = useState<'saved' | 'saving' | 'error'>('saved');

  // Mock questions - in real app, these would come from the current test
  const questions: Question[] = [
    {
      id: '1',
      type: 'multiple_choice',
      title: 'Basic Mathematics',
      content: 'What is 2 + 2?',
      points: 1,
      difficulty: 'easy',
      timeLimit: 60,
      options: [
        { id: 'a', text: '3', isCorrect: false },
        { id: 'b', text: '4', isCorrect: true },
        { id: 'c', text: '5', isCorrect: false },
        { id: 'd', text: '6', isCorrect: false }
      ],
      category: 'Mathematics',
      tags: ['basic', 'arithmetic'],
      createdBy: 'instructor1',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: '2',
      type: 'true_false',
      title: 'Science Fact',
      content: 'The Earth is flat.',
      points: 1,
      difficulty: 'easy',
      timeLimit: 30,
      correctAnswer: 'false',
      category: 'Science',
      tags: ['geography', 'basic'],
      createdBy: 'instructor1',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: '3',
      type: 'essay',
      title: 'Essay Question',
      content: 'Explain the importance of computer-based testing in modern education.',
      points: 10,
      difficulty: 'medium',
      timeLimit: 600,
      category: 'Education',
      tags: ['essay', 'education', 'technology'],
      createdBy: 'instructor1',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  // Timer effect
  useEffect(() => {
    if (isPaused || timeRemaining <= 0) return;

    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          handleSubmitTest();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isPaused, timeRemaining]);

  // Auto-save effect
  useEffect(() => {
    const autoSave = setTimeout(() => {
      setAutoSaveStatus('saving');
      // Simulate auto-save
      setTimeout(() => {
        setAutoSaveStatus('saved');
      }, 1000);
    }, 2000);

    return () => clearTimeout(autoSave);
  }, [answers]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const handleAnswerChange = (questionId: string, answer: any) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleToggleFlag = (questionId: string) => {
    setFlaggedQuestions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(questionId)) {
        newSet.delete(questionId);
      } else {
        newSet.add(questionId);
      }
      return newSet;
    });
  };

  const handleSubmitTest = () => {
    // Handle test submission
    console.log('Submitting test with answers:', answers);
    setShowSubmitConfirm(false);
  };

  const getProgressPercentage = () => {
    const answeredCount = Object.keys(answers).filter(key => 
      answers[key] !== undefined && answers[key] !== ''
    ).length;
    return (answeredCount / questions.length) * 100;
  };

  const currentQuestion = questions[currentQuestionIndex];
  const answeredCount = Object.keys(answers).filter(key => 
    answers[key] !== undefined && answers[key] !== ''
  ).length;

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">
      {/* Test Header */}
      <div className="bg-black/90 backdrop-blur-lg border-b border-gray-700/50 px-6 py-4 sticky top-0 z-20">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Shield className="text-green-400" size={24} />
            <div>
              <h1 className="text-xl font-bold text-white font-mono">Secure Test Mode</h1>
              <p className="text-gray-400 text-sm font-mono">
                Question {currentQuestionIndex + 1} of {questions.length}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-6">
            {/* Progress */}
            <div className="flex items-center space-x-2">
              <span className="text-gray-400 text-sm font-mono">Progress:</span>
              <div className="w-32 h-2 bg-gray-800 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-gray-600 to-white"
                  initial={{ width: 0 }}
                  animate={{ width: `${getProgressPercentage()}%` }}
                  transition={{ duration: 0.3 }}
                />
              </div>
              <span className="text-white text-sm font-mono">
                {answeredCount}/{questions.length}
              </span>
            </div>

            {/* Timer */}
            <div className={`flex items-center space-x-2 px-3 py-1 rounded-lg ${
              timeRemaining < 300 ? 'bg-red-900/30 border border-red-600/50' : 'bg-gray-800/50'
            }`}>
              <Clock className={timeRemaining < 300 ? 'text-red-400' : 'text-gray-400'} size={16} />
              <span className={`font-mono font-bold ${
                timeRemaining < 300 ? 'text-red-400' : 'text-white'
              }`}>
                {formatTime(timeRemaining)}
              </span>
            </div>

            {/* Auto-save status */}
            <div className="flex items-center space-x-2">
              {autoSaveStatus === 'saving' && (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                  className="w-4 h-4 border-2 border-gray-600 border-t-white rounded-full"
                />
              )}
              {autoSaveStatus === 'saved' && (
                <CheckCircle className="text-green-400" size={16} />
              )}
              {autoSaveStatus === 'error' && (
                <AlertCircle className="text-red-400" size={16} />
              )}
              <span className="text-gray-400 text-xs font-mono">
                {autoSaveStatus === 'saving' ? 'Saving...' : 
                 autoSaveStatus === 'saved' ? 'Saved' : 'Error'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex gap-6 p-6">
        {/* Question Display */}
        <div className="flex-1">
          <QuestionDisplay
            question={currentQuestion}
            answer={answers[currentQuestion.id]}
            onAnswerChange={(answer) => handleAnswerChange(currentQuestion.id, answer)}
            questionNumber={currentQuestionIndex + 1}
            totalQuestions={questions.length}
          />

          {/* Navigation Controls */}
          <div className="flex items-center justify-between mt-6">
            <div className="flex space-x-3">
              <motion.button
                onClick={handlePreviousQuestion}
                disabled={currentQuestionIndex === 0}
                className="flex items-center space-x-2 px-4 py-2 bg-gray-800 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors font-mono"
                whileHover={{ scale: currentQuestionIndex === 0 ? 1 : 1.05 }}
                whileTap={{ scale: currentQuestionIndex === 0 ? 1 : 0.95 }}
              >
                <ArrowLeft size={16} />
                <span>Previous</span>
              </motion.button>

              <motion.button
                onClick={() => handleToggleFlag(currentQuestion.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors font-mono ${
                  flaggedQuestions.has(currentQuestion.id)
                    ? 'bg-yellow-900/30 text-yellow-400 border border-yellow-600/50'
                    : 'bg-gray-800 hover:bg-gray-700 text-white'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Flag size={16} />
                <span>{flaggedQuestions.has(currentQuestion.id) ? 'Unflag' : 'Flag'}</span>
              </motion.button>
            </div>

            <div className="flex space-x-3">
              {currentQuestionIndex === questions.length - 1 ? (
                <motion.button
                  onClick={() => setShowSubmitConfirm(true)}
                  className="flex items-center space-x-2 px-6 py-2 bg-green-900/30 hover:bg-green-800/30 text-green-400 border border-green-600/50 rounded-lg transition-colors font-mono"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Send size={16} />
                  <span>Submit Test</span>
                </motion.button>
              ) : (
                <motion.button
                  onClick={handleNextQuestion}
                  className="flex items-center space-x-2 px-4 py-2 bg-black hover:bg-gray-900 text-white border border-gray-600 rounded-lg transition-colors font-mono"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span>Next</span>
                  <ArrowRight size={16} />
                </motion.button>
              )}
            </div>
          </div>
        </div>

        {/* Question Navigation Sidebar */}
        <div className="w-80">
          <QuestionNavigation
            questions={questions}
            currentQuestionIndex={currentQuestionIndex}
            answers={answers}
            flaggedQuestions={flaggedQuestions}
            onQuestionSelect={setCurrentQuestionIndex}
            onToggleFlag={handleToggleFlag}
          />
        </div>
      </div>

      {/* Submit Confirmation Modal */}
      <AnimatePresence>
        {showSubmitConfirm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-black/90 backdrop-blur-xl rounded-xl p-6 border border-gray-600/50 max-w-md w-full"
            >
              <div className="text-center">
                <AlertTriangle className="mx-auto text-yellow-400 mb-4" size={48} />
                <h3 className="text-xl font-bold text-white mb-2 font-mono">Submit Test?</h3>
                <p className="text-gray-400 mb-6 font-mono">
                  You have answered {answeredCount} out of {questions.length} questions.
                  {answeredCount < questions.length && ' Some questions are still unanswered.'}
                </p>
                
                <div className="flex space-x-3">
                  <motion.button
                    onClick={() => setShowSubmitConfirm(false)}
                    className="flex-1 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors font-mono"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Continue Test
                  </motion.button>
                  <motion.button
                    onClick={handleSubmitTest}
                    className="flex-1 px-4 py-2 bg-red-900/30 hover:bg-red-800/30 text-red-400 border border-red-600/50 rounded-lg transition-colors font-mono"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Submit Now
                  </motion.button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
