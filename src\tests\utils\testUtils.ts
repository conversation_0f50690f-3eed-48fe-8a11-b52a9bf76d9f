import { CBTUser, Question, Test, TestAttempt, Module, UserRole } from '../../types/cbt.types';

// Mock data generators for testing
export const createMockUser = (overrides: Partial<CBTUser> = {}): CBTUser => ({
  id: 'user_' + Math.random().toString(36).substr(2, 9),
  email: '<EMAIL>',
  name: 'Test User',
  role: 'student',
  avatar: '',
  institution: 'Test University',
  department: 'Computer Science',
  studentId: 'STU001',
  permissions: ['take_tests', 'view_results'],
  isActive: true,
  lastLogin: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides
});

export const createMockQuestion = (overrides: Partial<Question> = {}): Question => ({
  id: 'q_' + Math.random().toString(36).substr(2, 9),
  type: 'multiple_choice',
  title: 'Test Question',
  content: 'What is the correct answer?',
  points: 10,
  difficulty: 'medium',
  timeLimit: 120,
  options: [
    { id: 'a', text: 'Option A', isCorrect: true },
    { id: 'b', text: 'Option B', isCorrect: false },
    { id: 'c', text: 'Option C', isCorrect: false },
    { id: 'd', text: 'Option D', isCorrect: false }
  ],
  category: 'Test Category',
  tags: ['test', 'sample'],
  createdBy: 'instructor1',
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides
});

export const createMockTest = (overrides: Partial<Test> = {}): Test => ({
  id: 'test_' + Math.random().toString(36).substr(2, 9),
  title: 'Test Assessment',
  description: 'A test assessment for testing purposes',
  instructions: 'Answer all questions to the best of your ability.',
  duration: 60,
  totalPoints: 100,
  passingScore: 70,
  maxAttempts: 3,
  shuffleQuestions: false,
  shuffleOptions: false,
  showResults: true,
  allowReview: false,
  isPublished: true,
  questions: [],
  categories: ['Test'],
  difficulty: 'medium',
  estimatedTime: 45,
  createdBy: 'instructor1',
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides
});

export const createMockTestAttempt = (overrides: Partial<TestAttempt> = {}): TestAttempt => ({
  id: 'attempt_' + Math.random().toString(36).substr(2, 9),
  testId: 'test1',
  testTitle: 'Test Assessment',
  studentId: 'student1',
  studentName: 'Test Student',
  attemptNumber: 1,
  status: 'completed',
  startTime: new Date(Date.now() - 3600000), // 1 hour ago
  endTime: new Date(),
  duration: 3600, // 1 hour in seconds
  score: 85,
  totalQuestions: 10,
  correctAnswers: 8,
  incorrectAnswers: 2,
  skippedQuestions: 0,
  passingScore: 70,
  answers: {},
  timeSpentPerQuestion: {},
  flaggedQuestions: [],
  submittedAt: new Date(),
  createdAt: new Date(Date.now() - 3600000),
  updatedAt: new Date(),
  ...overrides
});

export const createMockModule = (overrides: Partial<Module> = {}): Module => ({
  id: 'mod_' + Math.random().toString(36).substr(2, 9),
  title: 'Test Module',
  description: 'A test module for testing purposes',
  code: 'TEST101',
  instructor: 'instructor1',
  tests: [],
  students: ['student1'],
  isActive: true,
  difficulty: 'medium',
  status: 'active',
  semester: 'Fall',
  academicYear: '2024',
  totalStudents: 1,
  completedTests: 0,
  averageScore: 0,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides
});

// Test helper functions
export const waitFor = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

export const mockLocalStorage = () => {
  const store: { [key: string]: string } = {};
  
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach(key => delete store[key]);
    })
  };
};

export const mockElectronAPI = () => ({
  auth: {
    login: jest.fn(),
    logout: jest.fn()
  },
  app: {
    getInfo: jest.fn()
  },
  window: {
    minimize: jest.fn(),
    maximize: jest.fn(),
    close: jest.fn()
  },
  security: {
    enterTestMode: jest.fn(),
    exitTestMode: jest.fn(),
    enableKioskMode: jest.fn(),
    disableKioskMode: jest.fn(),
    forceFullscreen: jest.fn(),
    exitFullscreen: jest.fn(),
    logEvent: jest.fn(),
    onFocusLost: jest.fn(),
    onFocusGained: jest.fn(),
    removeAllListeners: jest.fn()
  }
});

// Security testing utilities
export const simulateSecurityEvent = (type: 'tab_switch' | 'right_click' | 'key_combination' | 'focus_lost') => {
  switch (type) {
    case 'tab_switch':
      // Simulate tab switching
      Object.defineProperty(document, 'hidden', { value: true, writable: true });
      document.dispatchEvent(new Event('visibilitychange'));
      break;
    
    case 'right_click':
      // Simulate right-click
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        cancelable: true,
        button: 2
      });
      document.dispatchEvent(rightClickEvent);
      break;
    
    case 'key_combination':
      // Simulate F12 key press
      const keyEvent = new KeyboardEvent('keydown', {
        key: 'F12',
        bubbles: true,
        cancelable: true
      });
      document.dispatchEvent(keyEvent);
      break;
    
    case 'focus_lost':
      // Simulate window focus loss
      window.dispatchEvent(new Event('blur'));
      break;
  }
};

// Performance testing utilities
export const measurePerformance = async (fn: () => Promise<void> | void): Promise<number> => {
  const start = performance.now();
  await fn();
  const end = performance.now();
  return end - start;
};

export const simulateSlowNetwork = (delay: number = 2000) => {
  const originalFetch = global.fetch;
  global.fetch = jest.fn((...args) => {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(originalFetch(...args));
      }, delay);
    });
  });
  
  return () => {
    global.fetch = originalFetch;
  };
};

// Accessibility testing utilities
export const checkAccessibility = (element: HTMLElement): string[] => {
  const issues: string[] = [];
  
  // Check for missing alt text on images
  const images = element.querySelectorAll('img');
  images.forEach(img => {
    if (!img.alt) {
      issues.push('Image missing alt text');
    }
  });
  
  // Check for missing labels on form inputs
  const inputs = element.querySelectorAll('input, textarea, select');
  inputs.forEach(input => {
    const id = input.getAttribute('id');
    const ariaLabel = input.getAttribute('aria-label');
    const ariaLabelledBy = input.getAttribute('aria-labelledby');
    
    if (!id && !ariaLabel && !ariaLabelledBy) {
      const label = element.querySelector(`label[for="${id}"]`);
      if (!label) {
        issues.push('Form input missing label');
      }
    }
  });
  
  // Check for proper heading hierarchy
  const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
  let previousLevel = 0;
  headings.forEach(heading => {
    const level = parseInt(heading.tagName.charAt(1));
    if (level > previousLevel + 1) {
      issues.push('Heading hierarchy skipped a level');
    }
    previousLevel = level;
  });
  
  // Check for sufficient color contrast (simplified check)
  const textElements = element.querySelectorAll('*');
  textElements.forEach(el => {
    const styles = window.getComputedStyle(el);
    const color = styles.color;
    const backgroundColor = styles.backgroundColor;
    
    // This is a simplified check - in real testing you'd use a proper contrast ratio calculator
    if (color === backgroundColor) {
      issues.push('Insufficient color contrast detected');
    }
  });
  
  return issues;
};

// Data validation utilities
export const validateTestData = (test: Test): string[] => {
  const errors: string[] = [];
  
  if (!test.title || test.title.trim().length === 0) {
    errors.push('Test title is required');
  }
  
  if (!test.description || test.description.trim().length === 0) {
    errors.push('Test description is required');
  }
  
  if (test.duration <= 0) {
    errors.push('Test duration must be positive');
  }
  
  if (test.totalPoints <= 0) {
    errors.push('Total points must be positive');
  }
  
  if (test.passingScore < 0 || test.passingScore > 100) {
    errors.push('Passing score must be between 0 and 100');
  }
  
  if (test.maxAttempts <= 0) {
    errors.push('Max attempts must be positive');
  }
  
  if (!test.questions || test.questions.length === 0) {
    errors.push('Test must have at least one question');
  }
  
  return errors;
};

export const validateQuestionData = (question: Question): string[] => {
  const errors: string[] = [];
  
  if (!question.title || question.title.trim().length === 0) {
    errors.push('Question title is required');
  }
  
  if (!question.content || question.content.trim().length === 0) {
    errors.push('Question content is required');
  }
  
  if (question.points <= 0) {
    errors.push('Question points must be positive');
  }
  
  if (question.type === 'multiple_choice') {
    if (!question.options || question.options.length < 2) {
      errors.push('Multiple choice questions must have at least 2 options');
    }
    
    const correctOptions = question.options?.filter(opt => opt.isCorrect) || [];
    if (correctOptions.length === 0) {
      errors.push('Multiple choice questions must have at least one correct option');
    }
  }
  
  if (question.type === 'true_false') {
    if (!question.correctAnswer || !['true', 'false'].includes(question.correctAnswer as string)) {
      errors.push('True/false questions must have a correct answer of true or false');
    }
  }
  
  return errors;
};

// Export all utilities
export default {
  createMockUser,
  createMockQuestion,
  createMockTest,
  createMockTestAttempt,
  createMockModule,
  waitFor,
  mockLocalStorage,
  mockElectronAPI,
  simulateSecurityEvent,
  measurePerformance,
  simulateSlowNetwork,
  checkAccessibility,
  validateTestData,
  validateQuestionData
};
