import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON>, 
  Bell, 
  User, 
  Settings,
  Maximize2,
  Minimize2,
  X,
  Shield,
  Clock,
  Wifi,
  Battery,
  Monitor
} from 'lucide-react';
import { CBTUser } from '../../types/cbt.types';

interface CBTHeaderProps {
  user: CBTUser;
  pageTitle: string;
  onSearch?: (query: string) => void;
  onNotificationClick?: () => void;
  onProfileClick?: () => void;
}

export const CBTHeader: React.FC<CBTHeaderProps> = ({
  user,
  pageTitle,
  onSearch,
  onNotificationClick,
  onProfileClick
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [notifications] = useState(3); // Mock notification count
  const isElectron = typeof window !== 'undefined' && window.electronAPI;

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Check fullscreen status
  useEffect(() => {
    const checkFullscreen = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', checkFullscreen);
    return () => document.removeEventListener('fullscreenchange', checkFullscreen);
  }, []);

  const handleWindowControl = async (action: 'minimize' | 'maximize' | 'close') => {
    if (isElectron) {
      try {
        switch (action) {
          case 'minimize':
            await window.electronAPI.window.minimize();
            break;
          case 'maximize':
            await window.electronAPI.window.maximize();
            break;
          case 'close':
            await window.electronAPI.window.close();
            break;
        }
      } catch (error) {
        console.error('Window control error:', error);
      }
    }
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch?.(searchQuery);
  };

  const toggleFullscreen = async () => {
    try {
      if (!document.fullscreenElement) {
        await document.documentElement.requestFullscreen();
      } else {
        await document.exitFullscreen();
      }
    } catch (error) {
      console.error('Fullscreen toggle error:', error);
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'text-red-400';
      case 'instructor': return 'text-yellow-400';
      case 'student': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };

  const getSecurityStatus = () => {
    // Mock security status - in real app, this would check actual security measures
    return {
      secure: true,
      level: 'HIGH',
      features: ['Anti-cheat', 'Screen monitoring', 'Network secured']
    };
  };

  const securityStatus = getSecurityStatus();

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className="bg-black/90 backdrop-blur-lg border-b border-gray-700/50 px-6 py-4 relative z-20"
    >
      <div className="flex items-center justify-between">
        {/* Left side - Page title and breadcrumb */}
        <div className="flex items-center space-x-4">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-3"
          >
            <div className="w-8 h-8 bg-gradient-to-br from-gray-600 to-black rounded-lg flex items-center justify-center">
              <Shield className="text-white" size={16} />
            </div>
            <div>
              <h1 className="text-xl font-bold text-white font-mono">{pageTitle}</h1>
              <div className="flex items-center space-x-2 text-xs text-gray-400 font-mono">
                <span>CBT System</span>
                <span>•</span>
                <span className={getRoleColor(user.role)}>{user.role.toUpperCase()}</span>
                {securityStatus.secure && (
                  <>
                    <span>•</span>
                    <span className="text-green-400">SECURE</span>
                  </>
                )}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Center - Search */}
        <div className="flex-1 max-w-md mx-8">
          <form onSubmit={handleSearchSubmit} className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <input
              type="text"
              placeholder="Search tests, modules, users..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent font-mono text-sm"
            />
          </form>
        </div>

        {/* Right side - Status and controls */}
        <div className="flex items-center space-x-4">
          {/* System Status */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="hidden lg:flex items-center space-x-4 text-xs text-gray-400 font-mono"
          >
            <div className="flex items-center space-x-1">
              <Clock size={14} />
              <span>{currentTime.toLocaleTimeString()}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Wifi size={14} />
              <span>Connected</span>
            </div>
            <div className="flex items-center space-x-1">
              <Monitor size={14} />
              <span>{isFullscreen ? 'Fullscreen' : 'Windowed'}</span>
            </div>
          </motion.div>

          {/* Security Indicator */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="flex items-center space-x-2 px-3 py-1 bg-green-900/30 border border-green-600/50 rounded-lg"
          >
            <Shield className="text-green-400" size={14} />
            <span className="text-green-400 text-xs font-mono font-bold">{securityStatus.level}</span>
          </motion.div>

          {/* Notifications */}
          <motion.button
            onClick={onNotificationClick}
            className="relative p-2 rounded-lg bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 hover:text-white transition-all duration-200"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Bell size={18} />
            {notifications > 0 && (
              <motion.span
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-mono"
              >
                {notifications}
              </motion.span>
            )}
          </motion.button>

          {/* Fullscreen Toggle */}
          <motion.button
            onClick={toggleFullscreen}
            className="p-2 rounded-lg bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 hover:text-white transition-all duration-200"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            title={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
          >
            {isFullscreen ? <Minimize2 size={18} /> : <Maximize2 size={18} />}
          </motion.button>

          {/* User Profile */}
          <motion.button
            onClick={onProfileClick}
            className="flex items-center space-x-3 p-2 rounded-lg bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 group"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="w-8 h-8 bg-gradient-to-br from-gray-600 to-gray-800 rounded-full flex items-center justify-center">
              <User className="text-white" size={16} />
            </div>
            <div className="hidden md:block text-left">
              <p className="text-white text-sm font-medium font-mono">{user.name}</p>
              <p className="text-gray-400 text-xs font-mono">{user.email}</p>
            </div>
          </motion.button>

          {/* Window Controls (Electron only) */}
          {isElectron && (
            <div className="flex items-center space-x-1 ml-4">
              <motion.button
                onClick={() => handleWindowControl('minimize')}
                className="w-6 h-6 rounded bg-yellow-500/20 hover:bg-yellow-500/40 flex items-center justify-center transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Minimize2 size={12} className="text-yellow-400" />
              </motion.button>
              <motion.button
                onClick={() => handleWindowControl('maximize')}
                className="w-6 h-6 rounded bg-green-500/20 hover:bg-green-500/40 flex items-center justify-center transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Maximize2 size={12} className="text-green-400" />
              </motion.button>
              <motion.button
                onClick={() => handleWindowControl('close')}
                className="w-6 h-6 rounded bg-red-500/20 hover:bg-red-500/40 flex items-center justify-center transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <X size={12} className="text-red-400" />
              </motion.button>
            </div>
          )}
        </div>
      </div>
    </motion.header>
  );
};
