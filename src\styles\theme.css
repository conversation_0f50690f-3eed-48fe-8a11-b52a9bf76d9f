/* Theme System CSS Variables and Utilities */

/* Root CSS Variables */
:root {
  /* Default Light Theme Colors */
  --color-primary: #2563eb;
  --color-secondary: #64748b;
  --color-accent: #0ea5e9;
  --color-background: #ffffff;
  --color-surface: #f8fafc;
  --color-text: #1e293b;
  --color-text-secondary: #64748b;
  --color-border: #e2e8f0;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  /* Animation Durations */
  --animation-duration-fast: 0.15s;
  --animation-duration-normal: 0.3s;
  --animation-duration-slow: 0.6s;

  /* Font Sizes */
  --font-size-base: 16px;
  --font-family-mono: 'Space Mono', 'Courier New', monospace;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Dark Theme */
.theme-dark {
  --color-primary: #3b82f6;
  --color-secondary: #94a3b8;
  --color-accent: #06b6d4;
  --color-background: #0f172a;
  --color-surface: #1e293b;
  --color-text: #f1f5f9;
  --color-text-secondary: #94a3b8;
  --color-border: #334155;
  --color-success: #22c55e;
  --color-warning: #fbbf24;
  --color-error: #f87171;
  --color-info: #60a5fa;

  /* Dark theme shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);
}

/* Font Size Variants */
.font-small {
  --font-size-base: 14px;
}

.font-medium {
  --font-size-base: 16px;
}

.font-large {
  --font-size-base: 18px;
}

/* Reduced Motion */
.reduced-motion,
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* High Contrast Mode */
.high-contrast {
  --color-primary: #000000;
  --color-secondary: #666666;
  --color-accent: #0000ff;
  --color-background: #ffffff;
  --color-surface: #f0f0f0;
  --color-text: #000000;
  --color-text-secondary: #333333;
  --color-border: #000000;
  --color-success: #008000;
  --color-warning: #ff8c00;
  --color-error: #ff0000;
  --color-info: #0000ff;
}

.high-contrast.theme-dark {
  --color-primary: #ffffff;
  --color-secondary: #cccccc;
  --color-accent: #00ffff;
  --color-background: #000000;
  --color-surface: #1a1a1a;
  --color-text: #ffffff;
  --color-text-secondary: #cccccc;
  --color-border: #ffffff;
  --color-success: #00ff00;
  --color-warning: #ffff00;
  --color-error: #ff0000;
  --color-info: #00ffff;
}

/* Base Styles */
body {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-base);
  background-color: var(--color-background);
  color: var(--color-text);
  transition: background-color var(--animation-duration-normal) ease,
              color var(--animation-duration-normal) ease;
}

/* Utility Classes */
.theme-transition {
  transition: all var(--animation-duration-normal) ease;
}

.theme-bg-primary {
  background-color: var(--color-primary);
}

.theme-bg-secondary {
  background-color: var(--color-secondary);
}

.theme-bg-surface {
  background-color: var(--color-surface);
}

.theme-text-primary {
  color: var(--color-primary);
}

.theme-text-secondary {
  color: var(--color-text-secondary);
}

.theme-border {
  border-color: var(--color-border);
}

.theme-shadow-sm {
  box-shadow: var(--shadow-sm);
}

.theme-shadow-md {
  box-shadow: var(--shadow-md);
}

.theme-shadow-lg {
  box-shadow: var(--shadow-lg);
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn var(--animation-duration-normal) ease-in-out;
}

.animate-slide-up {
  animation: slideUp var(--animation-duration-normal) ease-out;
}

.animate-slide-down {
  animation: slideDown var(--animation-duration-normal) ease-out;
}

.animate-scale-in {
  animation: scaleIn var(--animation-duration-fast) ease-out;
}

/* Keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Component-specific styles */
.cbt-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-base);
  cursor: pointer;
  transition: all var(--animation-duration-fast) ease;
  box-shadow: var(--shadow-sm);
}

.cbt-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.cbt-button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.cbt-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--animation-duration-normal) ease;
}

.cbt-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.cbt-input {
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-base);
  color: var(--color-text);
  transition: all var(--animation-duration-fast) ease;
}

.cbt-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  :root {
    --font-size-base: 14px;
    --spacing-md: 0.75rem;
    --spacing-lg: 1rem;
  }
}

@media (max-width: 480px) {
  :root {
    --font-size-base: 13px;
    --spacing-sm: 0.375rem;
    --spacing-md: 0.5rem;
    --spacing-lg: 0.75rem;
  }
}

/* Print Styles */
@media print {
  .theme-transition,
  .animate-fade-in,
  .animate-slide-up,
  .animate-slide-down,
  .animate-scale-in {
    animation: none !important;
    transition: none !important;
  }
  
  .cbt-card {
    box-shadow: none;
    border: 1px solid #000;
  }
}
