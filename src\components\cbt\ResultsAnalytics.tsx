import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Clock, 
  Award, 
  Target,
  Download,
  Filter,
  Calendar,
  Eye,
  FileText,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { useCBT } from '../../contexts/CBTContext';
import { TestAttempt, TestAnalytics, UserRole } from '../../types/cbt.types';
import { cbtService } from '../../services/cbt.service';

interface StatsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
  };
  icon: React.ComponentType<{ size?: number }>;
  color: string;
}

const StatsCard: React.FC<StatsCardProps> = ({ title, value, change, icon: Icon, color }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ scale: 1.02 }}
      className="bg-black/60 backdrop-blur-xl rounded-xl p-6 border border-gray-600/50"
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-gray-400 text-sm font-mono mb-1">{title}</p>
          <p className="text-2xl font-bold text-white font-mono">{value}</p>
          {change && (
            <div className={`flex items-center space-x-1 mt-2 ${
              change.type === 'increase' ? 'text-green-400' : 'text-red-400'
            }`}>
              {change.type === 'increase' ? (
                <TrendingUp size={14} />
              ) : (
                <TrendingDown size={14} />
              )}
              <span className="text-xs font-mono">{change.value}%</span>
            </div>
          )}
        </div>
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${color}`}>
          <Icon size={24} />
        </div>
      </div>
    </motion.div>
  );
};

interface TestResultCardProps {
  attempt: TestAttempt;
  userRole: UserRole;
  onViewDetails: (attempt: TestAttempt) => void;
}

const TestResultCard: React.FC<TestResultCardProps> = ({ attempt, userRole, onViewDetails }) => {
  const getScoreColor = (score: number, passingScore: number) => {
    if (score >= passingScore) return 'text-green-400';
    if (score >= passingScore * 0.7) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="text-green-400" size={16} />;
      case 'in_progress': return <AlertCircle className="text-yellow-400" size={16} />;
      case 'abandoned': return <XCircle className="text-red-400" size={16} />;
      default: return <AlertCircle className="text-gray-400" size={16} />;
    }
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    }
    return `${minutes}m ${secs}s`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ scale: 1.01 }}
      className="bg-black/60 backdrop-blur-xl rounded-lg p-4 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-300"
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            {getStatusIcon(attempt.status)}
            <h4 className="text-white font-medium font-mono">{attempt.testTitle || 'Test'}</h4>
            <span className="px-2 py-1 bg-gray-800 text-gray-300 rounded text-xs font-mono">
              Attempt {attempt.attemptNumber}
            </span>
          </div>
          
          {userRole !== 'student' && (
            <p className="text-gray-400 text-sm font-mono mb-2">
              Student: {attempt.studentName || 'Unknown'}
            </p>
          )}
          
          <div className="flex items-center space-x-4 text-xs text-gray-500 font-mono">
            <span>Started: {new Date(attempt.startTime).toLocaleDateString()}</span>
            {attempt.endTime && (
              <span>Completed: {new Date(attempt.endTime).toLocaleDateString()}</span>
            )}
          </div>
        </div>
        
        <div className="text-right">
          <div className={`text-2xl font-bold font-mono ${getScoreColor(attempt.score, attempt.passingScore || 70)}`}>
            {attempt.score.toFixed(1)}%
          </div>
          <div className="text-gray-400 text-xs font-mono">
            {attempt.correctAnswers}/{attempt.totalQuestions} correct
          </div>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4 mb-4">
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 text-gray-400 mb-1">
            <Clock size={12} />
            <span className="text-xs font-mono">Duration</span>
          </div>
          <p className="text-white text-sm font-mono">
            {attempt.duration ? formatDuration(attempt.duration) : 'N/A'}
          </p>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 text-gray-400 mb-1">
            <Target size={12} />
            <span className="text-xs font-mono">Pass Rate</span>
          </div>
          <p className="text-white text-sm font-mono">
            {attempt.passingScore || 70}%
          </p>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 text-gray-400 mb-1">
            <Award size={12} />
            <span className="text-xs font-mono">Status</span>
          </div>
          <p className={`text-sm font-mono ${
            attempt.score >= (attempt.passingScore || 70) ? 'text-green-400' : 'text-red-400'
          }`}>
            {attempt.score >= (attempt.passingScore || 70) ? 'PASSED' : 'FAILED'}
          </p>
        </div>
      </div>

      <div className="flex space-x-2">
        <motion.button
          onClick={() => onViewDetails(attempt)}
          className="flex-1 px-3 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors font-mono text-sm"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Eye size={14} className="inline mr-2" />
          View Details
        </motion.button>
        
        <motion.button
          className="px-3 py-2 bg-black hover:bg-gray-900 text-white rounded-lg transition-colors font-mono text-sm border border-gray-600"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Download size={14} />
        </motion.button>
      </div>
    </motion.div>
  );
};

export const ResultsAnalytics: React.FC = () => {
  const { state } = useCBT();
  const [attempts, setAttempts] = useState<TestAttempt[]>([]);
  const [analytics, setAnalytics] = useState<TestAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterDateRange, setFilterDateRange] = useState<string>('all');
  const [selectedAttempt, setSelectedAttempt] = useState<TestAttempt | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load test attempts based on user role
      let attemptsResponse;
      if (state.user?.role === 'student') {
        attemptsResponse = await cbtService.getStudentAttempts(state.user.id);
      } else {
        attemptsResponse = await cbtService.getAllAttempts();
      }
      
      if (attemptsResponse.success && attemptsResponse.data) {
        setAttempts(attemptsResponse.data);
      }

      // Load analytics for instructors and admins
      if (state.user?.role !== 'student') {
        const analyticsResponse = await cbtService.getTestAnalytics();
        if (analyticsResponse.success && analyticsResponse.data) {
          setAnalytics(analyticsResponse.data);
        }
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetails = (attempt: TestAttempt) => {
    setSelectedAttempt(attempt);
  };

  const filteredAttempts = attempts.filter(attempt => {
    const statusMatch = filterStatus === 'all' || attempt.status === filterStatus;
    
    let dateMatch = true;
    if (filterDateRange !== 'all') {
      const now = new Date();
      const attemptDate = new Date(attempt.startTime);
      
      switch (filterDateRange) {
        case 'today':
          dateMatch = attemptDate.toDateString() === now.toDateString();
          break;
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          dateMatch = attemptDate >= weekAgo;
          break;
        case 'month':
          const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          dateMatch = attemptDate >= monthAgo;
          break;
      }
    }
    
    return statusMatch && dateMatch;
  });

  const getOverallStats = () => {
    if (filteredAttempts.length === 0) {
      return {
        totalAttempts: 0,
        averageScore: 0,
        passRate: 0,
        averageDuration: 0
      };
    }

    const completedAttempts = filteredAttempts.filter(a => a.status === 'completed');
    const totalScore = completedAttempts.reduce((sum, a) => sum + a.score, 0);
    const passedAttempts = completedAttempts.filter(a => a.score >= (a.passingScore || 70));
    const totalDuration = completedAttempts.reduce((sum, a) => sum + (a.duration || 0), 0);

    return {
      totalAttempts: filteredAttempts.length,
      averageScore: completedAttempts.length > 0 ? totalScore / completedAttempts.length : 0,
      passRate: completedAttempts.length > 0 ? (passedAttempts.length / completedAttempts.length) * 100 : 0,
      averageDuration: completedAttempts.length > 0 ? totalDuration / completedAttempts.length : 0
    };
  };

  const stats = getOverallStats();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          className="w-8 h-8 border-2 border-gray-600 border-t-white rounded-full"
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white font-mono">
            {state.user?.role === 'student' ? 'My Test Results' : 'Test Results & Analytics'}
          </h2>
          <p className="text-gray-400 font-mono">
            {state.user?.role === 'student' 
              ? 'View your test performance and progress'
              : 'Comprehensive test performance analytics and insights'
            }
          </p>
        </div>
        
        <div className="flex space-x-3">
          <motion.button
            className="px-4 py-2 bg-black hover:bg-gray-900 text-white rounded-lg transition-colors font-mono border border-gray-600"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Download size={16} className="inline mr-2" />
            Export Report
          </motion.button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Attempts"
          value={stats.totalAttempts}
          icon={FileText}
          color="bg-gray-800"
        />
        <StatsCard
          title="Average Score"
          value={`${stats.averageScore.toFixed(1)}%`}
          change={{ value: 5.2, type: 'increase' }}
          icon={BarChart3}
          color="bg-gray-800"
        />
        <StatsCard
          title="Pass Rate"
          value={`${stats.passRate.toFixed(1)}%`}
          change={{ value: 2.1, type: 'increase' }}
          icon={Award}
          color="bg-gray-800"
        />
        <StatsCard
          title="Avg Duration"
          value={`${Math.floor(stats.averageDuration / 60)}m`}
          change={{ value: 1.8, type: 'decrease' }}
          icon={Clock}
          color="bg-gray-800"
        />
      </div>

      {/* Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="flex space-x-4">
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-4 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
          >
            <option value="all">All Status</option>
            <option value="completed">Completed</option>
            <option value="in_progress">In Progress</option>
            <option value="abandoned">Abandoned</option>
          </select>
          
          <select
            value={filterDateRange}
            onChange={(e) => setFilterDateRange(e.target.value)}
            className="px-4 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
          >
            <option value="all">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
          </select>
        </div>
        
        <div className="flex items-center space-x-2 text-gray-400 font-mono text-sm">
          <span>Showing {filteredAttempts.length} of {attempts.length} results</span>
        </div>
      </div>

      {/* Results Grid */}
      {filteredAttempts.length === 0 ? (
        <div className="text-center py-12">
          <BarChart3 className="mx-auto text-gray-600 mb-4" size={48} />
          <h3 className="text-xl font-bold text-gray-400 mb-2 font-mono">No results found</h3>
          <p className="text-gray-500 font-mono">Try adjusting your filters or take a test to see results</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredAttempts.map((attempt, index) => (
            <motion.div
              key={attempt.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <TestResultCard
                attempt={attempt}
                userRole={state.user?.role || 'student'}
                onViewDetails={handleViewDetails}
              />
            </motion.div>
          ))}
        </div>
      )}

      {/* Analytics Section for Instructors/Admins */}
      {state.user?.role !== 'student' && analytics && (
        <div className="mt-8">
          <h3 className="text-xl font-bold text-white mb-6 font-mono">Detailed Analytics</h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Score Distribution Chart Placeholder */}
            <div className="bg-black/60 backdrop-blur-xl rounded-xl p-6 border border-gray-600/50">
              <h4 className="text-lg font-bold text-white mb-4 font-mono">Score Distribution</h4>
              <div className="h-64 bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-lg flex items-center justify-center">
                <p className="text-gray-400 font-mono">Chart visualization would go here</p>
              </div>
            </div>

            {/* Question Performance */}
            <div className="bg-black/60 backdrop-blur-xl rounded-xl p-6 border border-gray-600/50">
              <h4 className="text-lg font-bold text-white mb-4 font-mono">Question Performance</h4>
              <div className="space-y-3">
                {analytics.questionAnalytics?.slice(0, 5).map((qa, index) => (
                  <div key={qa.questionId} className="flex items-center justify-between">
                    <span className="text-gray-400 font-mono text-sm">Question {index + 1}</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 h-2 bg-gray-800 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-gradient-to-r from-red-500 to-green-500"
                          style={{ 
                            width: `${(qa.correctAnswers / (qa.correctAnswers + qa.incorrectAnswers)) * 100}%` 
                          }}
                        />
                      </div>
                      <span className="text-white font-mono text-sm">
                        {Math.round((qa.correctAnswers / (qa.correctAnswers + qa.incorrectAnswers)) * 100)}%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
