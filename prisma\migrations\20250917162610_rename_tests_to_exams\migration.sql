/*
  Warnings:

  - You are about to drop the `test_analytics` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `test_attempts` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `test_questions` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `test_sessions` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `tests` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropIndex
DROP INDEX "test_analytics_test_id_key";

-- DropIndex
DROP INDEX "test_attempts_session_id_key";

-- DropIndex
DROP INDEX "test_questions_test_id_question_id_key";

-- DropIndex
DROP INDEX "test_sessions_session_token_key";

-- DropTable
PRAGMA foreign_keys=off;
DROP TABLE "test_analytics";
PRAGMA foreign_keys=on;

-- DropTable
PRAGMA foreign_keys=off;
DROP TABLE "test_attempts";
PRAGMA foreign_keys=on;

-- DropTable
PRAGMA foreign_keys=off;
DROP TABLE "test_questions";
PRAGMA foreign_keys=on;

-- DropTable
PRAGMA foreign_keys=off;
DROP TABLE "test_sessions";
PRAGMA foreign_keys=on;

-- DropTable
PRAGMA foreign_keys=off;
DROP TABLE "tests";
PRAGMA foreign_keys=on;

-- CreateTable
CREATE TABLE "exams" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "instructions" TEXT,
    "module_id" TEXT,
    "duration" INTEGER NOT NULL,
    "total_points" INTEGER NOT NULL,
    "passing_score" INTEGER NOT NULL,
    "max_attempts" INTEGER NOT NULL DEFAULT 1,
    "shuffle_questions" BOOLEAN NOT NULL DEFAULT false,
    "shuffle_options" BOOLEAN NOT NULL DEFAULT false,
    "show_results" BOOLEAN NOT NULL DEFAULT true,
    "allow_review" BOOLEAN NOT NULL DEFAULT false,
    "is_published" BOOLEAN NOT NULL DEFAULT false,
    "start_date" DATETIME,
    "end_date" DATETIME,
    "difficulty" TEXT NOT NULL DEFAULT 'medium',
    "estimated_time" INTEGER,
    "created_by" TEXT NOT NULL,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    "sync_timestamp" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "sync_node_id" TEXT,
    "sync_version" INTEGER NOT NULL DEFAULT 1,
    "deleted_at" DATETIME,
    CONSTRAINT "exams_module_id_fkey" FOREIGN KEY ("module_id") REFERENCES "modules" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT "exams_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "exam_questions" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "test_id" TEXT NOT NULL,
    "question_id" TEXT NOT NULL,
    "order_index" INTEGER NOT NULL,
    "points_override" INTEGER,
    CONSTRAINT "exam_questions_test_id_fkey" FOREIGN KEY ("test_id") REFERENCES "exams" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "exam_questions_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "questions" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "exam_sessions" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "test_id" TEXT NOT NULL,
    "student_id" TEXT NOT NULL,
    "session_token" TEXT NOT NULL,
    "start_time" DATETIME NOT NULL,
    "end_time" DATETIME,
    "expiry_time" DATETIME NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "current_question_index" INTEGER NOT NULL DEFAULT 0,
    "time_remaining" INTEGER,
    "security_settings" TEXT,
    "ip_address" TEXT,
    "user_agent" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    "sync_timestamp" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "sync_node_id" TEXT,
    "sync_version" INTEGER NOT NULL DEFAULT 1,
    "deleted_at" DATETIME,
    CONSTRAINT "exam_sessions_test_id_fkey" FOREIGN KEY ("test_id") REFERENCES "exams" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "exam_sessions_student_id_fkey" FOREIGN KEY ("student_id") REFERENCES "users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "exam_attempts" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "test_id" TEXT NOT NULL,
    "student_id" TEXT NOT NULL,
    "session_id" TEXT NOT NULL,
    "attempt_number" INTEGER NOT NULL,
    "start_time" DATETIME NOT NULL,
    "end_time" DATETIME,
    "duration" INTEGER,
    "score" REAL NOT NULL,
    "total_questions" INTEGER NOT NULL,
    "correct_answers" INTEGER NOT NULL,
    "incorrect_answers" INTEGER NOT NULL,
    "skipped_questions" INTEGER NOT NULL,
    "percentage" REAL NOT NULL,
    "passed" BOOLEAN NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'in_progress',
    "ip_address" TEXT,
    "user_agent" TEXT,
    "submitted_at" DATETIME,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    "sync_timestamp" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "sync_node_id" TEXT,
    "sync_version" INTEGER NOT NULL DEFAULT 1,
    "deleted_at" DATETIME,
    CONSTRAINT "exam_attempts_test_id_fkey" FOREIGN KEY ("test_id") REFERENCES "exams" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "exam_attempts_student_id_fkey" FOREIGN KEY ("student_id") REFERENCES "users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "exam_attempts_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "exam_sessions" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "exam_analytics" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "test_id" TEXT NOT NULL,
    "total_attempts" INTEGER NOT NULL DEFAULT 0,
    "average_score" REAL NOT NULL DEFAULT 0.00,
    "pass_rate" REAL NOT NULL DEFAULT 0.00,
    "average_duration" INTEGER NOT NULL DEFAULT 0,
    "difficulty_rating" REAL NOT NULL DEFAULT 0.00,
    "last_calculated" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "exam_analytics_test_id_fkey" FOREIGN KEY ("test_id") REFERENCES "exams" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_security_events" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "session_id" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "details" TEXT,
    "severity" TEXT NOT NULL DEFAULT 'medium',
    "timestamp" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "security_events_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "exam_sessions" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
INSERT INTO "new_security_events" ("details", "id", "session_id", "severity", "timestamp", "type") SELECT "details", "id", "session_id", "severity", "timestamp", "type" FROM "security_events";
DROP TABLE "security_events";
ALTER TABLE "new_security_events" RENAME TO "security_events";
CREATE TABLE "new_student_answers" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "session_id" TEXT NOT NULL,
    "question_id" TEXT NOT NULL,
    "student_id" TEXT NOT NULL,
    "answer" TEXT,
    "answer_data" TEXT,
    "time_spent" INTEGER NOT NULL,
    "is_correct" BOOLEAN,
    "points_earned" REAL,
    "is_flagged" BOOLEAN NOT NULL DEFAULT false,
    "submitted_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "student_answers_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "exam_sessions" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "student_answers_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "questions" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "student_answers_student_id_fkey" FOREIGN KEY ("student_id") REFERENCES "users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_student_answers" ("answer", "answer_data", "id", "is_correct", "is_flagged", "points_earned", "question_id", "session_id", "student_id", "submitted_at", "time_spent") SELECT "answer", "answer_data", "id", "is_correct", "is_flagged", "points_earned", "question_id", "session_id", "student_id", "submitted_at", "time_spent" FROM "student_answers";
DROP TABLE "student_answers";
ALTER TABLE "new_student_answers" RENAME TO "student_answers";
CREATE UNIQUE INDEX "student_answers_session_id_question_id_key" ON "student_answers"("session_id", "question_id");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;

-- CreateIndex
CREATE UNIQUE INDEX "exam_questions_test_id_question_id_key" ON "exam_questions"("test_id", "question_id");

-- CreateIndex
CREATE UNIQUE INDEX "exam_sessions_session_token_key" ON "exam_sessions"("session_token");

-- CreateIndex
CREATE UNIQUE INDEX "exam_attempts_session_id_key" ON "exam_attempts"("session_id");

-- CreateIndex
CREATE UNIQUE INDEX "exam_analytics_test_id_key" ON "exam_analytics"("test_id");
