import React from 'react';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';

interface StatsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
  };
  icon: LucideIcon;
  color: 'blue' | 'green' | 'purple' | 'orange' | 'red';
  delay?: number;
}

const colorClasses = {
  blue: {
    bg: 'from-slate-700 to-slate-800',
    icon: 'bg-slate-200 text-slate-700',
    change: 'text-slate-400'
  },
  green: {
    bg: 'from-gray-700 to-gray-800',
    icon: 'bg-gray-200 text-gray-700',
    change: 'text-gray-400'
  },
  purple: {
    bg: 'from-slate-800 to-gray-900',
    icon: 'bg-slate-300 text-slate-800',
    change: 'text-slate-400'
  },
  orange: {
    bg: 'from-gray-800 to-slate-900',
    icon: 'bg-gray-300 text-gray-800',
    change: 'text-gray-400'
  },
  red: {
    bg: 'from-slate-700 to-gray-800',
    icon: 'bg-slate-200 text-slate-700',
    change: 'text-slate-400'
  }
};

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  change,
  icon: Icon,
  color,
  delay = 0
}) => {
  const colors = colorClasses[color];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      whileHover={{ scale: 1.02, y: -5 }}
      className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 shadow-lg"
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-gray-300 text-sm font-medium mb-1">{title}</p>
          <motion.p
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: delay + 0.2 }}
            className="text-3xl font-bold text-white mb-2"
          >
            {value}
          </motion.p>
          {change && (
            <div className="flex items-center">
              <span
                className={`text-sm font-medium ${
                  change.type === 'increase' ? 'text-green-400' : 'text-red-400'
                }`}
              >
                {change.type === 'increase' ? '+' : '-'}{Math.abs(change.value)}%
              </span>
              <span className="text-gray-400 text-sm ml-1">vs last month</span>
            </div>
          )}
        </div>
        <motion.div
          initial={{ rotate: -180, opacity: 0 }}
          animate={{ rotate: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: delay + 0.1 }}
          className={`p-3 rounded-lg bg-gradient-to-r ${colors.bg}`}
        >
          <Icon size={24} className="text-white" />
        </motion.div>
      </div>
    </motion.div>
  );
};
