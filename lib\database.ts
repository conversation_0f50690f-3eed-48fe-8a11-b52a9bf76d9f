import { PrismaClient } from '@prisma/client';
import path from 'path';
import fs from 'fs';

// Ensure data directory exists
const dataDir = path.join(process.cwd(), 'data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// Global variable to store the Prisma client instance
declare global {
  var __prisma: PrismaClient | undefined;
}

// Create a single instance of Prisma client
const prisma = globalThis.__prisma || new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL || 'file:./data/cbt_system.db'
    }
  }
});

// In development, store the client on the global object to prevent multiple instances
if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma;
}

// Database connection helper
export async function connectDatabase() {
  try {
    await prisma.$connect();
    console.log('✅ Database connected successfully');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
}

// Database disconnection helper
export async function disconnectDatabase() {
  try {
    await prisma.$disconnect();
    console.log('✅ Database disconnected successfully');
  } catch (error) {
    console.error('❌ Database disconnection failed:', error);
  }
}

// Database health check
export async function checkDatabaseHealth() {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return { status: 'healthy', message: 'Database is responding' };
  } catch (error) {
    return { status: 'unhealthy', message: 'Database is not responding', error };
  }
}

// Initialize database with default data
export async function initializeDatabase() {
  try {
    // Check if admin user exists
    const adminExists = await prisma.user.findFirst({
      where: { role: 'admin' }
    });

    if (!adminExists) {
      // Create default admin user
      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash('admin123', 12);

      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'admin',
          passwordHash: hashedPassword,
          firstName: 'System',
          lastName: 'Administrator',
          role: 'admin',
          isActive: true,
          isVerified: true,
          kycStatus: 'verified'
        }
      });

      console.log('✅ Default admin user created');
    }

    // Create sample instructor if not exists
    const instructorExists = await prisma.user.findFirst({
      where: { role: 'instructor' }
    });

    if (!instructorExists) {
      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash('instructor123', 12);

      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'instructor',
          passwordHash: hashedPassword,
          firstName: 'John',
          lastName: 'Instructor',
          role: 'instructor',
          isActive: true,
          isVerified: true,
          kycStatus: 'verified',
          employeeId: 'INS001'
        }
      });

      console.log('✅ Sample instructor created');
    }

    // Create sample student if not exists
    const studentExists = await prisma.user.findFirst({
      where: { role: 'student' }
    });

    if (!studentExists) {
      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash('student123', 12);

      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'student',
          passwordHash: hashedPassword,
          firstName: 'Jane',
          lastName: 'Student',
          role: 'student',
          isActive: true,
          isVerified: true,
          kycStatus: 'verified',
          studentId: 'STU001'
        }
      });

      console.log('✅ Sample student created');
    }

    // Create system settings if not exists
    const settingsExist = await prisma.systemSetting.findFirst();
    if (!settingsExist) {
      await prisma.systemSetting.createMany({
        data: [
          {
            settingKey: 'system_name',
            settingValue: 'CBT System',
            settingType: 'string',
            description: 'System name displayed in the interface',
            isPublic: true
          },
          {
            settingKey: 'max_test_duration',
            settingValue: '180',
            settingType: 'number',
            description: 'Maximum test duration in minutes',
            isPublic: false
          },
          {
            settingKey: 'auto_save_interval',
            settingValue: '30',
            settingType: 'number',
            description: 'Auto-save interval in seconds',
            isPublic: true
          },
          {
            settingKey: 'security_monitoring',
            settingValue: 'true',
            settingType: 'boolean',
            description: 'Enable security monitoring during tests',
            isPublic: false
          }
        ]
      });

      console.log('✅ System settings initialized');
    }

    return { success: true, message: 'Database initialized successfully' };
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    return { success: false, message: 'Database initialization failed', error };
  }
}

// Graceful shutdown
process.on('beforeExit', async () => {
  await disconnectDatabase();
});

process.on('SIGINT', async () => {
  await disconnectDatabase();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await disconnectDatabase();
  process.exit(0);
});

export default prisma;
