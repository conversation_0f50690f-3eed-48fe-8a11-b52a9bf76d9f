import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Monitor, 
  Users, 
  Database, 
  Activity, 
  Settings, 
  Shield, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  Download,
  Upload,
  Wifi,
  Server
} from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { SyncManager } from '../sync/SyncManager';
import { clientDatabaseService } from '../../services/client-database.service';

interface SystemStats {
  totalUsers: number;
  activeUsers: number;
  totalTests: number;
  activeTests: number;
  totalQuestions: number;
  syncOperations: number;
  systemUptime: string;
  databaseSize: string;
}

interface SystemHealth {
  overall: 'healthy' | 'warning' | 'critical';
  database: 'connected' | 'disconnected' | 'error';
  sync: 'active' | 'inactive' | 'error';
  modules: 'all_active' | 'some_inactive' | 'error';
  storage: 'normal' | 'warning' | 'critical';
}

export const AdminPanel: React.FC = () => {
  const { theme, getAnimationDuration } = useTheme();
  const [activeTab, setActiveTab] = useState<'overview' | 'sync' | 'modules' | 'users' | 'settings'>('overview');
  const [systemStats, setSystemStats] = useState<SystemStats>({
    totalUsers: 0,
    activeUsers: 0,
    totalTests: 0,
    activeTests: 0,
    totalQuestions: 0,
    syncOperations: 0,
    systemUptime: '0h 0m',
    databaseSize: '0 MB'
  });
  const [systemHealth, setSystemHealth] = useState<SystemHealth>({
    overall: 'healthy',
    database: 'connected',
    sync: 'active',
    modules: 'all_active',
    storage: 'normal'
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadSystemData();
    const interval = setInterval(loadSystemData, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadSystemData = async () => {
    try {
      // Load system statistics
      const stats = await getSystemStats();
      setSystemStats(stats);

      // Check system health
      const health = await getSystemHealth();
      setSystemHealth(health);

    } catch (error) {
      console.error('Error loading system data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getSystemStats = async (): Promise<SystemStats> => {
    // Mock data - replace with actual API calls
    return {
      totalUsers: 1250,
      activeUsers: 89,
      totalTests: 45,
      activeTests: 12,
      totalQuestions: 2340,
      syncOperations: 156,
      systemUptime: '2d 14h 32m',
      databaseSize: '245 MB'
    };
  };

  const getSystemHealth = async (): Promise<SystemHealth> => {
    try {
      const dbHealth = await clientDatabaseService.checkDatabaseHealth();
      const modules = await clientDatabaseService.getAllModules();

      return {
        overall: 'healthy',
        database: dbHealth.mysql && dbHealth.mongodb ? 'connected' : 'error',
        sync: 'active',
        modules: modules.length > 0 ? 'all_active' : 'some_inactive',
        storage: 'normal'
      };
    } catch (error) {
      return {
        overall: 'critical',
        database: 'error',
        sync: 'error',
        modules: 'error',
        storage: 'critical'
      };
    }
  };

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'connected':
      case 'active':
      case 'all_active':
      case 'normal':
        return CheckCircle;
      case 'warning':
      case 'some_inactive':
        return AlertTriangle;
      case 'critical':
      case 'disconnected':
      case 'inactive':
      case 'error':
        return XCircle;
      default:
        return Clock;
    }
  };

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'connected':
      case 'active':
      case 'all_active':
      case 'normal':
        return 'text-green-500';
      case 'warning':
      case 'some_inactive':
        return 'text-yellow-500';
      case 'critical':
      case 'disconnected':
      case 'inactive':
      case 'error':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Monitor },
    { id: 'sync', label: 'Sync Management', icon: Database },
    { id: 'modules', label: 'Modules', icon: Settings },
    { id: 'users', label: 'Users', icon: Users },
    { id: 'settings', label: 'Settings', icon: Shield }
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-black flex items-center justify-center">
        <motion.div
          className="text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          <motion.div
            className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
          <p className="text-gray-600 dark:text-gray-400 font-mono">Loading admin panel...</p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-black">
      {/* Header */}
      <header className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold font-mono text-gray-900 dark:text-white">
                Admin Panel
              </h1>
              <p className="text-gray-600 dark:text-gray-400 font-mono">
                System monitoring and management
              </p>
            </div>

            {/* System Health Indicator */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                {React.createElement(getHealthIcon(systemHealth.overall), {
                  size: 20,
                  className: getHealthColor(systemHealth.overall)
                })}
                <span className={`font-mono text-sm ${getHealthColor(systemHealth.overall)}`}>
                  System {systemHealth.overall}
                </span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <nav className="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 min-h-screen">
          <div className="p-4 space-y-2">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <motion.button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`
                    w-full flex items-center gap-3 px-4 py-3 rounded-lg font-mono text-sm transition-all
                    ${activeTab === tab.id 
                      ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400' 
                      : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }
                  `}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Icon size={18} />
                  {tab.label}
                </motion.button>
              );
            })}
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 p-6">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: getAnimationDuration('normal') }}
            >
              {activeTab === 'overview' && <OverviewTab stats={systemStats} health={systemHealth} />}
              {activeTab === 'sync' && <SyncManager />}
              {activeTab === 'modules' && <ModulesTab />}
              {activeTab === 'users' && <UsersTab />}
              {activeTab === 'settings' && <SettingsTab />}
            </motion.div>
          </AnimatePresence>
        </main>
      </div>
    </div>
  );
};

// Overview Tab Component
const OverviewTab: React.FC<{ stats: SystemStats; health: SystemHealth }> = ({ stats, health }) => {
  const { getAnimationDuration } = useTheme();

  const statCards = [
    { label: 'Total Users', value: stats.totalUsers, icon: Users, color: 'blue' },
    { label: 'Active Users', value: stats.activeUsers, icon: Activity, color: 'green' },
    { label: 'Total Tests', value: stats.totalTests, icon: Monitor, color: 'purple' },
    { label: 'Questions', value: stats.totalQuestions, icon: Database, color: 'orange' }
  ];

  const healthCards = [
    { label: 'Database', status: health.database, icon: Database },
    { label: 'Sync Service', status: health.sync, icon: Wifi },
    { label: 'Modules', status: health.modules, icon: Settings },
    { label: 'Storage', status: health.storage, icon: Server }
  ];

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card, index) => (
          <motion.div
            key={card.label}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1, duration: getAnimationDuration('normal') }}
            className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-mono text-gray-600 dark:text-gray-400">{card.label}</p>
                <p className="text-2xl font-bold font-mono text-gray-900 dark:text-white">
                  {card.value.toLocaleString()}
                </p>
              </div>
              <div className={`p-3 rounded-lg bg-${card.color}-100 dark:bg-${card.color}-900/30`}>
                <card.icon size={24} className={`text-${card.color}-600 dark:text-${card.color}-400`} />
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Health Status */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
        <h3 className="text-lg font-bold font-mono text-gray-900 dark:text-white mb-4">
          System Health
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {healthCards.map((card) => {
            const Icon = card.icon;
            const StatusIcon = getHealthIcon(card.status);
            return (
              <div key={card.label} className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <Icon size={20} className="text-gray-600 dark:text-gray-400" />
                <div className="flex-1">
                  <p className="font-mono text-sm text-gray-900 dark:text-white">{card.label}</p>
                  <div className="flex items-center gap-1">
                    <StatusIcon size={14} className={getHealthColor(card.status)} />
                    <span className={`font-mono text-xs ${getHealthColor(card.status)}`}>
                      {card.status.replace('_', ' ')}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* System Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
          <h3 className="text-lg font-bold font-mono text-gray-900 dark:text-white mb-4">
            System Information
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="font-mono text-sm text-gray-600 dark:text-gray-400">Uptime</span>
              <span className="font-mono text-sm text-gray-900 dark:text-white">{stats.systemUptime}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-mono text-sm text-gray-600 dark:text-gray-400">Database Size</span>
              <span className="font-mono text-sm text-gray-900 dark:text-white">{stats.databaseSize}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-mono text-sm text-gray-600 dark:text-gray-400">Sync Operations</span>
              <span className="font-mono text-sm text-gray-900 dark:text-white">{stats.syncOperations}</span>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
          <h3 className="text-lg font-bold font-mono text-gray-900 dark:text-white mb-4">
            Quick Actions
          </h3>
          <div className="space-y-3">
            <motion.button
              className="w-full flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-mono text-sm"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Download size={16} />
              Export System Data
            </motion.button>
            <motion.button
              className="w-full flex items-center gap-2 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg font-mono text-sm"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Upload size={16} />
              Import Configuration
            </motion.button>
            <motion.button
              className="w-full flex items-center gap-2 px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg font-mono text-sm"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <TrendingUp size={16} />
              Generate Report
            </motion.button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Placeholder components for other tabs
const ModulesTab: React.FC = () => (
  <div className="text-center py-12">
    <Settings size={48} className="mx-auto text-gray-400 mb-4" />
    <h3 className="text-lg font-mono text-gray-600 dark:text-gray-400">Module Management</h3>
    <p className="text-sm font-mono text-gray-500">Coming soon...</p>
  </div>
);

const UsersTab: React.FC = () => (
  <div className="text-center py-12">
    <Users size={48} className="mx-auto text-gray-400 mb-4" />
    <h3 className="text-lg font-mono text-gray-600 dark:text-gray-400">User Management</h3>
    <p className="text-sm font-mono text-gray-500">Coming soon...</p>
  </div>
);

const SettingsTab: React.FC = () => (
  <div className="text-center py-12">
    <Shield size={48} className="mx-auto text-gray-400 mb-4" />
    <h3 className="text-lg font-mono text-gray-600 dark:text-gray-400">System Settings</h3>
    <p className="text-sm font-mono text-gray-500">Coming soon...</p>
  </div>
);

// Helper functions (moved outside component to avoid re-creation)
const getHealthIcon = (status: string) => {
  switch (status) {
    case 'healthy':
    case 'connected':
    case 'active':
    case 'all_active':
    case 'normal':
      return CheckCircle;
    case 'warning':
    case 'some_inactive':
      return AlertTriangle;
    case 'critical':
    case 'disconnected':
    case 'inactive':
    case 'error':
      return XCircle;
    default:
      return Clock;
  }
};

const getHealthColor = (status: string) => {
  switch (status) {
    case 'healthy':
    case 'connected':
    case 'active':
    case 'all_active':
    case 'normal':
      return 'text-green-500';
    case 'warning':
    case 'some_inactive':
      return 'text-yellow-500';
    case 'critical':
    case 'disconnected':
    case 'inactive':
    case 'error':
      return 'text-red-500';
    default:
      return 'text-gray-500';
  }
};
