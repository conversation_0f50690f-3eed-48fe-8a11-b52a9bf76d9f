import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Wifi, 
  Bluetooth, 
  Usb, 
  FileText, 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  Clock,
  Download,
  Upload,
  Search,
  Settings
} from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { clientSyncService, SyncDevice, SyncOperation, SyncMethod } from '../../services/client-sync.service';

export const SyncManager: React.FC = () => {
  const { theme, getAnimationDuration } = useTheme();
  const [devices, setDevices] = useState<SyncDevice[]>([]);
  const [operations, setOperations] = useState<SyncOperation[]>([]);
  const [isDiscovering, setIsDiscovering] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState<SyncMethod>('wifi');

  useEffect(() => {
    // Load initial data
    setDevices(clientSyncService.getDevices());
    setOperations(clientSyncService.getOperations());

    // Listen to sync events
    const handleDeviceDiscovered = (device: SyncDevice) => {
      setDevices(prev => [...prev.filter(d => d.id !== device.id), device]);
    };

    const handleSyncStarted = (operation: SyncOperation) => {
      setOperations(prev => [...prev.filter(op => op.id !== operation.id), operation]);
    };

    const handleSyncProgress = (operation: SyncOperation) => {
      setOperations(prev => prev.map(op => op.id === operation.id ? operation : op));
    };

    const handleSyncCompleted = (operation: SyncOperation) => {
      setOperations(prev => prev.map(op => op.id === operation.id ? operation : op));
    };

    const handleDiscoveryStarted = () => setIsDiscovering(true);
    const handleDiscoveryCompleted = () => setIsDiscovering(false);

    clientSyncService.on('device-discovered', handleDeviceDiscovered);
    clientSyncService.on('sync-started', handleSyncStarted);
    clientSyncService.on('sync-progress', handleSyncProgress);
    clientSyncService.on('sync-completed', handleSyncCompleted);
    clientSyncService.on('sync-error', handleSyncCompleted);
    clientSyncService.on('discovery-started', handleDiscoveryStarted);
    clientSyncService.on('discovery-completed', handleDiscoveryCompleted);

    return () => {
      clientSyncService.off('device-discovered', handleDeviceDiscovered);
      clientSyncService.off('sync-started', handleSyncStarted);
      clientSyncService.off('sync-progress', handleSyncProgress);
      clientSyncService.off('sync-completed', handleSyncCompleted);
      clientSyncService.off('sync-error', handleSyncCompleted);
      clientSyncService.off('discovery-started', handleDiscoveryStarted);
      clientSyncService.off('discovery-completed', handleDiscoveryCompleted);
    };
  }, []);

  const handleDiscoverDevices = async () => {
    await clientSyncService.startDeviceDiscovery();
  };

  const handleSyncWithDevice = async (deviceId: string) => {
    try {
      await clientSyncService.syncWithDevice(deviceId, ['all']);
    } catch (error) {
      console.error('Sync error:', error);
    }
  };

  const getMethodIcon = (method: SyncMethod) => {
    switch (method) {
      case 'wifi': return Wifi;
      case 'bluetooth': return Bluetooth;
      case 'usb': return Usb;
      case 'file': return FileText;
      default: return Wifi;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return CheckCircle;
      case 'error': return XCircle;
      case 'syncing': return RefreshCw;
      default: return Clock;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-500';
      case 'error': return 'text-red-500';
      case 'syncing': return 'text-blue-500';
      default: return 'text-gray-500';
    }
  };

  const filteredDevices = devices.filter(device => device.type === selectedMethod);

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold font-mono text-gray-900 dark:text-white">
            Data Synchronization
          </h2>
          <p className="text-gray-600 dark:text-gray-400 font-mono">
            Sync exam data across devices without internet connection
          </p>
        </div>

        <motion.button
          onClick={handleDiscoverDevices}
          disabled={isDiscovering}
          className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-mono disabled:opacity-50"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <motion.div
            animate={isDiscovering ? { rotate: 360 } : {}}
            transition={{ duration: 1, repeat: isDiscovering ? Infinity : 0, ease: "linear" }}
          >
            <Search size={16} />
          </motion.div>
          {isDiscovering ? 'Discovering...' : 'Discover Devices'}
        </motion.button>
      </div>

      {/* Sync Method Selector */}
      <div className="flex gap-2 p-1 bg-gray-100 dark:bg-gray-800 rounded-lg">
        {(['wifi', 'bluetooth', 'usb', 'file'] as SyncMethod[]).map((method) => {
          const Icon = getMethodIcon(method);
          return (
            <motion.button
              key={method}
              onClick={() => setSelectedMethod(method)}
              className={`
                flex items-center gap-2 px-4 py-2 rounded-md font-mono text-sm transition-all
                ${selectedMethod === method 
                  ? 'bg-white dark:bg-gray-700 shadow-sm text-gray-900 dark:text-white' 
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }
              `}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Icon size={16} />
              <span className="capitalize">{method}</span>
            </motion.button>
          );
        })}
      </div>

      {/* Devices Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <AnimatePresence>
          {filteredDevices.map((device) => {
            const Icon = getMethodIcon(device.type);
            const activeOperation = operations.find(op => op.deviceId === device.id && op.status === 'syncing');
            
            return (
              <motion.div
                key={device.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <div className={`p-2 rounded-lg ${device.isOnline ? 'bg-green-100 dark:bg-green-900/30' : 'bg-gray-100 dark:bg-gray-700'}`}>
                      <Icon size={16} className={device.isOnline ? 'text-green-600 dark:text-green-400' : 'text-gray-500'} />
                    </div>
                    <div>
                      <h3 className="font-mono font-semibold text-gray-900 dark:text-white text-sm">
                        {device.name}
                      </h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                        {device.address}
                      </p>
                    </div>
                  </div>
                  
                  <div className={`w-2 h-2 rounded-full ${device.isOnline ? 'bg-green-500' : 'bg-gray-400'}`} />
                </div>

                {/* Sync Progress */}
                {activeOperation && (
                  <div className="mb-3">
                    <div className="flex items-center justify-between text-xs font-mono text-gray-600 dark:text-gray-400 mb-1">
                      <span>Syncing...</span>
                      <span>{activeOperation.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                      <motion.div
                        className="bg-blue-500 h-1 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${activeOperation.progress}%` }}
                        transition={{ duration: 0.3 }}
                      />
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex gap-2">
                  <motion.button
                    onClick={() => handleSyncWithDevice(device.id)}
                    disabled={!device.isOnline || !!activeOperation}
                    className="flex-1 flex items-center justify-center gap-1 px-3 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 dark:disabled:bg-gray-600 text-white rounded-md text-xs font-mono"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Upload size={12} />
                    Sync
                  </motion.button>
                  
                  <motion.button
                    className="px-3 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Settings size={12} />
                  </motion.button>
                </div>
              </motion.div>
            );
          })}
        </AnimatePresence>

        {filteredDevices.length === 0 && (
          <div className="col-span-full text-center py-8">
            <div className="text-gray-400 dark:text-gray-600 font-mono">
              No {selectedMethod} devices found. Click "Discover Devices" to search.
            </div>
          </div>
        )}
      </div>

      {/* Recent Operations */}
      <div className="space-y-4">
        <h3 className="text-lg font-bold font-mono text-gray-900 dark:text-white">
          Recent Sync Operations
        </h3>
        
        <div className="space-y-2">
          {operations.slice(0, 5).map((operation) => {
            const StatusIcon = getStatusIcon(operation.status);
            const device = devices.find(d => d.id === operation.deviceId);
            
            return (
              <motion.div
                key={operation.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
              >
                <div className="flex items-center gap-3">
                  <StatusIcon 
                    size={16} 
                    className={`${getStatusColor(operation.status)} ${operation.status === 'syncing' ? 'animate-spin' : ''}`} 
                  />
                  <div>
                    <div className="font-mono text-sm text-gray-900 dark:text-white">
                      {device?.name || 'Unknown Device'}
                    </div>
                    <div className="font-mono text-xs text-gray-500 dark:text-gray-400">
                      {operation.startTime.toLocaleTimeString()} • {operation.recordsCount} records
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className={`font-mono text-sm ${getStatusColor(operation.status)}`}>
                    {operation.status}
                  </div>
                  {operation.endTime && (
                    <div className="font-mono text-xs text-gray-500 dark:text-gray-400">
                      {Math.round((operation.endTime.getTime() - operation.startTime.getTime()) / 1000)}s
                    </div>
                  )}
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
