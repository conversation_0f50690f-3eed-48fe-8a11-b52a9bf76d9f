import { EventEmitter } from 'events';
import { 
  Module, 
  ModuleType, 
  ModuleAPI, 
  ModuleRegistry, 
  ModuleSettings,
  ModuleStatus,
  ModuleEvent,
  ModuleEventData
} from '../types/module.types';
import { databaseService } from './database.service';

class ModuleService extends EventEmitter {
  private registry: ModuleRegistry;
  private installedModules: Map<string, Module> = new Map();
  private moduleAPIs: Map<string, ModuleAPI> = new Map();

  constructor() {
    super();
    this.registry = {
      modules: new Map(),
      apis: new Map(),
      register: this.registerModule.bind(this),
      unregister: this.unregisterModule.bind(this),
      get: this.getModule.bind(this),
      getByType: this.getModulesByType.bind(this),
      getActive: this.getActiveModules.bind(this)
    };
    
    this.initializeDefaultModules();
  }

  private async initializeDefaultModules() {
    // Initialize core modules
    const coreModules = this.getCoreModules();
    
    for (const module of coreModules) {
      await this.installModule(module);
    }
  }

  private getCoreModules(): Module[] {
    return [
      {
        id: 'cbt-core',
        name: 'CBT Exam Module',
        description: 'Computer-Based Testing with advanced security features',
        type: 'cbt',
        icon: 'BookOpen',
        version: '1.0.0',
        isActive: true,
        isInstalled: true,
        permissions: ['exam.create', 'exam.take', 'exam.grade', 'exam.analyze'],
        settings: {
          examSettings: {
            defaultDuration: 60,
            allowReview: true,
            shuffleQuestions: false,
            shuffleOptions: false,
            showTimer: true,
            fullScreenRequired: true,
            preventCheating: true
          },
          securitySettings: {
            disableRightClick: true,
            disableCopy: true,
            disablePaste: true,
            disableDevTools: true,
            monitorTabSwitching: true,
            screenshotDetection: true
          }
        },
        dependencies: [],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'lms-core',
        name: 'Learning Management System',
        description: 'Course management and content delivery',
        type: 'lms',
        icon: 'GraduationCap',
        version: '1.0.0',
        isActive: false,
        isInstalled: false,
        permissions: ['course.create', 'course.manage', 'content.upload', 'student.enroll'],
        settings: {
          courseSettings: {
            allowSelfEnrollment: true,
            requireApproval: false,
            certificateGeneration: true,
            progressTracking: true
          }
        },
        dependencies: [],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'attendance-core',
        name: 'Attendance Management',
        description: 'Student attendance tracking and reporting',
        type: 'attendance',
        icon: 'Users',
        version: '1.0.0',
        isActive: false,
        isInstalled: false,
        permissions: ['attendance.mark', 'attendance.view', 'attendance.report'],
        settings: {
          trackingSettings: {
            method: 'manual',
            graceTime: 15,
            lateThreshold: 30,
            autoMarkAbsent: false
          }
        },
        dependencies: [],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'gradebook-core',
        name: 'Gradebook',
        description: 'Grade management and academic records',
        type: 'gradebook',
        icon: 'BarChart3',
        version: '1.0.0',
        isActive: false,
        isInstalled: false,
        permissions: ['grade.create', 'grade.edit', 'grade.view', 'transcript.generate'],
        settings: {
          gradingSettings: {
            scale: 'percentage',
            passingGrade: 60,
            weightedGrades: false,
            extraCredit: true
          }
        },
        dependencies: [],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'library-core',
        name: 'Library Management',
        description: 'Digital library and resource management',
        type: 'library',
        icon: 'Library',
        version: '1.0.0',
        isActive: false,
        isInstalled: false,
        permissions: ['book.catalog', 'book.issue', 'book.return', 'resource.manage'],
        settings: {
          catalogSettings: {
            allowReservations: true,
            maxBooksPerUser: 5,
            loanPeriod: 14,
            renewalLimit: 2
          }
        },
        dependencies: [],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'fee-management-core',
        name: 'Fee Management',
        description: 'Student fee collection and financial tracking',
        type: 'fee_management',
        icon: 'CreditCard',
        version: '1.0.0',
        isActive: false,
        isInstalled: false,
        permissions: ['fee.collect', 'fee.track', 'payment.process', 'receipt.generate'],
        settings: {
          paymentSettings: {
            acceptedMethods: ['cash', 'card', 'bank_transfer'],
            lateFeePercentage: 5,
            installmentAllowed: true,
            discountRules: []
          }
        },
        dependencies: [],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'timetable-core',
        name: 'Timetable Management',
        description: 'Class scheduling and timetable management',
        type: 'timetable',
        icon: 'Clock',
        version: '1.0.0',
        isActive: false,
        isInstalled: false,
        permissions: ['schedule.create', 'schedule.edit', 'schedule.view', 'conflict.resolve'],
        settings: {
          scheduleSettings: {
            workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
            periodsPerDay: 8,
            periodDuration: 45,
            breakDuration: 15
          }
        },
        dependencies: [],
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
  }

  // Module management
  async installModule(module: Module): Promise<void> {
    try {
      // Validate dependencies
      await this.validateDependencies(module.dependencies);
      
      // Save to database
      await databaseService.createModule(module);
      
      // Register in memory
      this.installedModules.set(module.id, module);
      
      // Emit event
      this.emitModuleEvent('module_installed', module.id);
      
      console.log(`✅ Module ${module.name} installed successfully`);
    } catch (error) {
      console.error(`❌ Failed to install module ${module.name}:`, error);
      this.emitModuleEvent('module_error', module.id, { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  async uninstallModule(moduleId: string): Promise<void> {
    try {
      const module = this.installedModules.get(moduleId);
      if (!module) {
        throw new Error('Module not found');
      }

      // Check for dependent modules
      const dependentModules = this.getDependentModules(moduleId);
      if (dependentModules.length > 0) {
        throw new Error(`Cannot uninstall: ${dependentModules.length} modules depend on this module`);
      }

      // Deactivate if active
      if (module.isActive) {
        await this.deactivateModule(moduleId);
      }

      // Remove from database
      // await databaseService.deleteModule(moduleId);
      
      // Unregister from memory
      this.installedModules.delete(moduleId);
      this.moduleAPIs.delete(moduleId);
      
      // Emit event
      this.emitModuleEvent('module_uninstalled', moduleId);
      
      console.log(`✅ Module ${module.name} uninstalled successfully`);
    } catch (error) {
      console.error(`❌ Failed to uninstall module ${moduleId}:`, error);
      this.emitModuleEvent('module_error', moduleId, { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  async activateModule(moduleId: string): Promise<void> {
    try {
      const module = this.installedModules.get(moduleId);
      if (!module) {
        throw new Error('Module not found');
      }

      if (module.isActive) {
        return; // Already active
      }

      // Initialize module API if available
      const api = this.moduleAPIs.get(moduleId);
      if (api) {
        await api.initialize();
      }

      // Update module status
      module.isActive = true;
      module.updatedAt = new Date();
      
      // Update in database
      await databaseService.updateModule(moduleId, { isActive: true });
      
      // Emit event
      this.emitModuleEvent('module_activated', moduleId);
      
      console.log(`✅ Module ${module.name} activated successfully`);
    } catch (error) {
      console.error(`❌ Failed to activate module ${moduleId}:`, error);
      this.emitModuleEvent('module_error', moduleId, { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  async deactivateModule(moduleId: string): Promise<void> {
    try {
      const module = this.installedModules.get(moduleId);
      if (!module) {
        throw new Error('Module not found');
      }

      if (!module.isActive) {
        return; // Already inactive
      }

      // Destroy module API if available
      const api = this.moduleAPIs.get(moduleId);
      if (api) {
        await api.destroy();
      }

      // Update module status
      module.isActive = false;
      module.updatedAt = new Date();
      
      // Update in database
      await databaseService.updateModule(moduleId, { isActive: false });
      
      // Emit event
      this.emitModuleEvent('module_deactivated', moduleId);
      
      console.log(`✅ Module ${module.name} deactivated successfully`);
    } catch (error) {
      console.error(`❌ Failed to deactivate module ${moduleId}:`, error);
      this.emitModuleEvent('module_error', moduleId, { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  // Registry methods
  private registerModule(module: Module, api: ModuleAPI): void {
    this.registry.modules.set(module.id, module);
    this.registry.apis.set(module.id, api);
    this.moduleAPIs.set(module.id, api);
  }

  private unregisterModule(moduleId: string): void {
    this.registry.modules.delete(moduleId);
    this.registry.apis.delete(moduleId);
    this.moduleAPIs.delete(moduleId);
  }

  private getModule(moduleId: string): { module: Module; api: ModuleAPI } | undefined {
    const module = this.registry.modules.get(moduleId);
    const api = this.registry.apis.get(moduleId);
    
    if (module && api) {
      return { module, api };
    }
    
    return undefined;
  }

  private getModulesWithAPI(): { module: Module; api: ModuleAPI }[] {
    const results: { module: Module; api: ModuleAPI }[] = [];

    for (const [id, module] of this.registry.modules) {
      const api = this.registry.apis.get(id);
      if (api) {
        results.push({ module, api });
      }
    }

    return results;
  }

  // Helper methods
  private async validateDependencies(dependencies: string[]): Promise<void> {
    for (const depId of dependencies) {
      const dep = this.installedModules.get(depId);
      if (!dep || !dep.isActive) {
        throw new Error(`Dependency ${depId} is not installed or active`);
      }
    }
  }

  private getDependentModules(moduleId: string): Module[] {
    const dependents: Module[] = [];
    
    for (const module of this.installedModules.values()) {
      if (module.dependencies.includes(moduleId)) {
        dependents.push(module);
      }
    }
    
    return dependents;
  }

  private emitModuleEvent(type: ModuleEvent, moduleId: string, data?: any): void {
    const eventData: ModuleEventData = {
      moduleId,
      type,
      timestamp: new Date(),
      data
    };
    
    this.emit('module-event', eventData);
    this.emit(type, eventData);
  }

  // Public API
  getInstalledModules(): Module[] {
    return Array.from(this.installedModules.values());
  }

  getActiveModules(): Module[] {
    return Array.from(this.installedModules.values()).filter(m => m.isActive);
  }

  getModuleById(id: string): Module | undefined {
    return this.installedModules.get(id);
  }

  getModulesByType(type: ModuleType): Module[] {
    return Array.from(this.installedModules.values()).filter(m => m.type === type);
  }

  async updateModuleSettings(moduleId: string, settings: Partial<ModuleSettings>): Promise<void> {
    const module = this.installedModules.get(moduleId);
    if (!module) {
      throw new Error('Module not found');
    }

    module.settings = { ...module.settings, ...settings };
    module.updatedAt = new Date();
    
    // Update in database
    await databaseService.updateModule(moduleId, { settings: module.settings });
    
    // Update API if available
    const api = this.moduleAPIs.get(moduleId);
    if (api) {
      await api.updateSettings(settings);
    }
    
    this.emitModuleEvent('settings_changed', moduleId, { settings });
  }

  getRegistry(): ModuleRegistry {
    return this.registry;
  }
}

export const moduleService = new ModuleService();
