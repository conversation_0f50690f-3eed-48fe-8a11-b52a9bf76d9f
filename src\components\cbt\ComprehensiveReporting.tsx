import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Bar<PERSON>hart3,
  Pie<PERSON>hart,
  TrendingUp,
  Download,
  Filter,
  Calendar,
  Users,
  Award,
  Clock,
  Target,
  FileText,
  Printer,
  Mail,
  Share2,
  Eye,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { TestAttempt, TestAnalytics, CBTUser } from '../../types/cbt.types';

interface ReportData {
  testAttempts: TestAttempt[];
  analytics: TestAnalytics;
  users: CBTUser[];
  timeRange: {
    start: Date;
    end: Date;
  };
}

interface ReportFilters {
  dateRange: 'today' | 'week' | 'month' | 'quarter' | 'year' | 'custom';
  testStatus: 'all' | 'completed' | 'in_progress' | 'abandoned';
  userRole: 'all' | 'student' | 'instructor' | 'admin';
  scoreRange: {
    min: number;
    max: number;
  };
  categories: string[];
}

interface ComprehensiveReportingProps {
  reportData: ReportData;
  onExportReport: (format: 'pdf' | 'excel' | 'csv') => void;
  onEmailReport: (recipients: string[]) => void;
}

export const ComprehensiveReporting: React.FC<ComprehensiveReportingProps> = ({
  reportData,
  onExportReport,
  onEmailReport
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'performance' | 'detailed' | 'trends'>('overview');
  const [filters, setFilters] = useState<ReportFilters>({
    dateRange: 'month',
    testStatus: 'all',
    userRole: 'all',
    scoreRange: { min: 0, max: 100 },
    categories: []
  });
  const [showExportOptions, setShowExportOptions] = useState(false);

  const getFilteredAttempts = () => {
    return reportData.testAttempts.filter(attempt => {
      // Date range filter
      const attemptDate = new Date(attempt.startTime);
      const now = new Date();
      let dateMatch = true;
      
      switch (filters.dateRange) {
        case 'today':
          dateMatch = attemptDate.toDateString() === now.toDateString();
          break;
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          dateMatch = attemptDate >= weekAgo;
          break;
        case 'month':
          const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          dateMatch = attemptDate >= monthAgo;
          break;
      }

      // Status filter
      const statusMatch = filters.testStatus === 'all' || attempt.status === filters.testStatus;

      // Score range filter
      const scoreMatch = attempt.score >= filters.scoreRange.min && attempt.score <= filters.scoreRange.max;

      return dateMatch && statusMatch && scoreMatch;
    });
  };

  const calculateMetrics = () => {
    const filteredAttempts = getFilteredAttempts();
    const completedAttempts = filteredAttempts.filter(a => a.status === 'completed');
    
    const totalAttempts = filteredAttempts.length;
    const averageScore = completedAttempts.length > 0 
      ? completedAttempts.reduce((sum, a) => sum + a.score, 0) / completedAttempts.length 
      : 0;
    const passRate = completedAttempts.length > 0
      ? (completedAttempts.filter(a => a.passed).length / completedAttempts.length) * 100
      : 0;
    const completionRate = totalAttempts > 0 
      ? (completedAttempts.length / totalAttempts) * 100 
      : 0;
    const averageDuration = completedAttempts.length > 0 
      ? completedAttempts.reduce((sum, a) => sum + (a.duration || 0), 0) / completedAttempts.length 
      : 0;

    return {
      totalAttempts,
      averageScore,
      passRate,
      completionRate,
      averageDuration,
      uniqueStudents: new Set(filteredAttempts.map(a => a.studentId)).size
    };
  };

  const getScoreDistribution = () => {
    const filteredAttempts = getFilteredAttempts().filter(a => a.status === 'completed');
    const ranges = [
      { label: '0-20%', min: 0, max: 20 },
      { label: '21-40%', min: 21, max: 40 },
      { label: '41-60%', min: 41, max: 60 },
      { label: '61-80%', min: 61, max: 80 },
      { label: '81-100%', min: 81, max: 100 }
    ];

    return ranges.map(range => ({
      ...range,
      count: filteredAttempts.filter(a => a.score >= range.min && a.score <= range.max).length
    }));
  };

  const getTopPerformers = () => {
    const filteredAttempts = getFilteredAttempts().filter(a => a.status === 'completed');
    const studentScores = new Map<string, { name: string; scores: number[]; average: number }>();

    filteredAttempts.forEach(attempt => {
      if (!studentScores.has(attempt.studentId)) {
        studentScores.set(attempt.studentId, {
          name: attempt.studentName || 'Unknown',
          scores: [],
          average: 0
        });
      }
      studentScores.get(attempt.studentId)!.scores.push(attempt.score);
    });

    // Calculate averages
    studentScores.forEach((data, studentId) => {
      data.average = data.scores.reduce((sum, score) => sum + score, 0) / data.scores.length;
    });

    return Array.from(studentScores.entries())
      .map(([id, data]) => ({ id, ...data }))
      .sort((a, b) => b.average - a.average)
      .slice(0, 10);
  };

  const metrics = calculateMetrics();
  const scoreDistribution = getScoreDistribution();
  const topPerformers = getTopPerformers();

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'performance', label: 'Performance', icon: Award },
    { id: 'detailed', label: 'Detailed', icon: FileText },
    { id: 'trends', label: 'Trends', icon: TrendingUp }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white font-mono">Comprehensive Reports</h2>
          <p className="text-gray-400 font-mono">Advanced analytics and insights</p>
        </div>
        
        <div className="flex space-x-3">
          <motion.button
            onClick={() => setShowExportOptions(!showExportOptions)}
            className="px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors font-mono border border-gray-600"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Download size={16} className="inline mr-2" />
            Export
          </motion.button>
          
          <motion.button
            className="px-4 py-2 bg-black hover:bg-gray-900 text-white rounded-lg transition-colors font-mono border border-gray-600"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Share2 size={16} className="inline mr-2" />
            Share
          </motion.button>
        </div>
      </div>

      {/* Export Options */}
      {showExportOptions && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-black/60 backdrop-blur-xl rounded-xl p-4 border border-gray-600/50"
        >
          <h3 className="text-lg font-bold text-white mb-3 font-mono">Export Options</h3>
          <div className="flex space-x-3">
            <motion.button
              onClick={() => onExportReport('pdf')}
              className="flex items-center space-x-2 px-4 py-2 bg-red-900/30 hover:bg-red-800/30 text-red-400 border border-red-600/50 rounded-lg transition-colors font-mono"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <FileText size={16} />
              <span>PDF Report</span>
            </motion.button>
            
            <motion.button
              onClick={() => onExportReport('excel')}
              className="flex items-center space-x-2 px-4 py-2 bg-green-900/30 hover:bg-green-800/30 text-green-400 border border-green-600/50 rounded-lg transition-colors font-mono"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <BarChart3 size={16} />
              <span>Excel</span>
            </motion.button>
            
            <motion.button
              onClick={() => onExportReport('csv')}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-900/30 hover:bg-blue-800/30 text-blue-400 border border-blue-600/50 rounded-lg transition-colors font-mono"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <FileText size={16} />
              <span>CSV Data</span>
            </motion.button>
          </div>
        </motion.div>
      )}

      {/* Filters */}
      <div className="bg-black/60 backdrop-blur-xl rounded-xl p-4 border border-gray-600/50">
        <div className="flex items-center space-x-4 flex-wrap">
          <div className="flex items-center space-x-2">
            <Calendar size={16} className="text-gray-400" />
            <select
              value={filters.dateRange}
              onChange={(e) => setFilters(prev => ({ ...prev, dateRange: e.target.value as any }))}
              className="px-3 py-1 bg-gray-800/50 border border-gray-600/50 rounded text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono text-sm"
            >
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="quarter">This Quarter</option>
              <option value="year">This Year</option>
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <Filter size={16} className="text-gray-400" />
            <select
              value={filters.testStatus}
              onChange={(e) => setFilters(prev => ({ ...prev, testStatus: e.target.value as any }))}
              className="px-3 py-1 bg-gray-800/50 border border-gray-600/50 rounded text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono text-sm"
            >
              <option value="all">All Status</option>
              <option value="completed">Completed</option>
              <option value="in_progress">In Progress</option>
              <option value="abandoned">Abandoned</option>
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <Users size={16} className="text-gray-400" />
            <select
              value={filters.userRole}
              onChange={(e) => setFilters(prev => ({ ...prev, userRole: e.target.value as any }))}
              className="px-3 py-1 bg-gray-800/50 border border-gray-600/50 rounded text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono text-sm"
            >
              <option value="all">All Users</option>
              <option value="student">Students</option>
              <option value="instructor">Instructors</option>
              <option value="admin">Admins</option>
            </select>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-700">
        <nav className="flex space-x-8">
          {tabs.map(tab => {
            const IconComponent = tab.icon;
            return (
              <motion.button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm font-mono transition-colors ${
                  activeTab === tab.id
                    ? 'border-white text-white'
                    : 'border-transparent text-gray-400 hover:text-gray-300'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <IconComponent size={16} />
                <span>{tab.label}</span>
              </motion.button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'overview' && (
          <>
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
              <div className="bg-black/60 backdrop-blur-xl rounded-xl p-4 border border-gray-600/50">
                <div className="flex items-center space-x-2 mb-2">
                  <Users className="text-blue-400" size={16} />
                  <span className="text-gray-400 text-sm font-mono">Total Attempts</span>
                </div>
                <p className="text-2xl font-bold text-white font-mono">{metrics.totalAttempts}</p>
              </div>

              <div className="bg-black/60 backdrop-blur-xl rounded-xl p-4 border border-gray-600/50">
                <div className="flex items-center space-x-2 mb-2">
                  <Award className="text-green-400" size={16} />
                  <span className="text-gray-400 text-sm font-mono">Avg Score</span>
                </div>
                <p className="text-2xl font-bold text-white font-mono">{metrics.averageScore.toFixed(1)}%</p>
              </div>

              <div className="bg-black/60 backdrop-blur-xl rounded-xl p-4 border border-gray-600/50">
                <div className="flex items-center space-x-2 mb-2">
                  <Target className="text-yellow-400" size={16} />
                  <span className="text-gray-400 text-sm font-mono">Pass Rate</span>
                </div>
                <p className="text-2xl font-bold text-white font-mono">{metrics.passRate.toFixed(1)}%</p>
              </div>

              <div className="bg-black/60 backdrop-blur-xl rounded-xl p-4 border border-gray-600/50">
                <div className="flex items-center space-x-2 mb-2">
                  <CheckCircle className="text-purple-400" size={16} />
                  <span className="text-gray-400 text-sm font-mono">Completion</span>
                </div>
                <p className="text-2xl font-bold text-white font-mono">{metrics.completionRate.toFixed(1)}%</p>
              </div>

              <div className="bg-black/60 backdrop-blur-xl rounded-xl p-4 border border-gray-600/50">
                <div className="flex items-center space-x-2 mb-2">
                  <Clock className="text-orange-400" size={16} />
                  <span className="text-gray-400 text-sm font-mono">Avg Time</span>
                </div>
                <p className="text-2xl font-bold text-white font-mono">
                  {Math.floor(metrics.averageDuration / 60)}m
                </p>
              </div>

              <div className="bg-black/60 backdrop-blur-xl rounded-xl p-4 border border-gray-600/50">
                <div className="flex items-center space-x-2 mb-2">
                  <Users className="text-cyan-400" size={16} />
                  <span className="text-gray-400 text-sm font-mono">Students</span>
                </div>
                <p className="text-2xl font-bold text-white font-mono">{metrics.uniqueStudents}</p>
              </div>
            </div>

            {/* Score Distribution */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-black/60 backdrop-blur-xl rounded-xl p-6 border border-gray-600/50">
                <h3 className="text-lg font-bold text-white mb-4 font-mono">Score Distribution</h3>
                <div className="space-y-3">
                  {scoreDistribution.map(range => {
                    const percentage = metrics.totalAttempts > 0 ? (range.count / metrics.totalAttempts) * 100 : 0;
                    return (
                      <div key={range.label} className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-gray-400 text-sm font-mono">{range.label}</span>
                          <span className="text-white text-sm font-mono">{range.count} ({percentage.toFixed(1)}%)</span>
                        </div>
                        <div className="w-full h-2 bg-gray-800 rounded-full overflow-hidden">
                          <motion.div
                            className="h-full bg-gradient-to-r from-red-500 via-yellow-500 to-green-500"
                            initial={{ width: 0 }}
                            animate={{ width: `${percentage}%` }}
                            transition={{ duration: 0.5, delay: 0.1 }}
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Top Performers */}
              <div className="bg-black/60 backdrop-blur-xl rounded-xl p-6 border border-gray-600/50">
                <h3 className="text-lg font-bold text-white mb-4 font-mono">Top Performers</h3>
                <div className="space-y-3">
                  {topPerformers.slice(0, 5).map((performer, index) => (
                    <div key={performer.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-mono ${
                          index === 0 ? 'bg-yellow-500 text-black' :
                          index === 1 ? 'bg-gray-400 text-black' :
                          index === 2 ? 'bg-orange-500 text-black' :
                          'bg-gray-700 text-white'
                        }`}>
                          {index + 1}
                        </span>
                        <span className="text-white font-mono">{performer.name}</span>
                      </div>
                      <span className="text-green-400 font-mono font-bold">
                        {performer.average.toFixed(1)}%
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </>
        )}

        {/* Other tab content would go here */}
        {activeTab !== 'overview' && (
          <div className="text-center py-12 bg-black/60 backdrop-blur-xl rounded-xl border border-gray-600/50">
            <BarChart3 className="mx-auto text-gray-600 mb-4" size={48} />
            <h3 className="text-xl font-bold text-gray-400 mb-2 font-mono">{tabs.find(t => t.id === activeTab)?.label} View</h3>
            <p className="text-gray-500 font-mono">Detailed {activeTab} analytics coming soon</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ComprehensiveReporting;
