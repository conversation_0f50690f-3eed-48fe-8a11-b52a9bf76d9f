import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Mail, Lock, Eye, EyeOff, Shield } from 'lucide-react';
import { Button } from '../shared/Button';
import { Input } from '../shared/Input';
import { validateEmail } from '../../utils/validation';
import { UserRole } from '../../types/cbt.types';
import { useCBT } from '../../contexts/CBTContext';

interface CBTLoginFormProps {
  role: UserRole;
}

interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

export const CBTLoginForm: React.FC<CBTLoginFormProps> = ({ role }) => {
  const { login, state } = useCBT();
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
    rememberMe: false
  });
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState<Partial<LoginFormData>>({});

  // Pre-fill demo credentials based on role
  const getDemoCredentials = () => {
    switch (role) {
      case 'admin':
        return { email: '<EMAIL>', password: 'admin123' };
      case 'instructor':
        return { email: '<EMAIL>', password: 'instructor123' };
      case 'student':
        return { email: '<EMAIL>', password: 'student123' };
      default:
        return { email: '', password: '' };
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name as keyof LoginFormData]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<LoginFormData> = {};

    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    await login(formData.email, formData.password, role);
  };

  const handleDemoLogin = () => {
    const demoCredentials = getDemoCredentials();
    setFormData(prev => ({
      ...prev,
      ...demoCredentials
    }));
  };

  const getRoleDisplayName = () => {
    switch (role) {
      case 'admin': return 'Administrator';
      case 'instructor': return 'Instructor';
      case 'student': return 'Student';
      default: return 'User';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, ease: [0.25, 0.1, 0.25, 1] }}
      className="w-full max-w-md mx-auto"
    >
      <div className="bg-black/60 backdrop-blur-xl rounded-2xl p-8 shadow-2xl border border-gray-600/50 ring-1 ring-gray-500/20">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.1, duration: 0.3, ease: [0.25, 0.1, 0.25, 1] }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center mb-4">
            <Shield className="text-gray-400 mr-2" size={24} />
            <h2 className="text-2xl font-bold text-white font-mono">SECURE LOGIN</h2>
          </div>
          <p className="text-gray-300 font-mono">{getRoleDisplayName()} Access Portal</p>
        </motion.div>

        {state.error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, ease: [0.25, 0.1, 0.25, 1] }}
            className="bg-red-900/40 border border-red-600/60 rounded-lg p-3 mb-6 backdrop-blur-sm"
          >
            <p className="text-red-200 text-sm font-mono">{state.error}</p>
          </motion.div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <Input
            type="email"
            name="email"
            placeholder="Enter your email"
            value={formData.email}
            onChange={handleChange}
            error={errors.email}
            icon={<Mail size={20} />}
            className="bg-black/70 border-gray-600/50 text-white placeholder-gray-400 backdrop-blur-sm font-mono"
          />

          <div className="relative">
            <Input
              type={showPassword ? 'text' : 'password'}
              name="password"
              placeholder="Enter your password"
              value={formData.password}
              onChange={handleChange}
              error={errors.password}
              icon={<Lock size={20} />}
              className="bg-black/70 border-gray-600/50 text-white placeholder-gray-400 backdrop-blur-sm pr-12 font-mono"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-300 hover:text-white transition-colors"
            >
              {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
            </button>
          </div>

          <div className="flex items-center justify-between">
            <label className="flex items-center">
              <input
                type="checkbox"
                name="rememberMe"
                checked={formData.rememberMe}
                onChange={handleChange}
                className="rounded border-gray-600 bg-black/50 text-gray-400 focus:ring-gray-500"
              />
              <span className="ml-2 text-sm text-gray-300 font-mono">Remember me</span>
            </label>
            <button
              type="button"
              className="text-sm text-gray-400 hover:text-gray-300 transition-colors font-mono"
            >
              Forgot password?
            </button>
          </div>

          <Button
            type="submit"
            loading={state.isLoading}
            className="w-full bg-gradient-to-r from-black to-gray-800 hover:from-gray-800 hover:to-black border border-gray-600/50 font-mono"
          >
            SECURE LOGIN
          </Button>
        </form>

        {/* Demo Login Button */}
        <div className="mt-6 pt-6 border-t border-gray-700/50">
          <button
            onClick={handleDemoLogin}
            className="w-full text-center text-gray-400 hover:text-gray-300 transition-colors font-mono text-sm"
          >
            Use demo credentials for {getRoleDisplayName().toLowerCase()}
          </button>
        </div>

        {/* Security Notice */}
        <div className="mt-6 p-4 bg-gray-900/50 rounded-lg border border-gray-700/50">
          <div className="flex items-start space-x-2">
            <Shield className="text-gray-400 mt-0.5" size={16} />
            <div>
              <p className="text-xs text-gray-400 font-mono">
                This is a secure testing environment. All activities are monitored and logged.
              </p>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};
