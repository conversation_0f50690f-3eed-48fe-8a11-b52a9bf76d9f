import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

import { CBTProvider } from '../../contexts/CBTContext';
import { CBTDashboard } from '../../components/cbt/CBTDashboard';
import { TestTaking } from '../../components/cbt/TestTaking';
import { TestCreation } from '../../components/cbt/TestCreation';
import { ResultsAnalytics } from '../../components/cbt/ResultsAnalytics';
import { CBTSettings } from '../../components/cbt/CBTSettings';
import { ModuleSelection } from '../../components/cbt/ModuleSelection';
import { AdaptiveQuestionEngine } from '../../components/cbt/AdaptiveQuestionEngine';
import { ProgressTracker } from '../../components/cbt/ProgressTracker';
import { DragDropTestBuilder } from '../../components/cbt/DragDropTestBuilder';
import { ComprehensiveReporting } from '../../components/cbt/ComprehensiveReporting';

import {
  createMockUser,
  createMockTest,
  createMockQuestion,
  createMockTestAttempt,
  mockElectronAPI,
  mockLocalStorage,
  simulateSecurityEvent,
  measurePerformance,
  checkAccessibility,
  validateTestData
} from '../utils/testUtils';

// Mock the Electron API
const mockElectron = mockElectronAPI();
(global as any).window.electronAPI = mockElectron;

// Mock localStorage
const mockStorage = mockLocalStorage();
Object.defineProperty(window, 'localStorage', { value: mockStorage });

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    form: ({ children, ...props }: any) => <form {...props}>{children}</form>,
    input: ({ children, ...props }: any) => <input {...props}>{children}</input>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock Three.js components
jest.mock('../../components/shared/EnhancedThreeBackground', () => ({
  EnhancedThreeBackground: () => <div data-testid="three-background" />
}));

describe('CBT System Integration Tests', () => {
  const mockUser = createMockUser({
    role: 'student',
    name: 'Test Student',
    email: '<EMAIL>'
  });

  const mockInstructor = createMockUser({
    role: 'instructor',
    name: 'Test Instructor',
    email: '<EMAIL>'
  });

  const mockAdmin = createMockUser({
    role: 'admin',
    name: 'Test Admin',
    email: '<EMAIL>'
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mockStorage.clear();
  });

  describe('Authentication and Authorization', () => {
    test('should authenticate user successfully', async () => {
      mockElectron.auth.login.mockResolvedValue({
        success: true,
        user: mockUser,
        token: 'test-token'
      });

      render(
        <CBTProvider>
          <CBTDashboard />
        </CBTProvider>
      );

      // Should show login form initially
      expect(screen.getByText(/secure login/i)).toBeInTheDocument();

      // Fill in login form
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await userEvent.type(emailInput, mockUser.email);
      await userEvent.type(passwordInput, 'password123');
      await userEvent.click(loginButton);

      // Should authenticate and show dashboard
      await waitFor(() => {
        expect(mockElectron.auth.login).toHaveBeenCalledWith({
          email: mockUser.email,
          password: 'password123'
        });
      });
    });

    test('should handle authentication failure', async () => {
      mockElectron.auth.login.mockResolvedValue({
        success: false,
        message: 'Invalid credentials'
      });

      render(
        <CBTProvider>
          <CBTDashboard />
        </CBTProvider>
      );

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await userEvent.type(emailInput, '<EMAIL>');
      await userEvent.type(passwordInput, 'wrongpassword');
      await userEvent.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument();
      });
    });

    test('should enforce role-based access control', async () => {
      // Test student access
      render(
        <CBTProvider>
          <TestCreation />
        </CBTProvider>
      );

      // Students should not be able to access test creation
      expect(screen.getByText(/insufficient permissions/i)).toBeInTheDocument();
    });
  });

  describe('Test Taking Functionality', () => {
    const mockQuestions = [
      createMockQuestion({
        id: 'q1',
        type: 'multiple_choice',
        title: 'Test Question 1',
        content: 'What is 2 + 2?',
        options: [
          { id: 'a', text: '3', isCorrect: false },
          { id: 'b', text: '4', isCorrect: true },
          { id: 'c', text: '5', isCorrect: false },
          { id: 'd', text: '6', isCorrect: false }
        ]
      }),
      createMockQuestion({
        id: 'q2',
        type: 'true_false',
        title: 'Test Question 2',
        content: 'The sky is blue.',
        correctAnswer: 'true'
      })
    ];

    test('should render test taking interface correctly', () => {
      render(
        <CBTProvider>
          <TestTaking />
        </CBTProvider>
      );

      expect(screen.getByText(/secure test mode/i)).toBeInTheDocument();
      expect(screen.getByText(/question navigation/i)).toBeInTheDocument();
      expect(screen.getByText(/progress/i)).toBeInTheDocument();
    });

    test('should handle question navigation', async () => {
      render(
        <CBTProvider>
          <TestTaking />
        </CBTProvider>
      );

      // Should show first question initially
      expect(screen.getByText(/question 1 of/i)).toBeInTheDocument();

      // Navigate to next question
      const nextButton = screen.getByRole('button', { name: /next/i });
      await userEvent.click(nextButton);

      // Should show second question
      expect(screen.getByText(/question 2 of/i)).toBeInTheDocument();

      // Navigate back
      const prevButton = screen.getByRole('button', { name: /previous/i });
      await userEvent.click(prevButton);

      // Should be back to first question
      expect(screen.getByText(/question 1 of/i)).toBeInTheDocument();
    });

    test('should save answers automatically', async () => {
      render(
        <CBTProvider>
          <TestTaking />
        </CBTProvider>
      );

      // Answer a multiple choice question
      const optionB = screen.getByLabelText(/option b/i);
      await userEvent.click(optionB);

      // Should show auto-save indicator
      await waitFor(() => {
        expect(screen.getByText(/saving/i)).toBeInTheDocument();
      });

      await waitFor(() => {
        expect(screen.getByText(/saved/i)).toBeInTheDocument();
      });
    });

    test('should handle test submission', async () => {
      render(
        <CBTProvider>
          <TestTaking />
        </CBTProvider>
      );

      // Navigate to last question
      const questions = screen.getAllByText(/question \d+ of/i);
      const lastQuestionButton = screen.getByRole('button', { name: /submit test/i });
      
      await userEvent.click(lastQuestionButton);

      // Should show confirmation dialog
      expect(screen.getByText(/submit test/i)).toBeInTheDocument();
      expect(screen.getByText(/continue test/i)).toBeInTheDocument();

      // Confirm submission
      const submitButton = screen.getByRole('button', { name: /submit now/i });
      await userEvent.click(submitButton);

      // Should handle submission
      await waitFor(() => {
        expect(screen.queryByText(/submit test/i)).not.toBeInTheDocument();
      });
    });

    test('should track time correctly', async () => {
      jest.useFakeTimers();

      render(
        <CBTProvider>
          <TestTaking />
        </CBTProvider>
      );

      // Should show initial time
      expect(screen.getByText(/60:00/)).toBeInTheDocument();

      // Advance time by 1 minute
      act(() => {
        jest.advanceTimersByTime(60000);
      });

      // Should show updated time
      expect(screen.getByText(/59:00/)).toBeInTheDocument();

      jest.useRealTimers();
    });
  });

  describe('Security Features', () => {
    test('should detect and prevent right-click', async () => {
      const securityLogSpy = jest.spyOn(mockElectron.security, 'logEvent');

      render(
        <CBTProvider>
          <TestTaking />
        </CBTProvider>
      );

      // Simulate right-click
      simulateSecurityEvent('right_click');

      await waitFor(() => {
        expect(securityLogSpy).toHaveBeenCalledWith(
          expect.objectContaining({
            type: 'security_violation',
            action: 'right_click_blocked'
          })
        );
      });
    });

    test('should detect tab switching', async () => {
      const securityLogSpy = jest.spyOn(mockElectron.security, 'logEvent');

      render(
        <CBTProvider>
          <TestTaking />
        </CBTProvider>
      );

      // Simulate tab switching
      simulateSecurityEvent('tab_switch');

      await waitFor(() => {
        expect(securityLogSpy).toHaveBeenCalledWith(
          expect.objectContaining({
            type: 'security_violation',
            action: 'tab_switch_detected'
          })
        );
      });
    });

    test('should prevent keyboard shortcuts', async () => {
      const securityLogSpy = jest.spyOn(mockElectron.security, 'logEvent');

      render(
        <CBTProvider>
          <TestTaking />
        </CBTProvider>
      );

      // Simulate F12 key press
      simulateSecurityEvent('key_combination');

      await waitFor(() => {
        expect(securityLogSpy).toHaveBeenCalledWith(
          expect.objectContaining({
            type: 'security_violation',
            action: 'blocked_shortcut'
          })
        );
      });
    });

    test('should enter secure mode for tests', async () => {
      render(
        <CBTProvider>
          <TestTaking />
        </CBTProvider>
      );

      // Should automatically enter secure mode
      await waitFor(() => {
        expect(mockElectron.security.enterTestMode).toHaveBeenCalled();
      });
    });
  });

  describe('Performance Tests', () => {
    test('should render components within performance thresholds', async () => {
      const renderTime = await measurePerformance(async () => {
        render(
          <CBTProvider>
            <CBTDashboard />
          </CBTProvider>
        );
      });

      // Should render within 1 second
      expect(renderTime).toBeLessThan(1000);
    });

    test('should handle large question sets efficiently', async () => {
      const largeQuestionSet = Array.from({ length: 100 }, (_, i) =>
        createMockQuestion({ id: `q${i}`, title: `Question ${i}` })
      );

      const renderTime = await measurePerformance(async () => {
        render(
          <CBTProvider>
            <TestTaking />
          </CBTProvider>
        );
      });

      // Should still render efficiently with large datasets
      expect(renderTime).toBeLessThan(2000);
    });
  });

  describe('Accessibility Tests', () => {
    test('should meet accessibility standards', () => {
      const { container } = render(
        <CBTProvider>
          <TestTaking />
        </CBTProvider>
      );

      const accessibilityIssues = checkAccessibility(container);
      
      // Should have minimal accessibility issues
      expect(accessibilityIssues.length).toBeLessThan(5);
    });

    test('should support keyboard navigation', async () => {
      render(
        <CBTProvider>
          <TestTaking />
        </CBTProvider>
      );

      // Should be able to navigate with Tab key
      const firstFocusableElement = screen.getByRole('button', { name: /previous/i });
      firstFocusableElement.focus();

      expect(document.activeElement).toBe(firstFocusableElement);

      // Tab to next element
      await userEvent.tab();
      
      const nextFocusableElement = screen.getByRole('button', { name: /flag/i });
      expect(document.activeElement).toBe(nextFocusableElement);
    });

    test('should have proper ARIA labels', () => {
      render(
        <CBTProvider>
          <TestTaking />
        </CBTProvider>
      );

      // Check for proper ARIA labels
      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByRole('navigation')).toBeInTheDocument();
      expect(screen.getByRole('timer')).toBeInTheDocument();
    });
  });

  describe('Data Validation Tests', () => {
    test('should validate test data correctly', () => {
      const validTest = createMockTest();
      const errors = validateTestData(validTest);
      expect(errors).toHaveLength(0);

      const invalidTest = createMockTest({
        title: '',
        duration: -1,
        totalPoints: 0,
        passingScore: 150
      });
      const invalidErrors = validateTestData(invalidTest);
      expect(invalidErrors.length).toBeGreaterThan(0);
    });

    test('should handle malformed data gracefully', () => {
      const malformedData = {
        id: null,
        title: undefined,
        questions: 'not-an-array'
      };

      expect(() => {
        render(
          <CBTProvider>
            <TestTaking />
          </CBTProvider>
        );
      }).not.toThrow();
    });
  });

  describe('Error Handling', () => {
    test('should handle network errors gracefully', async () => {
      // Mock network failure
      mockElectron.auth.login.mockRejectedValue(new Error('Network error'));

      render(
        <CBTProvider>
          <CBTDashboard />
        </CBTProvider>
      );

      const loginButton = screen.getByRole('button', { name: /login/i });
      await userEvent.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText(/network error/i)).toBeInTheDocument();
      });
    });

    test('should recover from component errors', () => {
      // Test error boundary functionality
      const ThrowError = () => {
        throw new Error('Test error');
      };

      expect(() => {
        render(
          <CBTProvider>
            <ThrowError />
          </CBTProvider>
        );
      }).not.toThrow();
    });
  });

  describe('Advanced CBT Features', () => {
    describe('Adaptive Question Engine', () => {
      const mockQuestions = [
        createMockQuestion({ id: 'q1', difficulty: 'easy', category: 'Math' }),
        createMockQuestion({ id: 'q2', difficulty: 'medium', category: 'Math' }),
        createMockQuestion({ id: 'q3', difficulty: 'hard', category: 'Science' })
      ];

      const mockStudentPerformance = {
        averageScore: 75,
        strongAreas: ['Math'],
        weakAreas: ['Science'],
        responseTime: 90
      };

      test('should render adaptive question engine', () => {
        const mockOnRecommendation = jest.fn();

        render(
          <AdaptiveQuestionEngine
            questions={mockQuestions}
            currentAnswers={{}}
            studentPerformance={mockStudentPerformance}
            onQuestionRecommendation={mockOnRecommendation}
          />
        );

        expect(screen.getByText(/adaptive learning engine/i)).toBeInTheDocument();
        expect(screen.getByText(/current performance/i)).toBeInTheDocument();
        expect(screen.getByText(/75%/)).toBeInTheDocument();
      });

      test('should recommend appropriate difficulty questions', async () => {
        const mockOnRecommendation = jest.fn();

        render(
          <AdaptiveQuestionEngine
            questions={mockQuestions}
            currentAnswers={{ q1: 'correct' }}
            studentPerformance={mockStudentPerformance}
            onQuestionRecommendation={mockOnRecommendation}
          />
        );

        await waitFor(() => {
          expect(mockOnRecommendation).toHaveBeenCalledWith(
            expect.arrayContaining([expect.any(String)])
          );
        });
      });

      test('should display weak areas for focus', () => {
        const mockOnRecommendation = jest.fn();

        render(
          <AdaptiveQuestionEngine
            questions={mockQuestions}
            currentAnswers={{}}
            studentPerformance={mockStudentPerformance}
            onQuestionRecommendation={mockOnRecommendation}
          />
        );

        expect(screen.getByText(/focus areas/i)).toBeInTheDocument();
        expect(screen.getByText(/science/i)).toBeInTheDocument();
      });
    });

    describe('Progress Tracker', () => {
      const mockProgressData = {
        questionsAnswered: 5,
        totalQuestions: 10,
        correctAnswers: 4,
        currentScore: 80,
        timeSpent: 1800,
        estimatedTimeRemaining: 1200,
        averageTimePerQuestion: 360,
        difficultyProgression: { easy: 2, medium: 2, hard: 1 },
        categoryProgress: {
          'Math': { answered: 3, correct: 3, total: 5 },
          'Science': { answered: 2, correct: 1, total: 5 }
        },
        streakData: { currentStreak: 3, longestStreak: 5, streakType: 'correct' as const },
        confidenceLevel: 0.8,
        learningVelocity: 1.2
      };

      test('should render progress overview', () => {
        render(
          <ProgressTracker progressData={mockProgressData} />
        );

        expect(screen.getByText(/progress overview/i)).toBeInTheDocument();
        expect(screen.getByText(/50.0%/)).toBeInTheDocument(); // 5/10 questions
        expect(screen.getByText(/80.0%/)).toBeInTheDocument(); // 4/5 accuracy
      });

      test('should show performance prediction', () => {
        render(
          <ProgressTracker progressData={mockProgressData} showDetailedStats={true} />
        );

        expect(screen.getByText(/performance prediction/i)).toBeInTheDocument();
        expect(screen.getByText(/predicted final score/i)).toBeInTheDocument();
      });

      test('should display category breakdown', () => {
        render(
          <ProgressTracker progressData={mockProgressData} showDetailedStats={true} />
        );

        expect(screen.getByText(/category progress/i)).toBeInTheDocument();
        expect(screen.getByText(/math/i)).toBeInTheDocument();
        expect(screen.getByText(/science/i)).toBeInTheDocument();
      });

      test('should trigger milestone callbacks', async () => {
        const mockOnMilestone = jest.fn();

        render(
          <ProgressTracker
            progressData={mockProgressData}
            onMilestoneReached={mockOnMilestone}
          />
        );

        // Should trigger 50% completion milestone
        await waitFor(() => {
          expect(mockOnMilestone).toHaveBeenCalledWith('Halfway Point');
        });
      });
    });

    describe('Drag and Drop Test Builder', () => {
      const mockQuestions = [
        createMockQuestion({ id: 'q1', title: 'Question 1' }),
        createMockQuestion({ id: 'q2', title: 'Question 2' })
      ];

      test('should render test builder interface', () => {
        const mockOnChange = jest.fn();
        const mockOnAdd = jest.fn();

        render(
          <DragDropTestBuilder
            questions={mockQuestions}
            onQuestionsChange={mockOnChange}
            onAddQuestion={mockOnAdd}
          />
        );

        expect(screen.getByText(/test builder/i)).toBeInTheDocument();
        expect(screen.getByText(/drag and drop to reorder/i)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /add question/i })).toBeInTheDocument();
      });

      test('should show question type selector', async () => {
        const mockOnChange = jest.fn();
        const mockOnAdd = jest.fn();

        render(
          <DragDropTestBuilder
            questions={[]}
            onQuestionsChange={mockOnChange}
            onAddQuestion={mockOnAdd}
          />
        );

        const addButton = screen.getByRole('button', { name: /add question/i });
        await userEvent.click(addButton);

        expect(screen.getByText(/choose question type/i)).toBeInTheDocument();
        expect(screen.getByText(/multiple choice/i)).toBeInTheDocument();
        expect(screen.getByText(/true\/false/i)).toBeInTheDocument();
      });

      test('should handle question deletion', async () => {
        const mockOnChange = jest.fn();
        const mockOnAdd = jest.fn();

        render(
          <DragDropTestBuilder
            questions={mockQuestions}
            onQuestionsChange={mockOnChange}
            onAddQuestion={mockOnAdd}
          />
        );

        const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
        await userEvent.click(deleteButtons[0]);

        expect(mockOnChange).toHaveBeenCalledWith([mockQuestions[1]]);
      });

      test('should handle question duplication', async () => {
        const mockOnChange = jest.fn();
        const mockOnAdd = jest.fn();

        render(
          <DragDropTestBuilder
            questions={mockQuestions}
            onQuestionsChange={mockOnChange}
            onAddQuestion={mockOnAdd}
          />
        );

        const duplicateButtons = screen.getAllByRole('button', { name: /duplicate/i });
        await userEvent.click(duplicateButtons[0]);

        expect(mockOnChange).toHaveBeenCalledWith(
          expect.arrayContaining([
            expect.objectContaining({ title: 'Question 1 (Copy)' })
          ])
        );
      });
    });

    describe('Comprehensive Reporting', () => {
      const mockReportData = {
        testAttempts: [
          createMockTestAttempt({ score: 85, status: 'completed' }),
          createMockTestAttempt({ score: 92, status: 'completed' }),
          createMockTestAttempt({ score: 78, status: 'completed' })
        ],
        analytics: {
          totalTests: 3,
          averageScore: 85,
          passRate: 100,
          completionRate: 100
        },
        users: [mockUser],
        timeRange: {
          start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          end: new Date()
        }
      };

      test('should render comprehensive reports', () => {
        const mockOnExport = jest.fn();
        const mockOnEmail = jest.fn();

        render(
          <ComprehensiveReporting
            reportData={mockReportData}
            onExportReport={mockOnExport}
            onEmailReport={mockOnEmail}
          />
        );

        expect(screen.getByText(/comprehensive reports/i)).toBeInTheDocument();
        expect(screen.getByText(/advanced analytics/i)).toBeInTheDocument();
      });

      test('should show key metrics', () => {
        const mockOnExport = jest.fn();
        const mockOnEmail = jest.fn();

        render(
          <ComprehensiveReporting
            reportData={mockReportData}
            onExportReport={mockOnExport}
            onEmailReport={mockOnEmail}
          />
        );

        expect(screen.getByText(/total attempts/i)).toBeInTheDocument();
        expect(screen.getByText(/avg score/i)).toBeInTheDocument();
        expect(screen.getByText(/pass rate/i)).toBeInTheDocument();
      });

      test('should handle export functionality', async () => {
        const mockOnExport = jest.fn();
        const mockOnEmail = jest.fn();

        render(
          <ComprehensiveReporting
            reportData={mockReportData}
            onExportReport={mockOnExport}
            onEmailReport={mockOnEmail}
          />
        );

        const exportButton = screen.getByRole('button', { name: /export/i });
        await userEvent.click(exportButton);

        expect(screen.getByText(/export options/i)).toBeInTheDocument();

        const pdfButton = screen.getByRole('button', { name: /pdf report/i });
        await userEvent.click(pdfButton);

        expect(mockOnExport).toHaveBeenCalledWith('pdf');
      });

      test('should filter data correctly', async () => {
        const mockOnExport = jest.fn();
        const mockOnEmail = jest.fn();

        render(
          <ComprehensiveReporting
            reportData={mockReportData}
            onExportReport={mockOnExport}
            onEmailReport={mockOnEmail}
          />
        );

        const statusFilter = screen.getByDisplayValue(/all status/i);
        await userEvent.selectOptions(statusFilter, 'completed');

        // Should update the displayed data
        expect(screen.getByText(/3/)).toBeInTheDocument(); // Total attempts
      });
    });
  });
});
