import {
  CBTUser,
  Mo<PERSON>le,
  Test,
  TestAttempt,
  Question,
  TestSession,
  SecurityEvent,
  TestAnalytics,
  APIResponse,
  UserRole
} from '../types/cbt.types';

class CBTAPIService {
  private baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
  private sessionToken: string | null = null;

  constructor() {
    // Load token from localStorage if available
    if (typeof window !== 'undefined') {
      this.sessionToken = localStorage.getItem('cbt_token');
    }
  }

  // Helper method to get auth headers
  private getAuthHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (this.sessionToken) {
      headers['Authorization'] = `Bearer ${this.sessionToken}`;
    }

    return headers;
  }

  // Helper method to handle API responses
  private async handleResponse<T>(response: Response): Promise<APIResponse<T>> {
    try {
      const data = await response.json();
      
      if (!response.ok) {
        return {
          success: false,
          message: data.message || 'An error occurred',
          errors: data.errors
        };
      }

      return {
        success: true,
        data: data.data || data,
        message: data.message || 'Success'
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to parse response'
      };
    }
  }

  // Authentication methods
  async login(email: string, password: string): Promise<APIResponse<{ user: CBTUser; token: string }>> {
    try {
      const response = await fetch(`${this.baseUrl}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const result = await this.handleResponse<{ user: CBTUser; token: string }>(response);
      
      if (result.success && result.data) {
        this.sessionToken = result.data.token;
        localStorage.setItem('cbt_token', result.data.token);
        localStorage.setItem('cbt_user', JSON.stringify(result.data.user));
      }

      return result;
    } catch (error) {
      return {
        success: false,
        message: 'Login failed. Please check your connection.'
      };
    }
  }

  async logout(): Promise<APIResponse<void>> {
    try {
      const response = await fetch(`${this.baseUrl}/auth/logout`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
      });

      // Clear local storage regardless of response
      this.sessionToken = null;
      localStorage.removeItem('cbt_token');
      localStorage.removeItem('cbt_user');

      return this.handleResponse<void>(response);
    } catch (error) {
      // Clear local storage even if request fails
      this.sessionToken = null;
      localStorage.removeItem('cbt_token');
      localStorage.removeItem('cbt_user');
      
      return {
        success: true,
        message: 'Logged out successfully'
      };
    }
  }

  async verifyToken(): Promise<APIResponse<CBTUser>> {
    try {
      const response = await fetch(`${this.baseUrl}/auth/verify-token`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });

      return this.handleResponse<CBTUser>(response);
    } catch (error) {
      return {
        success: false,
        message: 'Token verification failed'
      };
    }
  }

  // User management
  async getCurrentUser(): Promise<APIResponse<CBTUser>> {
    try {
      const response = await fetch(`${this.baseUrl}/users/me`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });

      return this.handleResponse<CBTUser>(response);
    } catch (error) {
      return {
        success: false,
        message: 'Failed to fetch user data'
      };
    }
  }

  async updateProfile(userData: Partial<CBTUser>): Promise<APIResponse<CBTUser>> {
    try {
      const response = await fetch(`${this.baseUrl}/users/profile`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(userData),
      });

      return this.handleResponse<CBTUser>(response);
    } catch (error) {
      return {
        success: false,
        message: 'Failed to update profile'
      };
    }
  }

  // Test management
  async getTests(): Promise<APIResponse<Test[]>> {
    try {
      const response = await fetch(`${this.baseUrl}/tests`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });

      return this.handleResponse<Test[]>(response);
    } catch (error) {
      return {
        success: false,
        message: 'Failed to fetch tests'
      };
    }
  }

  async getTest(testId: string): Promise<APIResponse<Test>> {
    try {
      const response = await fetch(`${this.baseUrl}/tests/${testId}`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });

      return this.handleResponse<Test>(response);
    } catch (error) {
      return {
        success: false,
        message: 'Failed to fetch test'
      };
    }
  }

  async createTest(testData: Omit<Test, 'id' | 'createdAt' | 'updatedAt'>): Promise<APIResponse<Test>> {
    try {
      const response = await fetch(`${this.baseUrl}/tests`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(testData),
      });

      return this.handleResponse<Test>(response);
    } catch (error) {
      return {
        success: false,
        message: 'Failed to create test'
      };
    }
  }

  async updateTest(testId: string, testData: Partial<Test>): Promise<APIResponse<Test>> {
    try {
      const response = await fetch(`${this.baseUrl}/tests/${testId}`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(testData),
      });

      return this.handleResponse<Test>(response);
    } catch (error) {
      return {
        success: false,
        message: 'Failed to update test'
      };
    }
  }

  async deleteTest(testId: string): Promise<APIResponse<void>> {
    try {
      const response = await fetch(`${this.baseUrl}/tests/${testId}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders(),
      });

      return this.handleResponse<void>(response);
    } catch (error) {
      return {
        success: false,
        message: 'Failed to delete test'
      };
    }
  }

  // Test session management
  async startTestSession(testId: string): Promise<APIResponse<TestSession>> {
    try {
      const response = await fetch(`${this.baseUrl}/tests/${testId}/start`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
      });

      return this.handleResponse<TestSession>(response);
    } catch (error) {
      return {
        success: false,
        message: 'Failed to start test session'
      };
    }
  }

  async getTestSession(sessionId: string): Promise<APIResponse<TestSession>> {
    try {
      const response = await fetch(`${this.baseUrl}/tests/sessions/${sessionId}`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });

      return this.handleResponse<TestSession>(response);
    } catch (error) {
      return {
        success: false,
        message: 'Failed to fetch test session'
      };
    }
  }

  async submitAnswer(sessionId: string, questionId: string, answer: any): Promise<APIResponse<void>> {
    try {
      const response = await fetch(`${this.baseUrl}/tests/sessions/answer`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          sessionId,
          questionId,
          answer
        }),
      });

      return this.handleResponse<void>(response);
    } catch (error) {
      return {
        success: false,
        message: 'Failed to submit answer'
      };
    }
  }

  async submitTest(sessionId: string): Promise<APIResponse<TestAttempt>> {
    try {
      const response = await fetch(`${this.baseUrl}/tests/sessions/${sessionId}/submit`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
      });

      return this.handleResponse<TestAttempt>(response);
    } catch (error) {
      return {
        success: false,
        message: 'Failed to submit test'
      };
    }
  }

  // Results and analytics
  async getTestResults(testId?: string): Promise<APIResponse<TestAttempt[]>> {
    try {
      const url = testId 
        ? `${this.baseUrl}/tests/${testId}/results`
        : `${this.baseUrl}/tests/results`;
        
      const response = await fetch(url, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });

      return this.handleResponse<TestAttempt[]>(response);
    } catch (error) {
      return {
        success: false,
        message: 'Failed to fetch test results'
      };
    }
  }

  async getStudentAttempts(studentId?: string): Promise<APIResponse<TestAttempt[]>> {
    try {
      const url = studentId 
        ? `${this.baseUrl}/users/${studentId}/attempts`
        : `${this.baseUrl}/users/me/attempts`;
        
      const response = await fetch(url, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });

      return this.handleResponse<TestAttempt[]>(response);
    } catch (error) {
      return {
        success: false,
        message: 'Failed to fetch student attempts'
      };
    }
  }

  // Utility methods
  generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  isAuthenticated(): boolean {
    return !!this.sessionToken;
  }

  getStoredUser(): CBTUser | null {
    if (typeof window === 'undefined') return null;
    
    const userData = localStorage.getItem('cbt_user');
    return userData ? JSON.parse(userData) : null;
  }
}

export const cbtApiService = new CBTAPIService();
