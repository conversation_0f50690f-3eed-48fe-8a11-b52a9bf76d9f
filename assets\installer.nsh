; Custom NSIS installer script for My App
; This file contains custom installer instructions

; Add custom installer pages or modifications here
!macro customInstall
  ; Custom installation steps
  DetailPrint "Installing My App..."
  
  ; Create additional shortcuts if needed
  CreateShortCut "$DESKTOP\My App.lnk" "$INSTDIR\My App.exe" "" "$INSTDIR\My App.exe" 0
  
  ; Register file associations if needed
  ; WriteRegStr HKCR ".myapp" "" "MyApp.Document"
  ; WriteRegStr HKCR "MyApp.Document" "" "My App Document"
  ; WriteRegStr HKCR "MyApp.Document\DefaultIcon" "" "$INSTDIR\My App.exe,0"
  ; WriteRegStr HKCR "MyApp.Document\shell\open\command" "" '"$INSTDIR\My App.exe" "%1"'
!macroend

!macro customUnInstall
  ; Custom uninstallation steps
  DetailPrint "Uninstalling My App..."
  
  ; Remove additional shortcuts
  Delete "$DESKTOP\My App.lnk"
  
  ; Remove file associations if they were created
  ; DeleteRegKey HKCR ".myapp"
  ; DeleteRegKey HKCR "MyApp.Document"
!macroend
