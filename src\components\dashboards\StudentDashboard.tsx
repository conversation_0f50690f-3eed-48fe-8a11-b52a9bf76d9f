import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BookOpen,
  Clock,
  Trophy,
  TrendingUp,
  Calendar,
  FileText,
  Award,
  Target,
  Play,
  CheckCircle,
  AlertCircle,
  Home,
  Users,
  Settings,
  BarChart3,
  Bell
} from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { Sidebar } from '../dashboard/Sidebar';
import { JAMBTestInterface } from '../cbt/JAMBTestInterface';

interface StudentDashboardProps {
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
    avatar?: string;
  };
  onBack: () => void;
  onLogout: () => void;
}

interface TestData {
  id: string;
  title: string;
  subject: string;
  duration: number;
  questions: number;
  status: 'available' | 'in_progress' | 'completed' | 'missed';
  score?: number;
  deadline: Date;
  attempts: number;
  maxAttempts: number;
}

interface StudentStats {
  totalTests: number;
  completedTests: number;
  averageScore: number;
  rank: number;
  totalStudents: number;
  upcomingTests: number;
}

export const StudentDashboard: React.FC<StudentDashboardProps> = ({
  user,
  onBack,
  onLogout
}) => {
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState<'overview' | 'tests' | 'results' | 'schedule'>('overview');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [currentView, setCurrentView] = useState<'dashboard' | 'test'>('dashboard');
  const [stats, setStats] = useState<StudentStats>({
    totalTests: 12,
    completedTests: 8,
    averageScore: 85.5,
    rank: 3,
    totalStudents: 45,
    upcomingTests: 2
  });

  const [tests, setTests] = useState<TestData[]>([
    {
      id: '1',
      title: 'Mathematics Final Exam',
      subject: 'Mathematics',
      duration: 120,
      questions: 50,
      status: 'available',
      deadline: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
      attempts: 0,
      maxAttempts: 1
    },
    {
      id: '2',
      title: 'Physics Quiz - Chapter 5',
      subject: 'Physics',
      duration: 45,
      questions: 20,
      status: 'available',
      deadline: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
      attempts: 0,
      maxAttempts: 2
    },
    {
      id: '3',
      title: 'Chemistry Lab Test',
      subject: 'Chemistry',
      duration: 90,
      questions: 35,
      status: 'completed',
      score: 92,
      deadline: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      attempts: 1,
      maxAttempts: 1
    },
    {
      id: '4',
      title: 'English Literature Essay',
      subject: 'English',
      duration: 180,
      questions: 5,
      status: 'completed',
      score: 78,
      deadline: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      attempts: 1,
      maxAttempts: 1
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'in_progress': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20';
      case 'completed': return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
      case 'missed': return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available': return <Play size={16} />;
      case 'in_progress': return <Clock size={16} />;
      case 'completed': return <CheckCircle size={16} />;
      case 'missed': return <AlertCircle size={16} />;
      default: return <Clock size={16} />;
    }
  };

  const formatTimeRemaining = (deadline: Date) => {
    const now = new Date();
    const diff = deadline.getTime() - now.getTime();

    if (diff < 0) return 'Expired';

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    if (days > 0) return `${days}d ${hours}h`;
    return `${hours}h`;
  };

  const handleSidebarItemClick = (itemId: string) => {
    switch (itemId) {
      case 'dashboard':
        setCurrentView('dashboard');
        setActiveTab('overview');
        break;
      case 'tests':
        setActiveTab('tests');
        break;
      case 'results':
        setActiveTab('results');
        break;
      case 'analytics':
        setActiveTab('schedule');
        break;
      default:
        break;
    }
  };

  const handleStartTest = () => {
    setCurrentView('test');
  };

  const handleSubmitTest = (answers: Record<number, string>) => {
    console.log('Test submitted with answers:', answers);
    setCurrentView('dashboard');
    setActiveTab('results');
  };

  const handleExitTest = () => {
    setCurrentView('dashboard');
  };

  const StatCard = ({ icon: Icon, title, value, subtitle, color }: any) => (
    <motion.div
      whileHover={{ scale: 1.02 }}
      className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700"
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400 font-mono">{title}</p>
          <p className={`text-2xl font-bold ${color} font-mono`}>{value}</p>
          {subtitle && <p className="text-xs text-gray-500 dark:text-gray-500 font-mono">{subtitle}</p>}
        </div>
        <div className={`p-3 rounded-lg ${color.replace('text-', 'bg-').replace('600', '100')} dark:bg-opacity-20`}>
          <Icon size={24} className={color} />
        </div>
      </div>
    </motion.div>
  );

  // Show test interface if in test mode
  if (currentView === 'test') {
    return (
      <JAMBTestInterface
        onSubmitTest={handleSubmitTest}
        onExitTest={handleExitTest}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex">
      {/* Sidebar */}
      <Sidebar
        isCollapsed={sidebarCollapsed}
        onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
        activeItem={activeTab === 'overview' ? 'dashboard' : activeTab}
        onItemClick={handleSidebarItemClick}
        onLogout={onLogout}
      />

      {/* Main content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white font-mono">
                Student Dashboard
              </h1>
            </div>

            <div className="flex items-center space-x-4">
              <button
                onClick={onBack}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors font-mono"
              >
                Back to Welcome
              </button>

              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <Users size={16} className="text-white" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white font-mono">{user.name}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 font-mono capitalize">{user.role}</p>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Navigation Tabs */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="px-6">
            <nav className="flex space-x-8">
              {[
                { id: 'overview', label: 'Overview', icon: Target },
                { id: 'tests', label: 'Tests', icon: BookOpen },
                { id: 'results', label: 'Results', icon: Trophy },
                { id: 'schedule', label: 'Schedule', icon: Calendar }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm font-mono transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  }`}
                >
                  <tab.icon size={16} />
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Main Content */}
        <main className="flex-1 px-6 py-8 overflow-auto">
        {activeTab === 'overview' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-8"
          >
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <StatCard
                icon={BookOpen}
                title="Total Tests"
                value={stats.totalTests}
                color="text-blue-600"
              />
              <StatCard
                icon={CheckCircle}
                title="Completed"
                value={stats.completedTests}
                color="text-green-600"
              />
              <StatCard
                icon={TrendingUp}
                title="Average Score"
                value={`${stats.averageScore}%`}
                color="text-purple-600"
              />
              <StatCard
                icon={Award}
                title="Class Rank"
                value={`#${stats.rank}`}
                subtitle={`of ${stats.totalStudents} students`}
                color="text-orange-600"
              />
            </div>

            {/* Recent Tests */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white font-mono">Recent Tests</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {tests.slice(0, 3).map((test) => (
                    <div key={test.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className={`p-2 rounded-lg ${getStatusColor(test.status)}`}>
                          {getStatusIcon(test.status)}
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-white font-mono">{test.title}</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400 font-mono">
                            {test.subject} • {test.questions} questions • {test.duration} min
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        {test.status === 'completed' && test.score && (
                          <p className="text-lg font-semibold text-gray-900 dark:text-white font-mono">{test.score}%</p>
                        )}
                        {test.status === 'available' && (
                          <p className="text-sm text-gray-500 dark:text-gray-400 font-mono">
                            Due in {formatTimeRemaining(test.deadline)}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {activeTab === 'tests' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white font-mono">Available Tests</h2>
              </div>
              <div className="p-6">
                <div className="grid gap-6">
                  {tests.map((test) => (
                    <div key={test.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white font-mono">{test.title}</h3>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(test.status)} font-mono`}>
                              {test.status.replace('_', ' ')}
                            </span>
                          </div>
                          <p className="text-gray-600 dark:text-gray-400 mb-4 font-mono">{test.subject}</p>
                          
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <p className="text-gray-500 dark:text-gray-400 font-mono">Duration</p>
                              <p className="font-medium text-gray-900 dark:text-white font-mono">{test.duration} min</p>
                            </div>
                            <div>
                              <p className="text-gray-500 dark:text-gray-400 font-mono">Questions</p>
                              <p className="font-medium text-gray-900 dark:text-white font-mono">{test.questions}</p>
                            </div>
                            <div>
                              <p className="text-gray-500 dark:text-gray-400 font-mono">Attempts</p>
                              <p className="font-medium text-gray-900 dark:text-white font-mono">{test.attempts}/{test.maxAttempts}</p>
                            </div>
                            <div>
                              <p className="text-gray-500 dark:text-gray-400 font-mono">Deadline</p>
                              <p className="font-medium text-gray-900 dark:text-white font-mono">
                                {formatTimeRemaining(test.deadline)}
                              </p>
                            </div>
                          </div>
                        </div>
                        
                        <div className="ml-6">
                          {test.status === 'available' && (
                            <button
                              onClick={handleStartTest}
                              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors font-mono"
                            >
                              Start Test
                            </button>
                          )}
                          {test.status === 'completed' && test.score && (
                            <div className="text-right">
                              <p className="text-2xl font-bold text-gray-900 dark:text-white font-mono">{test.score}%</p>
                              <p className="text-sm text-gray-500 dark:text-gray-400 font-mono">Score</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {activeTab === 'results' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6"
          >
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white font-mono mb-6">Test Results</h2>
            <div className="space-y-4">
              {tests.filter(test => test.status === 'completed').map((test) => (
                <div key={test.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white font-mono">{test.title}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 font-mono">{test.subject}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-xl font-bold text-gray-900 dark:text-white font-mono">{test.score}%</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400 font-mono">
                      {test.score! >= 80 ? 'Excellent' : test.score! >= 60 ? 'Good' : 'Needs Improvement'}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        )}

        {activeTab === 'schedule' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6"
          >
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white font-mono mb-6">Upcoming Schedule</h2>
            <div className="space-y-4">
              {tests.filter(test => test.status === 'available').map((test) => (
                <div key={test.id} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white font-mono">{test.title}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 font-mono">
                      {test.subject} • Due: {test.deadline.toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900 dark:text-white font-mono">
                      {formatTimeRemaining(test.deadline)} remaining
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        )}
        </main>
      </div>
    </div>
  );
};
