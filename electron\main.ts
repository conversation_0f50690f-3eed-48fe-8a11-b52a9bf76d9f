import { app, BrowserWindow, ipcMain, Menu, shell, globalShortcut, screen } from 'electron';
import * as path from 'path';
import isDev from 'electron-is-dev';

interface SecuritySettings {
  kioskMode: boolean;
  disableDevTools: boolean;
  disableRightClick: boolean;
  disableKeyboardShortcuts: boolean;
  fullScreenRequired: boolean;
  monitorTabSwitching: boolean;
}

class ElectronApp {
  private mainWindow: BrowserWindow | null = null;
  private securitySettings: SecuritySettings = {
    kioskMode: false,
    disableDevTools: true,
    disableRightClick: true,
    disableKeyboardShortcuts: true,
    fullScreenRequired: true, // Enable full-screen by default for CBT
    monitorTabSwitching: true
  };
  private isTestMode: boolean = false;

  constructor() {
    this.initializeApp();
    this.setupIpcHandlers();
  }

  private initializeApp(): void {
    // Configure app for CBT security
    app.commandLine.appendSwitch('disable-web-security');
    app.commandLine.appendSwitch('disable-features', 'VizDisplayCompositor');

    // Prevent multiple instances
    const gotTheLock = app.requestSingleInstanceLock();
    if (!gotTheLock) {
      app.quit();
      return;
    }

    app.on('second-instance', () => {
      // Someone tried to run a second instance, focus our window instead
      if (this.mainWindow) {
        if (this.mainWindow.isMinimized()) this.mainWindow.restore();
        this.mainWindow.focus();
      }
    });

    // Handle app ready
    app.whenReady().then(() => {
      this.createMainWindow();
      this.setupMenu();
      this.setupIPC();
      this.setupGlobalSecurity();

      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    // Handle window closed
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // Security: Prevent new window creation
    app.on('web-contents-created', (_, contents) => {
      contents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
      });
    });
  }

  private createMainWindow(): void {
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width, height } = primaryDisplay.workAreaSize;

    // Enable kiosk mode for production builds by default
    const isProduction = !isDev;
    const shouldUseKioskMode = this.securitySettings.kioskMode || isProduction;

    // Configure window options based on security settings
    const windowOptions: Electron.BrowserWindowConstructorOptions = {
      width: shouldUseKioskMode ? width : 1200,
      height: shouldUseKioskMode ? height : 800,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: true,
        allowRunningInsecureContent: false,
        experimentalFeatures: false,
        devTools: !this.securitySettings.disableDevTools || Boolean(isDev)
      },
      icon: path.join(__dirname, '../assets/icon.png'),
      show: false,
      titleBarStyle: shouldUseKioskMode ? 'hidden' : 'default',
      frame: !shouldUseKioskMode,
      resizable: !shouldUseKioskMode,
      maximizable: !shouldUseKioskMode,
      minimizable: !shouldUseKioskMode,
      closable: !shouldUseKioskMode,
      fullscreen: shouldUseKioskMode || this.securitySettings.fullScreenRequired,
      kiosk: shouldUseKioskMode,
      alwaysOnTop: shouldUseKioskMode,
      skipTaskbar: shouldUseKioskMode,
      autoHideMenuBar: true, // Hide menu bar by default
      useContentSize: true
    };

    this.mainWindow = new BrowserWindow(windowOptions);

    // Update security settings if using kiosk mode
    if (shouldUseKioskMode) {
      this.securitySettings.kioskMode = true;
    }

    // Load the app
    const startUrl = isDev
      ? 'http://localhost:3000'
      : `file://${path.join(__dirname, '../out/index.html')}`;

    this.mainWindow.loadURL(startUrl);

    // Show window when ready
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();

      // Enter fullscreen if required
      if (this.securitySettings.fullScreenRequired || this.securitySettings.kioskMode) {
        this.mainWindow?.setFullScreen(true);
      }

      if (isDev && !this.securitySettings.disableDevTools) {
        this.mainWindow?.webContents.openDevTools();
      }
    });

    // Handle window closed
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
      this.unregisterSecurityShortcuts();
    });

    // Security: Prevent navigation to external URLs
    this.mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
      const parsedUrl = new URL(navigationUrl);

      if (parsedUrl.origin !== 'http://localhost:3000' && !navigationUrl.startsWith('file://')) {
        event.preventDefault();
      }
    });

    // Security: Disable right-click context menu
    if (this.securitySettings.disableRightClick) {
      this.mainWindow.webContents.on('context-menu', (event) => {
        event.preventDefault();
      });
    }

    // Security: Monitor focus changes for tab switching detection
    if (this.securitySettings.monitorTabSwitching) {
      this.mainWindow.on('blur', () => {
        if (this.isTestMode) {
          console.warn('Security Alert: Window lost focus during test');
          this.mainWindow?.webContents.send('security:focus-lost');
        }
      });

      this.mainWindow.on('focus', () => {
        if (this.isTestMode) {
          this.mainWindow?.webContents.send('security:focus-gained');
        }
      });
    }

    // Security: Prevent leaving fullscreen during test mode
    this.mainWindow.on('leave-full-screen', () => {
      if (this.isTestMode && this.securitySettings.fullScreenRequired) {
        this.mainWindow?.setFullScreen(true);
      }
    });

    // Setup security shortcuts
    this.setupSecurityShortcuts();
  }

  private setupGlobalSecurity(): void {
    // Prevent new window creation globally
    app.on('web-contents-created', (_, contents) => {
      // Prevent navigation to external URLs
      contents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        if (parsedUrl.origin !== 'http://localhost:3000' && !navigationUrl.startsWith('file://')) {
          event.preventDefault();
        }
      });

      // Prevent opening new windows
      contents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
      });

      // Disable certain web features for security
      contents.on('dom-ready', () => {
        if (this.securitySettings.disableRightClick) {
          contents.executeJavaScript(`
            document.addEventListener('contextmenu', e => e.preventDefault());
          `);
        }

        if (this.securitySettings.disableKeyboardShortcuts) {
          contents.executeJavaScript(`
            document.addEventListener('keydown', e => {
              // Disable F12, Ctrl+Shift+I, etc.
              if (e.key === 'F12' ||
                  (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                  (e.ctrlKey && e.shiftKey && e.key === 'J') ||
                  (e.ctrlKey && e.key === 'u') ||
                  (e.ctrlKey && e.key === 's') ||
                  (e.ctrlKey && e.key === 'a') ||
                  (e.ctrlKey && e.key === 'c') ||
                  (e.ctrlKey && e.key === 'v') ||
                  (e.ctrlKey && e.key === 'x')) {
                e.preventDefault();
                e.stopPropagation();
                return false;
              }
            });
          `);
        }
      });
    });

    // Global shortcut registration for security
    if (this.securitySettings.disableKeyboardShortcuts) {
      this.setupSecurityShortcuts();
    }
  }

  private setupSecurityShortcuts(): void {
    if (!this.securitySettings.disableKeyboardShortcuts) return;

    // Disable common shortcuts that could be used to bypass security
    const shortcutsToDisable = [
      'F11', // Fullscreen toggle
      'F12', // Developer tools
      'CommandOrControl+Shift+I', // Developer tools
      'CommandOrControl+Shift+J', // Console
      'CommandOrControl+Shift+C', // Element inspector
      'CommandOrControl+R', // Reload
      'CommandOrControl+Shift+R', // Hard reload
      'CommandOrControl+W', // Close tab
      'CommandOrControl+T', // New tab
      'CommandOrControl+N', // New window
      'CommandOrControl+Shift+N', // New incognito window
      'Alt+F4', // Close window (Windows)
      'CommandOrControl+Q', // Quit application
      'CommandOrControl+H', // Hide window
      'CommandOrControl+M', // Minimize window
      'Alt+Tab', // Switch applications
      'CommandOrControl+Tab', // Switch tabs
      'CommandOrControl+Shift+Tab', // Switch tabs reverse
      'CommandOrControl+U', // View source
      'CommandOrControl+S', // Save page
      'CommandOrControl+P', // Print
      'CommandOrControl+A', // Select all
      'CommandOrControl+C', // Copy
      'CommandOrControl+V', // Paste
      'CommandOrControl+X', // Cut
      'CommandOrControl+Z', // Undo
      'CommandOrControl+Y', // Redo
      'CommandOrControl+F', // Find
      'CommandOrControl+G', // Find next
      'CommandOrControl+Shift+G', // Find previous
      'Escape' // Escape key
    ];

    shortcutsToDisable.forEach(shortcut => {
      globalShortcut.register(shortcut, () => {
        // Do nothing - effectively disabling the shortcut
        console.log(`Blocked shortcut: ${shortcut}`);
      });
    });
  }

  private unregisterSecurityShortcuts(): void {
    globalShortcut.unregisterAll();
  }

  private setupMenu(): void {
    // In kiosk mode or test mode, disable menu completely
    if (this.securitySettings.kioskMode || this.isTestMode) {
      Menu.setApplicationMenu(null);
      return;
    }

    const template: Electron.MenuItemConstructorOptions[] = [
      {
        label: 'File',
        submenu: [
          {
            label: 'Quit',
            accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
            click: () => {
              app.quit();
            }
          }
        ]
      },
      {
        label: 'View',
        submenu: [
          { role: 'reload' },
          { role: 'forceReload' },
          ...(isDev ? [{ role: 'toggleDevTools' as const }] : []),
          { type: 'separator' },
          { role: 'resetZoom' },
          { role: 'zoomIn' },
          { role: 'zoomOut' },
          { type: 'separator' },
          { role: 'togglefullscreen' }
        ]
      },
      {
        label: 'Window',
        submenu: [
          { role: 'minimize' },
          { role: 'close' }
        ]
      }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  private setupIPC(): void {
    // Handle authentication
    ipcMain.handle('auth:login', async (_, credentials) => {
      // Dummy authentication logic
      const { email, password } = credentials;

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Dummy validation
      if (email && password) {
        return {
          success: true,
          user: {
            id: '1',
            email: email,
            name: 'John Doe',
            role: 'user'
          },
          token: 'dummy-jwt-token'
        };
      } else {
        return {
          success: false,
          message: 'Invalid credentials'
        };
      }
    });

    // Handle logout
    ipcMain.handle('auth:logout', async () => {
      this.isTestMode = false;
      this.exitSecureMode();
      return { success: true };
    });

    // Handle app info
    ipcMain.handle('app:getInfo', () => {
      return {
        name: app.getName(),
        version: app.getVersion(),
        platform: process.platform,
        isKioskMode: this.securitySettings.kioskMode,
        isTestMode: this.isTestMode
      };
    });

    // Security: Enter test mode
    ipcMain.handle('security:enterTestMode', (_, settings) => {
      this.isTestMode = true;
      if (settings) {
        this.securitySettings = { ...this.securitySettings, ...settings };
      }
      this.enterSecureMode();
      return { success: true };
    });

    // Security: Exit test mode
    ipcMain.handle('security:exitTestMode', () => {
      this.isTestMode = false;
      this.exitSecureMode();
      return { success: true };
    });

    // Security: Enable kiosk mode
    ipcMain.handle('security:enableKioskMode', () => {
      this.securitySettings.kioskMode = true;
      this.enterKioskMode();
      return { success: true };
    });

    // Security: Disable kiosk mode
    ipcMain.handle('security:disableKioskMode', () => {
      this.securitySettings.kioskMode = false;
      this.exitKioskMode();
      return { success: true };
    });

    // Security: Force fullscreen
    ipcMain.handle('security:forceFullscreen', () => {
      this.mainWindow?.setFullScreen(true);
      return { success: true };
    });

    // Security: Exit fullscreen
    ipcMain.handle('security:exitFullscreen', () => {
      if (!this.isTestMode) {
        this.mainWindow?.setFullScreen(false);
      }
      return { success: true };
    });

    // Handle window controls (with security checks)
    ipcMain.handle('window:minimize', () => {
      if (!this.securitySettings.kioskMode && !this.isTestMode) {
        this.mainWindow?.minimize();
      }
    });

    ipcMain.handle('window:maximize', () => {
      if (!this.securitySettings.kioskMode && !this.isTestMode) {
        if (this.mainWindow?.isMaximized()) {
          this.mainWindow.unmaximize();
        } else {
          this.mainWindow?.maximize();
        }
      }
    });

    ipcMain.handle('window:close', () => {
      if (!this.securitySettings.kioskMode && !this.isTestMode) {
        this.mainWindow?.close();
      }
    });

    // Security logging
    ipcMain.handle('security:logEvent', (_, event) => {
      console.warn('Security Event:', event);
      // In a real implementation, this would log to a security monitoring system
    });
  }

  private enterSecureMode(): void {
    if (!this.mainWindow) return;

    // Force fullscreen if required
    if (this.securitySettings.fullScreenRequired) {
      this.mainWindow.setFullScreen(true);
    }

    // Disable menu in secure mode
    Menu.setApplicationMenu(null);

    // Setup security shortcuts
    this.setupSecurityShortcuts();

    // Prevent window manipulation
    this.mainWindow.setResizable(false);
    this.mainWindow.setMaximizable(false);
    this.mainWindow.setMinimizable(false);
    this.mainWindow.setClosable(false);
  }

  private exitSecureMode(): void {
    if (!this.mainWindow) return;

    // Exit fullscreen
    this.mainWindow.setFullScreen(false);

    // Restore menu
    this.setupMenu();

    // Unregister security shortcuts
    this.unregisterSecurityShortcuts();

    // Restore window manipulation
    this.mainWindow.setResizable(true);
    this.mainWindow.setMaximizable(true);
    this.mainWindow.setMinimizable(true);
    this.mainWindow.setClosable(true);
  }

  private enterKioskMode(): void {
    if (!this.mainWindow) return;

    this.mainWindow.setKiosk(true);
    this.mainWindow.setAlwaysOnTop(true);
    this.mainWindow.setSkipTaskbar(true);
    Menu.setApplicationMenu(null);
  }

  private exitKioskMode(): void {
    if (!this.mainWindow) return;

    this.mainWindow.setKiosk(false);
    this.mainWindow.setAlwaysOnTop(false);
    this.mainWindow.setSkipTaskbar(false);
    this.setupMenu();
  }

  private setupIpcHandlers(): void {
    // Database operations
    ipcMain.handle('database:createUser', async (event, userData) => {
      // Mock implementation - replace with actual database calls
      return { id: 'user_' + Date.now(), ...userData, createdAt: new Date(), updatedAt: new Date() };
    });

    ipcMain.handle('database:getUserById', async (event, id) => {
      // Mock implementation
      return null;
    });

    ipcMain.handle('database:getUserByEmail', async (event, email) => {
      // Mock implementation
      return null;
    });

    ipcMain.handle('database:verifyPassword', async (event, email, password) => {
      // Mock implementation
      return null;
    });

    ipcMain.handle('database:checkHealth', async () => {
      return { mysql: true, mongodb: true };
    });

    // Sync operations
    ipcMain.handle('sync:getAvailableMethods', async () => {
      return ['wifi', 'bluetooth', 'usb', 'file'];
    });

    ipcMain.handle('sync:startDiscovery', async () => {
      return true;
    });

    ipcMain.handle('sync:getDevices', async () => {
      return [];
    });

    // Module operations
    ipcMain.handle('modules:getInstalled', async () => {
      return [];
    });

    // Authentication
    ipcMain.handle('auth:login', async (event, credentials) => {
      // Mock implementation
      return { success: false, message: 'Not implemented' };
    });

    ipcMain.handle('auth:getCurrentUser', async () => {
      return null;
    });

    // System operations
    ipcMain.handle('system:getInfo', async () => {
      return {
        platform: process.platform,
        arch: process.arch,
        version: app.getVersion()
      };
    });

    ipcMain.handle('system:setFullscreen', async (event, fullscreen) => {
      if (this.mainWindow) {
        this.mainWindow.setFullScreen(fullscreen);
      }
    });

    ipcMain.handle('system:minimize', async () => {
      if (this.mainWindow) {
        this.mainWindow.minimize();
      }
    });

    ipcMain.handle('system:maximize', async () => {
      if (this.mainWindow) {
        if (this.mainWindow.isMaximized()) {
          this.mainWindow.unmaximize();
        } else {
          this.mainWindow.maximize();
        }
      }
    });

    ipcMain.handle('system:close', async () => {
      if (this.mainWindow) {
        this.mainWindow.close();
      }
    });

    // App info
    ipcMain.handle('app:getVersion', async () => {
      return app.getVersion();
    });

    ipcMain.handle('app:getPath', async () => {
      return app.getAppPath();
    });
  }
}

// Initialize the app
new ElectronApp();
