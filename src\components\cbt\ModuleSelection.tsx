import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON><PERSON><PERSON>, 
  Clock, 
  Users, 
  Award, 
  Play, 
  Eye,
  Filter,
  Search,
  Grid,
  List,
  Star,
  Calendar,
  BarChart3,
  CheckCircle,
  AlertCircle,
  Lock
} from 'lucide-react';
import { useCBT } from '../../contexts/CBTContext';
import { Module, Test, UserRole } from '../../types/cbt.types';
import { cbtService } from '../../services/cbt.service';

interface ModuleCardProps {
  module: Module;
  userRole: UserRole;
  onSelectModule: (module: Module) => void;
  onStartTest: (test: Test) => void;
  viewMode: 'grid' | 'list';
}

const ModuleCard: React.FC<ModuleCardProps> = ({ 
  module, 
  userRole, 
  onSelectModule, 
  onStartTest,
  viewMode 
}) => {
  const canTakeTest = userRole === 'student';
  const canViewDetails = true;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400';
      case 'draft': return 'text-yellow-400';
      case 'archived': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-900/30 text-green-400 border-green-600/50';
      case 'medium': return 'bg-yellow-900/30 text-yellow-400 border-yellow-600/50';
      case 'hard': return 'bg-red-900/30 text-red-400 border-red-600/50';
      default: return 'bg-gray-900/30 text-gray-400 border-gray-600/50';
    }
  };

  if (viewMode === 'list') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ scale: 1.01 }}
        className="bg-black/60 backdrop-blur-xl rounded-lg p-6 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-300"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 flex-1">
            <div className="w-12 h-12 bg-gradient-to-br from-gray-600 to-black rounded-lg flex items-center justify-center">
              <BookOpen className="text-white" size={24} />
            </div>
            
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <h3 className="text-lg font-bold text-white font-mono">{module.title}</h3>
                {module.difficulty && (
                  <span className={`px-2 py-1 rounded text-xs font-mono border ${getDifficultyColor(module.difficulty)}`}>
                    {module.difficulty.toUpperCase()}
                  </span>
                )}
                {module.status && (
                  <span className={`text-xs font-mono ${getStatusColor(module.status)}`}>
                    {module.status.toUpperCase()}
                  </span>
                )}
              </div>
              
              <p className="text-gray-400 text-sm font-mono mb-3">{module.description}</p>
              
              <div className="flex items-center space-x-6 text-xs text-gray-500 font-mono">
                <div className="flex items-center space-x-1">
                  <Clock size={14} />
                  <span>{module.estimatedDuration} min</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Users size={14} />
                  <span>{module.enrolledCount} enrolled</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Award size={14} />
                  <span>{module.averageScore}% avg</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Calendar size={14} />
                  <span>Updated {new Date(module.updatedAt).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {canViewDetails && (
              <motion.button
                onClick={() => onSelectModule(module)}
                className="px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors font-mono text-sm"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Eye size={16} className="inline mr-2" />
                View
              </motion.button>
            )}
            
            {canTakeTest && module.tests && module.tests.length > 0 && (
              <motion.button
                onClick={() => onStartTest(module.tests[0])}
                className="px-4 py-2 bg-black hover:bg-gray-900 text-white rounded-lg transition-colors font-mono text-sm border border-gray-600"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Play size={16} className="inline mr-2" />
                Start Test
              </motion.button>
            )}
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      whileHover={{ scale: 1.02, y: -5 }}
      className="bg-black/60 backdrop-blur-xl rounded-xl p-6 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-300 group"
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="w-12 h-12 bg-gradient-to-br from-gray-600 to-black rounded-lg flex items-center justify-center">
          <BookOpen className="text-white" size={24} />
        </div>
        
        <div className="flex items-center space-x-2">
          {module.difficulty && (
            <span className={`px-2 py-1 rounded text-xs font-mono border ${getDifficultyColor(module.difficulty)}`}>
              {module.difficulty.toUpperCase()}
            </span>
          )}
          {module.status === 'active' && (
            <CheckCircle className="text-green-400" size={16} />
          )}
          {module.status === 'draft' && (
            <AlertCircle className="text-yellow-400" size={16} />
          )}
          {module.status === 'archived' && (
            <Lock className="text-gray-400" size={16} />
          )}
        </div>
      </div>

      {/* Content */}
      <div className="mb-4">
        <h3 className="text-lg font-bold text-white mb-2 font-mono group-hover:text-gray-200 transition-colors">
          {module.title}
        </h3>
        <p className="text-gray-400 text-sm font-mono line-clamp-2">
          {module.description}
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 text-gray-400 mb-1">
            <Clock size={14} />
            <span className="text-xs font-mono">Duration</span>
          </div>
          <p className="text-white font-bold font-mono">{module.estimatedDuration}m</p>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 text-gray-400 mb-1">
            <Users size={14} />
            <span className="text-xs font-mono">Enrolled</span>
          </div>
          <p className="text-white font-bold font-mono">{module.enrolledCount}</p>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 text-gray-400 mb-1">
            <Award size={14} />
            <span className="text-xs font-mono">Avg Score</span>
          </div>
          <p className="text-white font-bold font-mono">{module.averageScore}%</p>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 text-gray-400 mb-1">
            <Star size={14} />
            <span className="text-xs font-mono">Rating</span>
          </div>
          <p className="text-white font-bold font-mono">{module.rating || 'N/A'}</p>
        </div>
      </div>

      {/* Actions */}
      <div className="flex space-x-2">
        {canViewDetails && (
          <motion.button
            onClick={() => onSelectModule(module)}
            className="flex-1 px-3 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors font-mono text-sm"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Eye size={16} className="inline mr-2" />
            View Details
          </motion.button>
        )}
        
        {canTakeTest && module.tests && module.tests.length > 0 && (
          <motion.button
            onClick={() => onStartTest(module.tests[0])}
            className="flex-1 px-3 py-2 bg-black hover:bg-gray-900 text-white rounded-lg transition-colors font-mono text-sm border border-gray-600"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Play size={16} className="inline mr-2" />
            Start Test
          </motion.button>
        )}
      </div>
    </motion.div>
  );
};

export const ModuleSelection: React.FC = () => {
  const { state, setCurrentModule, setCurrentTest } = useCBT();
  const { user } = state;
  const [modules, setModules] = useState<Module[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterDifficulty, setFilterDifficulty] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  useEffect(() => {
    loadModules();
  }, []);

  const loadModules = async () => {
    try {
      setLoading(true);
      const response = await cbtService.getModules(user?.id || '', user?.role || 'student');
      if (response.success && response.data) {
        setModules(response.data);
      }
    } catch (error) {
      console.error('Error loading modules:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectModule = (module: Module) => {
    setCurrentModule(module);
    // Navigate to module details or test creation based on user role
  };

  const handleStartTest = (test: Test) => {
    setCurrentTest(test);
    // Navigate to test taking interface
  };

  const filteredModules = modules.filter(module => {
    const matchesSearch = module.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         module.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesDifficulty = filterDifficulty === 'all' || module.difficulty === filterDifficulty;
    const matchesStatus = filterStatus === 'all' || module.status === filterStatus;
    
    return matchesSearch && matchesDifficulty && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          className="w-8 h-8 border-2 border-gray-600 border-t-white rounded-full"
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white font-mono">Available Modules</h2>
          <p className="text-gray-400 font-mono">Select a module to view tests or start testing</p>
        </div>
        
        <div className="flex items-center space-x-2">
          <motion.button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'grid' ? 'bg-gray-800 text-white' : 'bg-gray-800/50 text-gray-400 hover:text-white'
            }`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Grid size={18} />
          </motion.button>
          <motion.button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'list' ? 'bg-gray-800 text-white' : 'bg-gray-800/50 text-gray-400 hover:text-white'
            }`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <List size={18} />
          </motion.button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <input
              type="text"
              placeholder="Search modules..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent font-mono"
            />
          </div>
        </div>
        
        <div className="flex space-x-4">
          <select
            value={filterDifficulty}
            onChange={(e) => setFilterDifficulty(e.target.value)}
            className="px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
          >
            <option value="all">All Difficulties</option>
            <option value="easy">Easy</option>
            <option value="medium">Medium</option>
            <option value="hard">Hard</option>
          </select>
          
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="draft">Draft</option>
            <option value="archived">Archived</option>
          </select>
        </div>
      </div>

      {/* Modules Grid/List */}
      {filteredModules.length === 0 ? (
        <div className="text-center py-12">
          <BookOpen className="mx-auto text-gray-600 mb-4" size={48} />
          <h3 className="text-xl font-bold text-gray-400 mb-2 font-mono">No modules found</h3>
          <p className="text-gray-500 font-mono">Try adjusting your search or filters</p>
        </div>
      ) : (
        <div className={
          viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
            : 'space-y-4'
        }>
          {filteredModules.map((module, index) => (
            <motion.div
              key={module.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <ModuleCard
                module={module}
                userRole={state.user?.role || 'student'}
                onSelectModule={handleSelectModule}
                onStartTest={handleStartTest}
                viewMode={viewMode}
              />
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
};
