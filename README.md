# My App - Desktop Application

A beautiful desktop application built with Electron, React, Next.js, Tailwind CSS, and Three.js.

## Features

- 🔐 **Authentication System** - Beautiful login/register forms with dummy authentication
- 🎨 **Stunning UI** - Modern design with Tailwind CSS and Framer Motion animations
- 🌟 **3D Background Effects** - Interactive Three.js particle systems and geometric shapes
- 📱 **Responsive Design** - Works perfectly on different screen sizes
- ⚡ **Fast Performance** - Optimized with Next.js and Electron
- 🔒 **Secure** - Proper security configurations for Electron
- 📦 **Easy Installation** - Windows installer with NSIS

## Technology Stack

- **Frontend**: React 19, Next.js 15, TypeScript
- **Styling**: Tailwind CSS 4, Framer Motion
- **3D Graphics**: Three.js
- **Desktop**: Electron 38
- **Icons**: Lucide React
- **Build**: electron-builder

## Prerequisites

- Node.js 18 or higher
- npm or yarn
- Windows (for building Windows installer)

## Installation & Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd my-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run in development mode**
   ```bash
   # Start Next.js development server and Electron
   npm run electron-dev
   ```

4. **Build for production**
   ```bash
   # Build the complete application
   npm run build-installer
   ```
