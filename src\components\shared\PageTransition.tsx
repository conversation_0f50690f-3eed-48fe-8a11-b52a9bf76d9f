import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'fade' | 'slide' | 'scale' | 'slideUp' | 'slideDown';
  duration?: number;
  delay?: number;
}

const transitionVariants = {
  fade: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 }
  },
  slide: {
    initial: { opacity: 0, x: 30 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -30 }
  },
  scale: {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 1.05 }
  },
  slideUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 }
  },
  slideDown: {
    initial: { opacity: 0, y: -20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: 20 }
  }
};

const easingCurves = {
  smooth: [0.25, 0.46, 0.45, 0.94] as const,
  elegant: [0.4, 0, 0.2, 1] as const,
  professional: [0.25, 0.1, 0.25, 1] as const,
  subtle: [0.16, 1, 0.3, 1] as const
};

export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  className = '',
  variant = 'fade',
  duration = 0.4,
  delay = 0
}) => {
  const variants = transitionVariants[variant];

  return (
    <motion.div
      initial={variants.initial}
      animate={variants.animate}
      exit={variants.exit}
      transition={{
        duration,
        delay,
        ease: easingCurves.professional
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

interface AnimatedPageProps {
  children: React.ReactNode;
  className?: string;
  pageKey: string;
}

export const AnimatedPage: React.FC<AnimatedPageProps> = ({
  children,
  className = '',
  pageKey
}) => {
  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={pageKey}
        initial={{ opacity: 0, scale: 0.98 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 1.02 }}
        transition={{
          duration: 0.5,
          ease: easingCurves.elegant
        }}
        className={className}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
};

interface StaggeredAnimationProps {
  children: React.ReactNode[];
  className?: string;
  staggerDelay?: number;
  variant?: 'fade' | 'slideUp' | 'scale';
}

export const StaggeredAnimation: React.FC<StaggeredAnimationProps> = ({
  children,
  className = '',
  staggerDelay = 0.1,
  variant = 'slideUp'
}) => {
  const variants = transitionVariants[variant];

  return (
    <div className={className}>
      {children.map((child, index) => (
        <motion.div
          key={index}
          initial={variants.initial}
          animate={variants.animate}
          transition={{
            duration: 0.4,
            delay: index * staggerDelay,
            ease: easingCurves.professional
          }}
        >
          {child}
        </motion.div>
      ))}
    </div>
  );
};

interface SlideTransitionProps {
  children: React.ReactNode;
  direction: 'left' | 'right' | 'up' | 'down';
  className?: string;
  duration?: number;
}

export const SlideTransition: React.FC<SlideTransitionProps> = ({
  children,
  direction,
  className = '',
  duration = 0.4
}) => {
  const getInitialPosition = () => {
    switch (direction) {
      case 'left': return { x: -50, opacity: 0 };
      case 'right': return { x: 50, opacity: 0 };
      case 'up': return { y: -30, opacity: 0 };
      case 'down': return { y: 30, opacity: 0 };
      default: return { opacity: 0 };
    }
  };

  return (
    <motion.div
      initial={getInitialPosition()}
      animate={{ x: 0, y: 0, opacity: 1 }}
      exit={getInitialPosition()}
      transition={{
        duration,
        ease: easingCurves.elegant
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
};
