import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  BookOpen, 
  Users, 
  GraduationCap, 
  Sparkles,
  ArrowRight,
  Clock,
  Shield,
  Zap
} from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';

interface AnimatedWelcomeScreenProps {
  onComplete: () => void;
  onSkip: () => void;
}

const AnimatedWelcomeScreenComponent: React.FC<AnimatedWelcomeScreenProps> = ({
  onComplete,
  onSkip
}) => {
  const { theme } = useTheme();
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const welcomeSteps = [
    {
      icon: BookOpen,
      title: "Welcome to CBT System",
      subtitle: "Advanced Computer-Based Testing Platform",
      description: "Experience the future of educational assessment with our comprehensive testing solution.",
      color: "from-blue-500 to-purple-600"
    },
    {
      icon: Shield,
      title: "Secure & Reliable",
      subtitle: "Enterprise-Grade Security",
      description: "Built with advanced security features to ensure fair and tamper-proof examinations.",
      color: "from-green-500 to-teal-600"
    },
    {
      icon: Users,
      title: "Multi-Role Support",
      subtitle: "Students, Instructors & Administrators",
      description: "Tailored experiences for every user role with comprehensive management tools.",
      color: "from-orange-500 to-red-600"
    },
    {
      icon: Zap,
      title: "Offline-First Design",
      subtitle: "Works Anywhere, Anytime",
      description: "Full functionality without internet connection, with intelligent sync capabilities.",
      color: "from-purple-500 to-pink-600"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentStep((prev) => {
        if (prev < welcomeSteps.length - 1) {
          return prev + 1;
        } else {
          clearInterval(timer);
          setTimeout(() => {
            setIsVisible(false);
            setTimeout(onComplete, 500);
          }, 2000);
          return prev;
        }
      });
    }, 3000);

    return () => clearInterval(timer);
  }, [onComplete, welcomeSteps.length]);

  const handleSkip = () => {
    setIsVisible(false);
    setTimeout(onSkip, 300);
  };

  const currentStepData = welcomeSteps[currentStep];
  const IconComponent = currentStepData.icon;

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"
        >
          {/* Animated Background Particles */}
          <div className="absolute inset-0 overflow-hidden">
            {mounted && [...Array(50)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-white rounded-full opacity-20"
                initial={{
                  x: Math.random() * (typeof window !== 'undefined' ? window.innerWidth : 1920),
                  y: Math.random() * (typeof window !== 'undefined' ? window.innerHeight : 1080),
                }}
                animate={{
                  x: Math.random() * (typeof window !== 'undefined' ? window.innerWidth : 1920),
                  y: Math.random() * (typeof window !== 'undefined' ? window.innerHeight : 1080),
                }}
                transition={{
                  duration: Math.random() * 10 + 10,
                  repeat: Infinity,
                  ease: "linear"
                }}
              />
            ))}
          </div>

          {/* Main Content */}
          <div className="relative z-10 text-center max-w-4xl mx-auto px-8">
            {/* Logo/Icon Animation */}
            <motion.div
              key={currentStep}
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ 
                type: "spring", 
                stiffness: 260, 
                damping: 20,
                duration: 0.8 
              }}
              className="mb-8"
            >
              <div className={`inline-flex items-center justify-center w-32 h-32 rounded-full bg-gradient-to-r ${currentStepData.color} shadow-2xl`}>
                <IconComponent size={64} className="text-white" />
              </div>
            </motion.div>

            {/* Title Animation */}
            <motion.h1
              key={`title-${currentStep}`}
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="text-6xl font-bold text-white mb-4 font-mono"
            >
              {currentStepData.title}
            </motion.h1>

            {/* Subtitle Animation */}
            <motion.h2
              key={`subtitle-${currentStep}`}
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.6 }}
              className="text-2xl text-purple-200 mb-6 font-mono"
            >
              {currentStepData.subtitle}
            </motion.h2>

            {/* Description Animation */}
            <motion.p
              key={`desc-${currentStep}`}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.7, duration: 0.6 }}
              className="text-lg text-gray-300 mb-12 max-w-2xl mx-auto leading-relaxed"
            >
              {currentStepData.description}
            </motion.p>

            {/* Progress Indicators */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.9 }}
              className="flex justify-center space-x-3 mb-8"
            >
              {welcomeSteps.map((_, index) => (
                <motion.div
                  key={index}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentStep 
                      ? 'bg-white scale-125' 
                      : index < currentStep 
                        ? 'bg-purple-400' 
                        : 'bg-gray-600'
                  }`}
                  animate={{
                    scale: index === currentStep ? 1.25 : 1,
                  }}
                />
              ))}
            </motion.div>

            {/* Loading Animation */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.1 }}
              className="flex items-center justify-center space-x-2 text-purple-200"
            >
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              >
                <Sparkles size={20} />
              </motion.div>
              <span className="text-sm font-mono">
                {currentStep < welcomeSteps.length - 1 ? 'Loading...' : 'Ready to begin!'}
              </span>
            </motion.div>

            {/* Skip Button */}
            <motion.button
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.5 }}
              onClick={handleSkip}
              className="absolute top-8 right-8 flex items-center space-x-2 px-4 py-2 text-white/70 hover:text-white transition-colors duration-200 font-mono text-sm"
            >
              <span>Skip</span>
              <ArrowRight size={16} />
            </motion.button>

            {/* Version Info */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 2 }}
              className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-gray-500 font-mono"
            >
              CBT System v1.0.0 - Enhanced Edition
            </motion.div>
          </div>

          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent pointer-events-none" />
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Export as dynamic component to prevent hydration issues
export const AnimatedWelcomeScreen = dynamic(
  () => Promise.resolve(AnimatedWelcomeScreenComponent),
  { ssr: false }
);
