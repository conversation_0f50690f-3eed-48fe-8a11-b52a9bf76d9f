{"name": "cbt-system", "version": "1.0.0", "private": true, "main": "dist/main.js", "homepage": "./", "description": "Computer-Based Testing System with Advanced Security Features", "author": "CBT Development Team", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "electron-kiosk": "cross-env NODE_ENV=production electron .", "build-electron": "tsc --project electron/tsconfig.json", "build-app": "next build && next export", "build-all": "npm run build-app && npm run build-electron", "dist": "npm run build-all && electron-builder", "dist-win": "npm run build-all && electron-builder --win", "dist-mac": "npm run build-all && electron-builder --mac", "dist-linux": "npm run build-all && electron-builder --linux", "build-installer": "node scripts/build.js", "clean": "<PERSON><PERSON><PERSON> dist out release", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:debug": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit", "test:security": "jest --testNamePattern=\"Security\"", "test:performance": "jest --testNamePattern=\"Performance\"", "test:accessibility": "jest --testNamePattern=\"Accessibility\"", "test:integration": "jest --testNamePattern=\"Integration\"", "test:unit": "jest --testNamePattern=\"Unit\"", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma migrate reset"}, "dependencies": {"@types/three": "^0.180.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "electron-is-dev": "^3.0.1", "express": "^5.1.0", "framer-motion": "^12.23.12", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.544.0", "mongodb": "^6.19.0", "multer": "^2.0.2", "sqlite3": "^5.1.6", "@prisma/client": "^5.7.1", "next": "15.5.3", "node-cron": "^4.2.1", "rate-limiter-flexible": "^7.3.1", "react": "19.1.0", "react-dom": "19.1.0", "react-router-dom": "^7.9.1", "socket.io": "^4.8.1", "three": "^0.180.0", "uuid": "^13.0.0", "ws": "^8.18.3", "xlsx": "^0.18.5", "xml2js": "^0.6.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@types/xml2js": "^0.4.14", "@types/sqlite3": "^3.1.11", "prisma": "^5.7.1", "babel-jest": "^29.7.0", "concurrently": "^9.2.1", "cross-env": "^10.0.0", "electron": "^38.1.0", "electron-builder": "^26.0.12", "eslint": "^9", "eslint-config-next": "15.5.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^29.7.0", "rimraf": "^6.0.1", "tailwindcss": "^4", "ts-jest": "^29.1.2", "typescript": "^5", "wait-on": "^8.0.5"}, "build": {"appId": "com.cbt.system", "productName": "CBT System", "copyright": "Copyright © 2024 CBT System", "description": "Secure Computer-Based Testing System", "directories": {"output": "release", "buildResources": "assets"}, "files": ["dist/", "out/", "node_modules/", "package.json", "!node_modules/.cache", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "extraResources": [{"from": "assets/", "to": "assets/", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64"]}], "icon": "assets/icon.ico", "publisherName": "CBT System Inc.", "verifyUpdateCodeSignature": false, "requestedExecutionLevel": "requireAdministrator"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.education"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Education"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "installerIcon": "assets/icon.ico", "uninstallerIcon": "assets/icon.ico", "installerHeaderIcon": "assets/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "CBT System", "include": "assets/installer.nsh", "runAfterFinish": true, "menuCategory": "Education"}, "portable": {"artifactName": "${productName}-${version}-portable.${ext}"}, "publish": null}}