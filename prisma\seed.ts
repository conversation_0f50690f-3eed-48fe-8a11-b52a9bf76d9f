import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create default admin user
  const adminExists = await prisma.user.findFirst({
    where: { role: 'admin' }
  });

  if (!adminExists) {
    const hashedPassword = await bcrypt.hash('demo123', 12);
    
    await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'admin',
        passwordHash: hashedPassword,
        firstName: 'System',
        lastName: 'Administrator',
        role: 'admin',
        isActive: true,
        isVerified: true,
        kycStatus: 'verified'
      }
    });

    console.log('✅ Default admin user created');
  }

  // Create sample instructor
  const instructorExists = await prisma.user.findFirst({
    where: { role: 'instructor' }
  });

  if (!instructorExists) {
    const hashedPassword = await bcrypt.hash('demo123', 12);
    
    const instructor = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'instructor',
        passwordHash: hashedPassword,
        firstName: 'John',
        lastName: 'Instructor',
        role: 'instructor',
        isActive: true,
        isVerified: true,
        kycStatus: 'verified',
        employeeId: 'INS001',
        institution: 'CBT University',
        department: 'Computer Science'
      }
    });

    console.log('✅ Sample instructor created');

    // Create a sample module
    const module = await prisma.module.create({
      data: {
        title: 'Introduction to Computer Science',
        description: 'Basic concepts of computer science and programming',
        code: 'CS101',
        instructorId: instructor.id,
        semester: 'Fall 2024',
        academicYear: '2024-2025',
        difficulty: 'easy',
        status: 'active',
        estimatedDuration: '16 weeks',
        maxStudents: 50
      }
    });

    console.log('✅ Sample module created');

    // Create sample questions
    const questions = await Promise.all([
      prisma.question.create({
        data: {
          type: 'multiple_choice',
          title: 'Basic Programming Concept',
          content: 'What is a variable in programming?',
          points: 2,
          difficulty: 'easy',
          explanation: 'A variable is a storage location with an associated name that contains data.',
          createdBy: instructor.id,
          category: 'Programming Basics',
          options: {
            create: [
              { text: 'A storage location for data', isCorrect: true, orderIndex: 1 },
              { text: 'A type of loop', isCorrect: false, orderIndex: 2 },
              { text: 'A function parameter', isCorrect: false, orderIndex: 3 },
              { text: 'A programming language', isCorrect: false, orderIndex: 4 }
            ]
          }
        }
      }),
      prisma.question.create({
        data: {
          type: 'true_false',
          title: 'Programming Logic',
          content: 'Python is a compiled programming language.',
          points: 1,
          difficulty: 'easy',
          explanation: 'Python is an interpreted language, not compiled.',
          createdBy: instructor.id,
          category: 'Programming Languages',
          options: {
            create: [
              { text: 'True', isCorrect: false, orderIndex: 1 },
              { text: 'False', isCorrect: true, orderIndex: 2 }
            ]
          }
        }
      }),
      prisma.question.create({
        data: {
          type: 'essay',
          title: 'Algorithm Design',
          content: 'Explain the concept of an algorithm and provide an example.',
          points: 5,
          difficulty: 'medium',
          maxWords: 200,
          rubric: 'Clear definition (2 points), Correct example (2 points), Writing quality (1 point)',
          createdBy: instructor.id,
          category: 'Algorithms'
        }
      })
    ]);

    console.log('✅ Sample questions created');

    // Create a sample exam
    const exam = await prisma.exam.create({
      data: {
        title: 'CS101 Midterm Exam',
        description: 'Midterm examination covering basic programming concepts',
        instructions: 'Answer all questions. You have 60 minutes to complete this exam.',
        moduleId: module.id,
        duration: 60,
        totalPoints: 8,
        passingScore: 5,
        maxAttempts: 2,
        shuffleQuestions: true,
        shuffleOptions: true,
        showResults: true,
        allowReview: false,
        difficulty: 'medium',
        estimatedTime: 45,
        createdBy: instructor.id,
        isPublished: true,
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
      }
    });

    // Add questions to the exam
    await Promise.all(
      questions.map((question, index) =>
        prisma.examQuestion.create({
          data: {
            testId: exam.id,
            questionId: question.id,
            orderIndex: index + 1
          }
        })
      )
    );

    console.log('✅ Sample exam created');
  }

  // Create sample student
  const studentExists = await prisma.user.findFirst({
    where: { role: 'student' }
  });

  if (!studentExists) {
    const hashedPassword = await bcrypt.hash('demo123', 12);
    
    const student = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'student',
        passwordHash: hashedPassword,
        firstName: 'Jane',
        lastName: 'Student',
        role: 'student',
        isActive: true,
        isVerified: true,
        kycStatus: 'verified',
        studentId: 'STU001',
        institution: 'CBT University',
        department: 'Computer Science'
      }
    });

    console.log('✅ Sample student created');

    // Enroll student in the module
    const module = await prisma.module.findFirst({
      where: { code: 'CS101' }
    });

    if (module) {
      await prisma.moduleEnrollment.create({
        data: {
          moduleId: module.id,
          studentId: student.id,
          status: 'enrolled',
          progress: 0
        }
      });

      console.log('✅ Student enrolled in module');
    }
  }

  // Create system settings
  const settingsExist = await prisma.systemSetting.findFirst();
  if (!settingsExist) {
    await prisma.systemSetting.createMany({
      data: [
        {
          settingKey: 'system_name',
          settingValue: 'CBT System',
          settingType: 'string',
          description: 'System name displayed in the interface',
          isPublic: true
        },
        {
          settingKey: 'max_test_duration',
          settingValue: '180',
          settingType: 'number',
          description: 'Maximum test duration in minutes',
          isPublic: false
        },
        {
          settingKey: 'auto_save_interval',
          settingValue: '30',
          settingType: 'number',
          description: 'Auto-save interval in seconds',
          isPublic: true
        },
        {
          settingKey: 'security_monitoring',
          settingValue: 'true',
          settingType: 'boolean',
          description: 'Enable security monitoring during tests',
          isPublic: false
        }
      ]
    });

    console.log('✅ System settings created');
  }

  console.log('🎉 Database seeding completed successfully!');
  console.log('📋 Default credentials:');
  console.log('   Admin: <EMAIL> / demo123');
  console.log('   Instructor: <EMAIL> / demo123');
  console.log('   Student: <EMAIL> / demo123');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
