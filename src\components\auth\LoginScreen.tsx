import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  User, 
  Lock, 
  Mail, 
  Eye, 
  EyeOff, 
  LogIn, 
  ArrowLeft,
  AlertCircle,
  CheckCircle,
  Loader
} from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { EnhancedThreeBackground } from '../shared/EnhancedThreeBackground';

interface LoginScreenProps {
  onLogin: (credentials: LoginCredentials) => Promise<LoginResult>;
  onBack: () => void;
}

interface LoginCredentials {
  identifier: string; // email or student ID
  password: string;
  rememberMe: boolean;
}

interface LoginResult {
  success: boolean;
  user?: {
    id: string;
    name: string;
    email: string;
    role: 'student' | 'instructor' | 'admin';
    avatar?: string;
  };
  message?: string;
}

export const LoginScreen: React.FC<LoginScreenProps> = ({ onLogin, onBack }) => {
  const { theme } = useTheme();
  const [credentials, setCredentials] = useState<LoginCredentials>({
    identifier: '',
    password: '',
    rememberMe: false
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await onLogin(credentials);
      
      if (result.success) {
        setSuccess('Login successful! Redirecting...');
        // The parent component will handle the redirect
      } else {
        setError(result.message || 'Login failed. Please check your credentials.');
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof LoginCredentials, value: string | boolean) => {
    setCredentials(prev => ({ ...prev, [field]: value }));
    if (error) setError(null); // Clear error when user starts typing
  };

  const isValidEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const isValidStudentId = (id: string) => {
    return /^[A-Z0-9]{6,12}$/i.test(id);
  };

  const isValidIdentifier = isValidEmail(credentials.identifier) || isValidStudentId(credentials.identifier);

  const handleDemoLogin = async (role: 'student' | 'instructor' | 'admin') => {
    const demoCredentials = {
      student: { identifier: '<EMAIL>', password: 'demo123' },
      instructor: { identifier: '<EMAIL>', password: 'demo123' },
      admin: { identifier: '<EMAIL>', password: 'demo123' }
    };

    const demo = demoCredentials[role];
    setCredentials({
      identifier: demo.identifier,
      password: demo.password,
      rememberMe: false
    });

    // Auto-submit the form
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await onLogin({
        identifier: demo.identifier,
        password: demo.password,
        rememberMe: false
      });

      if (result.success) {
        setSuccess(`Demo ${role} login successful! Redirecting...`);
      } else {
        setError(result.message || 'Demo login failed. Please try again.');
      }
    } catch (err) {
      setError('Demo login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      <EnhancedThreeBackground theme="auth" />
      
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="w-full max-w-md"
        >
          {/* Back Button */}
          <motion.button
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            onClick={onBack}
            className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors mb-6 font-mono"
          >
            <ArrowLeft size={20} />
            <span>Back to Welcome</span>
          </motion.button>

          {/* Login Card */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
            className="bg-white/90 dark:bg-gray-900/90 backdrop-blur-lg rounded-2xl shadow-2xl p-8 border border-gray-200/50 dark:border-gray-700/50"
          >
            {/* Header */}
            <div className="text-center mb-8">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.5, type: "spring", stiffness: 200 }}
                className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4"
              >
                <User size={32} className="text-white" />
              </motion.div>
              
              <motion.h1
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="text-2xl font-bold text-gray-900 dark:text-white mb-2 font-mono"
              >
                Welcome Back
              </motion.h1>
              
              <motion.p
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 }}
                className="text-gray-600 dark:text-gray-400 font-mono"
              >
                Sign in to access your dashboard
              </motion.p>
            </div>

            {/* Alert Messages */}
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center space-x-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3 mb-4"
              >
                <AlertCircle size={16} className="text-red-500" />
                <span className="text-red-700 dark:text-red-400 text-sm font-mono">{error}</span>
              </motion.div>
            )}

            {success && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center space-x-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3 mb-4"
              >
                <CheckCircle size={16} className="text-green-500" />
                <span className="text-green-700 dark:text-green-400 text-sm font-mono">{success}</span>
              </motion.div>
            )}

            {/* Login Form */}
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Email/ID Input */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.8 }}
              >
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-mono">
                  Email or Student ID
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail size={20} className="text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={credentials.identifier}
                    onChange={(e) => handleInputChange('identifier', e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white font-mono transition-all"
                    placeholder="Enter email or student ID"
                    required
                  />
                </div>
                {credentials.identifier && !isValidIdentifier && (
                  <p className="mt-1 text-xs text-red-500 font-mono">
                    Please enter a valid email or student ID (6-12 characters)
                  </p>
                )}
              </motion.div>

              {/* Password Input */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.9 }}
              >
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-mono">
                  Password
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock size={20} className="text-gray-400" />
                  </div>
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={credentials.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    className="w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white font-mono transition-all"
                    placeholder="Enter your password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
              </motion.div>

              {/* Remember Me */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.0 }}
                className="flex items-center justify-between"
              >
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={credentials.rememberMe}
                    onChange={(e) => handleInputChange('rememberMe', e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300 font-mono">Remember me</span>
                </label>
                
                <button
                  type="button"
                  className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-mono"
                >
                  Forgot password?
                </button>
              </motion.div>

              {/* Submit Button */}
              <motion.button
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.1 }}
                type="submit"
                disabled={isLoading || !isValidIdentifier || !credentials.password}
                className="w-full flex items-center justify-center space-x-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all font-mono"
              >
                {isLoading ? (
                  <>
                    <Loader size={20} className="animate-spin" />
                    <span>Signing in...</span>
                  </>
                ) : (
                  <>
                    <LogIn size={20} />
                    <span>Sign In</span>
                  </>
                )}
              </motion.button>
            </form>

            {/* Demo Login Buttons */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.2 }}
              className="mt-6"
            >
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 font-mono text-center">Quick Demo Login:</p>
              <div className="grid grid-cols-3 gap-2">
                <button
                  type="button"
                  onClick={() => handleDemoLogin('student')}
                  disabled={isLoading}
                  className="flex flex-col items-center p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-all disabled:opacity-50"
                >
                  <User size={16} className="text-blue-600 dark:text-blue-400 mb-1" />
                  <span className="text-xs font-mono text-blue-700 dark:text-blue-300">Student</span>
                </button>

                <button
                  type="button"
                  onClick={() => handleDemoLogin('instructor')}
                  disabled={isLoading}
                  className="flex flex-col items-center p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-all disabled:opacity-50"
                >
                  <User size={16} className="text-green-600 dark:text-green-400 mb-1" />
                  <span className="text-xs font-mono text-green-700 dark:text-green-300">Instructor</span>
                </button>

                <button
                  type="button"
                  onClick={() => handleDemoLogin('admin')}
                  disabled={isLoading}
                  className="flex flex-col items-center p-3 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-all disabled:opacity-50"
                >
                  <User size={16} className="text-purple-600 dark:text-purple-400 mb-1" />
                  <span className="text-xs font-mono text-purple-700 dark:text-purple-300">Admin</span>
                </button>
              </div>
            </motion.div>

            {/* Demo Credentials Info */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.3 }}
              className="mt-4 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700"
            >
              <p className="text-xs text-gray-600 dark:text-gray-400 mb-2 font-mono font-semibold">Demo Credentials:</p>
              <div className="space-y-1 text-xs text-gray-500 dark:text-gray-500 font-mono">
                <p><strong>Student:</strong> <EMAIL> / demo123</p>
                <p><strong>Instructor:</strong> <EMAIL> / demo123</p>
                <p><strong>Admin:</strong> <EMAIL> / demo123</p>
              </div>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};
