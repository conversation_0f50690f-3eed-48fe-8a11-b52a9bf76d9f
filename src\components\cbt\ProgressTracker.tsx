import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  Target, 
  Award,
  BarChart3,
  CheckCircle,
  AlertCircle,
  Zap,
  Brain
} from 'lucide-react';

interface ProgressData {
  questionsAnswered: number;
  totalQuestions: number;
  correctAnswers: number;
  currentScore: number;
  timeSpent: number;
  estimatedTimeRemaining: number;
  averageTimePerQuestion: number;
  difficultyProgression: {
    easy: number;
    medium: number;
    hard: number;
  };
  categoryProgress: {
    [category: string]: {
      answered: number;
      correct: number;
      total: number;
    };
  };
  streakData: {
    currentStreak: number;
    longestStreak: number;
    streakType: 'correct' | 'incorrect' | 'none';
  };
  confidenceLevel: number;
  learningVelocity: number;
}

interface ProgressTrackerProps {
  progressData: ProgressData;
  targetScore?: number;
  showDetailedStats?: boolean;
  onMilestoneReached?: (milestone: string) => void;
}

export const ProgressTracker: React.FC<ProgressTrackerProps> = ({
  progressData,
  targetScore = 70,
  showDetailedStats = true,
  onMilestoneReached
}) => {
  const [milestones, setMilestones] = useState<string[]>([]);
  const [previousProgress, setPreviousProgress] = useState<ProgressData | null>(null);

  useEffect(() => {
    checkMilestones();
    setPreviousProgress(progressData);
  }, [progressData]);

  const checkMilestones = () => {
    const newMilestones: string[] = [];
    
    // Check various milestones
    if (progressData.questionsAnswered === Math.floor(progressData.totalQuestions * 0.25)) {
      newMilestones.push('25% Complete');
    }
    if (progressData.questionsAnswered === Math.floor(progressData.totalQuestions * 0.5)) {
      newMilestones.push('Halfway Point');
    }
    if (progressData.questionsAnswered === Math.floor(progressData.totalQuestions * 0.75)) {
      newMilestones.push('75% Complete');
    }
    if (progressData.currentScore >= targetScore && previousProgress && previousProgress.currentScore < targetScore) {
      newMilestones.push('Target Score Reached');
    }
    if (progressData.streakData.currentStreak >= 5 && progressData.streakData.streakType === 'correct') {
      newMilestones.push('5-Question Streak');
    }
    if (progressData.streakData.currentStreak >= 10 && progressData.streakData.streakType === 'correct') {
      newMilestones.push('Perfect 10 Streak');
    }

    newMilestones.forEach(milestone => {
      if (!milestones.includes(milestone)) {
        onMilestoneReached?.(milestone);
      }
    });

    setMilestones(prev => [...new Set([...prev, ...newMilestones])]);
  };

  const getProgressPercentage = () => {
    return (progressData.questionsAnswered / progressData.totalQuestions) * 100;
  };

  const getAccuracyPercentage = () => {
    if (progressData.questionsAnswered === 0) return 0;
    return (progressData.correctAnswers / progressData.questionsAnswered) * 100;
  };

  const getTimeEfficiency = () => {
    const expectedTimePerQuestion = 120; // 2 minutes
    const efficiency = expectedTimePerQuestion / progressData.averageTimePerQuestion;
    return Math.min(efficiency * 100, 200); // Cap at 200%
  };

  const getPredictedFinalScore = () => {
    const accuracy = getAccuracyPercentage();
    const timeEfficiency = getTimeEfficiency();
    const confidenceBonus = progressData.confidenceLevel * 10;
    
    // Weighted prediction based on current performance
    return Math.min(accuracy + (timeEfficiency - 100) * 0.1 + confidenceBonus, 100);
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m ${secs}s`;
  };

  const getPerformanceTrend = () => {
    if (!previousProgress) return 'stable';
    
    const currentAccuracy = getAccuracyPercentage();
    const previousAccuracy = previousProgress.questionsAnswered > 0 
      ? (previousProgress.correctAnswers / previousProgress.questionsAnswered) * 100 
      : 0;
    
    if (currentAccuracy > previousAccuracy + 5) return 'improving';
    if (currentAccuracy < previousAccuracy - 5) return 'declining';
    return 'stable';
  };

  const trend = getPerformanceTrend();
  const progressPercentage = getProgressPercentage();
  const accuracy = getAccuracyPercentage();
  const predictedScore = getPredictedFinalScore();

  return (
    <div className="space-y-4">
      {/* Main Progress Bar */}
      <div className="bg-black/60 backdrop-blur-xl rounded-xl p-4 border border-gray-600/50">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-bold text-white font-mono">Progress Overview</h3>
          <div className="flex items-center space-x-2">
            {trend === 'improving' && <TrendingUp className="text-green-400" size={16} />}
            {trend === 'declining' && <TrendingDown className="text-red-400" size={16} />}
            <span className={`text-sm font-mono ${
              trend === 'improving' ? 'text-green-400' :
              trend === 'declining' ? 'text-red-400' : 'text-gray-400'
            }`}>
              {trend === 'improving' ? 'Improving' :
               trend === 'declining' ? 'Needs Focus' : 'Steady'}
            </span>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-400 text-sm font-mono">
              {progressData.questionsAnswered} of {progressData.totalQuestions} questions
            </span>
            <span className="text-white font-mono font-bold">
              {progressPercentage.toFixed(1)}%
            </span>
          </div>
          <div className="w-full h-3 bg-gray-800 rounded-full overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-gray-600 to-white"
              initial={{ width: 0 }}
              animate={{ width: `${progressPercentage}%` }}
              transition={{ duration: 0.5, ease: 'easeOut' }}
            />
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 text-gray-400 mb-1">
              <Target size={12} />
              <span className="text-xs font-mono">Accuracy</span>
            </div>
            <p className={`text-lg font-bold font-mono ${
              accuracy >= 80 ? 'text-green-400' :
              accuracy >= 60 ? 'text-yellow-400' : 'text-red-400'
            }`}>
              {accuracy.toFixed(1)}%
            </p>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 text-gray-400 mb-1">
              <Clock size={12} />
              <span className="text-xs font-mono">Time Spent</span>
            </div>
            <p className="text-lg font-bold text-white font-mono">
              {formatTime(progressData.timeSpent)}
            </p>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 text-gray-400 mb-1">
              <Award size={12} />
              <span className="text-xs font-mono">Current Score</span>
            </div>
            <p className={`text-lg font-bold font-mono ${
              progressData.currentScore >= targetScore ? 'text-green-400' : 'text-yellow-400'
            }`}>
              {progressData.currentScore.toFixed(1)}%
            </p>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 text-gray-400 mb-1">
              <Zap size={12} />
              <span className="text-xs font-mono">Streak</span>
            </div>
            <p className={`text-lg font-bold font-mono ${
              progressData.streakData.streakType === 'correct' ? 'text-green-400' : 'text-gray-400'
            }`}>
              {progressData.streakData.currentStreak}
            </p>
          </div>
        </div>
      </div>

      {/* Detailed Statistics */}
      {showDetailedStats && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Performance Prediction */}
          <div className="bg-black/60 backdrop-blur-xl rounded-xl p-4 border border-gray-600/50">
            <h4 className="text-md font-bold text-white mb-3 font-mono flex items-center">
              <Brain className="mr-2" size={16} />
              Performance Prediction
            </h4>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-sm font-mono">Predicted Final Score</span>
                <span className={`font-mono font-bold ${
                  predictedScore >= targetScore ? 'text-green-400' : 'text-yellow-400'
                }`}>
                  {predictedScore.toFixed(1)}%
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-sm font-mono">Time Efficiency</span>
                <span className={`font-mono ${
                  getTimeEfficiency() > 100 ? 'text-green-400' : 'text-yellow-400'
                }`}>
                  {getTimeEfficiency().toFixed(0)}%
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-sm font-mono">Confidence Level</span>
                <span className="text-white font-mono">
                  {(progressData.confidenceLevel * 100).toFixed(0)}%
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-sm font-mono">Learning Velocity</span>
                <span className={`font-mono ${
                  progressData.learningVelocity > 1 ? 'text-green-400' : 'text-yellow-400'
                }`}>
                  {progressData.learningVelocity.toFixed(1)}x
                </span>
              </div>
            </div>
          </div>

          {/* Category Breakdown */}
          <div className="bg-black/60 backdrop-blur-xl rounded-xl p-4 border border-gray-600/50">
            <h4 className="text-md font-bold text-white mb-3 font-mono flex items-center">
              <BarChart3 className="mr-2" size={16} />
              Category Progress
            </h4>
            
            <div className="space-y-2">
              {Object.entries(progressData.categoryProgress).map(([category, data]) => {
                const categoryAccuracy = data.answered > 0 ? (data.correct / data.answered) * 100 : 0;
                const categoryProgress = (data.answered / data.total) * 100;
                
                return (
                  <div key={category} className="space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400 text-xs font-mono">{category}</span>
                      <span className="text-white text-xs font-mono">
                        {data.answered}/{data.total} ({categoryAccuracy.toFixed(0)}%)
                      </span>
                    </div>
                    <div className="w-full h-2 bg-gray-800 rounded-full overflow-hidden">
                      <div 
                        className={`h-full transition-all duration-300 ${
                          categoryAccuracy >= 80 ? 'bg-green-500' :
                          categoryAccuracy >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${categoryProgress}%` }}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* Recent Milestones */}
      {milestones.length > 0 && (
        <div className="bg-black/60 backdrop-blur-xl rounded-xl p-4 border border-gray-600/50">
          <h4 className="text-md font-bold text-white mb-3 font-mono flex items-center">
            <CheckCircle className="mr-2 text-green-400" size={16} />
            Recent Achievements
          </h4>
          <div className="flex flex-wrap gap-2">
            {milestones.slice(-5).map((milestone, index) => (
              <motion.span
                key={milestone}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                className="px-3 py-1 bg-green-900/30 text-green-400 rounded-full text-xs font-mono border border-green-600/50"
              >
                {milestone}
              </motion.span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProgressTracker;
