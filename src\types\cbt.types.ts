// User roles in the CBT system
export type UserRole = 'admin' | 'instructor' | 'student';

// Enhanced user interface for CBT
export interface CBTUser {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  avatar?: string;
  institution?: string;
  department?: string;
  studentId?: string; // For students
  employeeId?: string; // For instructors/admins
  permissions: string[];
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Question types supported by the CBT system
export type QuestionType = 
  | 'multiple_choice' 
  | 'true_false' 
  | 'essay' 
  | 'fill_blank' 
  | 'matching' 
  | 'ordering' 
  | 'numeric'
  | 'file_upload';

// Question difficulty levels
export type DifficultyLevel = 'easy' | 'medium' | 'hard';

// Question interface
export interface Question {
  id: string;
  type: QuestionType;
  title: string;
  content: string;
  points: number;
  difficulty: DifficultyLevel;
  timeLimit?: number; // in seconds
  options?: QuestionOption[];
  correctAnswer?: string | string[] | number;
  acceptableAnswers?: string[]; // For fill-in-the-blank questions
  tolerance?: number; // For numeric questions
  explanation?: string;
  media?: MediaFile[];
  maxWords?: number; // For essay questions
  rubric?: string; // For essay questions
  workRequired?: boolean; // For numeric questions requiring work shown
  units?: string; // For numeric questions with units
  tags: string[];
  category: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Question options for multiple choice, matching, etc.
export interface QuestionOption {
  id: string;
  text: string;
  isCorrect?: boolean;
  media?: MediaFile;
}

// Media files for questions
export interface MediaFile {
  id: string;
  type: 'image' | 'video' | 'audio' | 'document';
  url: string;
  filename: string;
  size: number;
  mimeType: string;
}

// Test/Quiz configuration
export interface Test {
  id: string;
  title: string;
  description: string;
  instructions: string;
  duration: number; // in minutes
  totalPoints: number;
  passingScore: number;
  maxAttempts: number;
  shuffleQuestions: boolean;
  shuffleOptions: boolean;
  showResults: boolean;
  allowReview: boolean;
  isPublished: boolean;
  startDate?: Date;
  endDate?: Date;
  questions: Question[];
  categories: string[];
  difficulty?: DifficultyLevel;
  estimatedTime?: number;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Test attempt by a student
export interface TestAttempt {
  id: string;
  testId: string;
  testTitle?: string;
  studentId: string;
  studentName?: string;
  attemptNumber?: number;
  startTime: Date;
  endTime?: Date;
  duration: number; // actual time taken in seconds
  score: number;
  totalQuestions?: number;
  correctAnswers?: number;
  incorrectAnswers?: number;
  skippedQuestions?: number;
  passingScore?: number;
  percentage: number;
  passed: boolean;
  answers: StudentAnswer[];
  timeSpentPerQuestion?: Record<string, number>;
  flaggedQuestions?: string[];
  submittedAt?: Date;
  status: 'in_progress' | 'completed' | 'abandoned' | 'submitted';
  ipAddress?: string;
  userAgent?: string;
  flaggedEvents: SecurityEvent[];
  createdAt?: Date;
  updatedAt?: Date;
}

// Student's answer to a question
export interface StudentAnswer {
  questionId: string;
  answer: string | string[] | number | File;
  timeSpent: number; // in seconds
  isCorrect: boolean;
  pointsEarned: number;
  flagged: boolean;
  submittedAt: Date;
}

// Security events during test taking
export interface SecurityEvent {
  id: string;
  type: 'tab_switch' | 'window_blur' | 'copy_attempt' | 'paste_attempt' | 'right_click' | 'key_combination' | 'fullscreen_exit';
  timestamp: Date;
  details: string;
  severity: 'low' | 'medium' | 'high';
}

// Module/Course structure
export interface Module {
  id: string;
  title: string;
  description: string;
  code: string;
  instructor: string;
  tests: Test[];
  students: string[];
  isActive: boolean;
  difficulty?: DifficultyLevel;
  status?: 'active' | 'inactive' | 'archived' | 'draft';
  totalStudents?: number;
  enrolledCount?: number;
  completedTests?: number;
  averageScore?: number;
  rating?: number;
  estimatedDuration?: string;
  semester: string;
  academicYear: string;
  createdAt: Date;
  updatedAt: Date;
}

// Analytics and reporting
export interface TestAnalytics {
  testId: string;
  totalAttempts: number;
  averageScore: number;
  passRate: number;
  averageDuration: number;
  questionAnalytics: QuestionAnalytics[];
  difficultyDistribution: Record<DifficultyLevel, number>;
  scoreDistribution: number[];
}

export interface QuestionAnalytics {
  questionId: string;
  correctAnswers: number;
  incorrectAnswers: number;
  averageTimeSpent: number;
  difficultyRating: number;
  discriminationIndex: number;
}

// Application state interfaces
export interface CBTAppState {
  user: CBTUser | null;
  currentModule: Module | null;
  currentTest: Test | null;
  currentAttempt: TestAttempt | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// API response interfaces
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  message: string;
  errors?: string[];
}

// Test session configuration
export interface TestSession {
  id: string;
  testId: string;
  studentId: string;
  sessionToken: string;
  startTime: Date;
  expiryTime: Date;
  isActive: boolean;
  securitySettings: SecuritySettings;
}

export interface SecuritySettings {
  disableRightClick: boolean;
  disableCopy: boolean;
  disablePaste: boolean;
  disableDevTools: boolean;
  fullScreenRequired: boolean;
  monitorTabSwitching: boolean;
  allowedApplications: string[];
  blockedWebsites: string[];
}

// Navigation and routing
export type CBTRoute = 
  | 'login'
  | 'dashboard'
  | 'modules'
  | 'test-creation'
  | 'test-taking'
  | 'results'
  | 'analytics'
  | 'settings'
  | 'profile';

export interface NavigationItem {
  id: string;
  label: string;
  route: CBTRoute;
  icon: string;
  roles: UserRole[];
  children?: NavigationItem[];
}
