import React from 'react';
import { motion } from 'framer-motion';
import { 
  Home, 
  Users, 
  Settings, 
  BarChart3, 
  FileText, 
  Bell, 
  LogOut,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { NavigationItem } from '../../types/app.types';

interface SidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  activeItem: string;
  onItemClick: (itemId: string) => void;
  onLogout: () => void;
}

const navigationItems: NavigationItem[] = [
  { id: 'dashboard', label: 'Dashboard', icon: 'Home', path: '/dashboard' },
  { id: 'tests', label: 'Tests', icon: 'FileText', path: '/tests' },
  { id: 'results', label: 'Results', icon: 'BarChart3', path: '/results' },
  { id: 'analytics', label: 'Schedule', icon: 'Bell', path: '/schedule' },
  { id: 'settings', label: 'Settings', icon: 'Settings', path: '/settings' }
];

const iconMap = {
  Home,
  <PERSON>,
  <PERSON><PERSON><PERSON>3,
  <PERSON>Text,
  <PERSON>,
  Settings
};

export const Sidebar: React.FC<SidebarProps> = ({
  isCollapsed,
  onToggle,
  activeItem,
  onItemClick,
  onLogout
}) => {
  return (
    <motion.div
      initial={{ x: -300 }}
      animate={{ x: 0, width: isCollapsed ? 80 : 280 }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      className="bg-gray-900/95 backdrop-blur-lg border-r border-gray-700/50 flex flex-col h-full relative"
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-700/50">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <motion.h2
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="text-xl font-bold text-white font-mono"
            >
              CBT System
            </motion.h2>
          )}
          <button
            onClick={onToggle}
            className="p-2 rounded-lg bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white transition-colors"
          >
            {isCollapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
          </button>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {navigationItems.map((item) => {
            const IconComponent = iconMap[item.icon as keyof typeof iconMap];
            const isActive = activeItem === item.id;

            return (
              <li key={item.id}>
                <motion.button
                  onClick={() => onItemClick(item.id)}
                  className={`
                    w-full flex items-center p-3 rounded-lg transition-all duration-200
                    ${isActive
                      ? 'bg-black text-white shadow-lg shadow-black/25 border border-gray-600'
                      : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                    }
                  `}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <IconComponent size={20} />
                  {!isCollapsed && (
                    <motion.span
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      className="ml-3 font-medium"
                    >
                      {item.label}
                    </motion.span>
                  )}
                </motion.button>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-700/50">
        <motion.button
          onClick={onLogout}
          className="w-full flex items-center p-3 rounded-lg text-gray-300 hover:bg-red-600 hover:text-white transition-all duration-200"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <LogOut size={20} />
          {!isCollapsed && (
            <motion.span
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -10 }}
              className="ml-3 font-medium"
            >
              Logout
            </motion.span>
          )}
        </motion.button>
      </div>
    </motion.div>
  );
};
