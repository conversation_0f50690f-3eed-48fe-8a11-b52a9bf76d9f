import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Home, 
  BookOpen, 
  PlusCircle, 
  Play, 
  BarChart3, 
  Settings, 
  User, 
  LogOut,
  ChevronLeft,
  ChevronRight,
  Shield,
  FileText,
  Users,
  Clock,
  Award
} from 'lucide-react';
import { CBTRoute, UserRole } from '../../types/cbt.types';

interface CBTSidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  activeRoute: CBTRoute;
  onRouteChange: (route: CBTRoute) => void;
  userRole: UserRole;
  onLogout: () => void;
}

interface NavigationItem {
  id: CBTRoute;
  label: string;
  icon: React.ComponentType<{ size?: number }>;
  roles: UserRole[];
  description?: string;
}

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: Home,
    roles: ['admin', 'instructor', 'student'],
    description: 'Overview and statistics'
  },
  {
    id: 'modules',
    label: 'Modules',
    icon: BookOpen,
    roles: ['admin', 'instructor', 'student'],
    description: 'Available test modules'
  },
  {
    id: 'test-creation',
    label: 'Create Test',
    icon: PlusCircle,
    roles: ['admin', 'instructor'],
    description: 'Build new tests'
  },
  {
    id: 'test-taking',
    label: 'Take Test',
    icon: Play,
    roles: ['student'],
    description: 'Active test session'
  },
  {
    id: 'results',
    label: 'Results',
    icon: Award,
    roles: ['admin', 'instructor', 'student'],
    description: 'Test results and scores'
  },
  {
    id: 'analytics',
    label: 'Analytics',
    icon: BarChart3,
    roles: ['admin', 'instructor'],
    description: 'Performance analytics'
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    roles: ['admin', 'instructor'],
    description: 'System configuration'
  },
  {
    id: 'profile',
    label: 'Profile',
    icon: User,
    roles: ['admin', 'instructor', 'student'],
    description: 'User profile settings'
  }
];

export const CBTSidebar: React.FC<CBTSidebarProps> = ({
  isCollapsed,
  onToggle,
  activeRoute,
  onRouteChange,
  userRole,
  onLogout
}) => {
  const filteredItems = navigationItems.filter(item => 
    item.roles.includes(userRole)
  );

  const getRoleDisplayName = (role: UserRole) => {
    switch (role) {
      case 'admin': return 'Administrator';
      case 'instructor': return 'Instructor';
      case 'student': return 'Student';
      default: return 'User';
    }
  };

  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case 'admin': return Shield;
      case 'instructor': return Users;
      case 'student': return User;
      default: return User;
    }
  };

  const RoleIcon = getRoleIcon(userRole);

  return (
    <motion.div
      initial={{ x: -300 }}
      animate={{ x: 0, width: isCollapsed ? 80 : 280 }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      className="bg-black/95 backdrop-blur-lg border-r border-gray-700/50 flex flex-col h-full relative"
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-700/50">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="flex items-center space-x-3"
            >
              <div className="w-8 h-8 bg-gradient-to-br from-gray-600 to-black rounded-lg flex items-center justify-center">
                <FileText className="text-white" size={16} />
              </div>
              <div>
                <h2 className="text-lg font-bold text-white font-mono">CBT SYSTEM</h2>
                <p className="text-xs text-gray-400 font-mono">v2.0.0</p>
              </div>
            </motion.div>
          )}
          
          <motion.button
            onClick={onToggle}
            className="p-2 rounded-lg bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 hover:text-white transition-all duration-200"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {isCollapsed ? <ChevronRight size={16} /> : <ChevronLeft size={16} />}
          </motion.button>
        </div>
      </div>

      {/* User Info */}
      <div className="p-4 border-b border-gray-700/50">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-gray-600 to-gray-800 rounded-full flex items-center justify-center">
            <RoleIcon className="text-white" size={20} />
          </div>
          {!isCollapsed && (
            <motion.div
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -10 }}
            >
              <p className="text-white font-medium font-mono">{getRoleDisplayName(userRole)}</p>
              <p className="text-gray-400 text-sm font-mono">Access Level</p>
            </motion.div>
          )}
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {filteredItems.map((item) => {
            const IconComponent = item.icon;
            const isActive = activeRoute === item.id;

            return (
              <li key={item.id}>
                <motion.button
                  onClick={() => onRouteChange(item.id)}
                  className={`
                    w-full flex items-center p-3 rounded-lg transition-all duration-200 group
                    ${isActive 
                      ? 'bg-gray-800 text-white shadow-lg shadow-gray-800/25 border border-gray-600' 
                      : 'text-gray-300 hover:bg-gray-800/50 hover:text-white'
                    }
                  `}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  title={isCollapsed ? item.label : undefined}
                >
                  <IconComponent size={20} />
                  {!isCollapsed && (
                    <motion.div
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      className="ml-3 flex-1 text-left"
                    >
                      <span className="font-medium font-mono">{item.label}</span>
                      {item.description && (
                        <p className="text-xs text-gray-500 font-mono">{item.description}</p>
                      )}
                    </motion.div>
                  )}
                  
                  {isActive && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="w-2 h-2 bg-white rounded-full ml-auto"
                    />
                  )}
                </motion.button>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-700/50">
        <motion.button
          onClick={onLogout}
          className="w-full flex items-center p-3 rounded-lg text-gray-300 hover:bg-red-900/30 hover:text-red-300 transition-all duration-200 group"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <LogOut size={20} />
          {!isCollapsed && (
            <motion.span
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -10 }}
              className="ml-3 font-medium font-mono"
            >
              Logout
            </motion.span>
          )}
        </motion.button>
        
        {!isCollapsed && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="mt-4 text-center"
          >
            <div className="flex items-center justify-center space-x-2 text-gray-500">
              <Clock size={12} />
              <span className="text-xs font-mono">
                {new Date().toLocaleTimeString()}
              </span>
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};
