import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CBTLoginForm } from './CBTLoginForm';
import { EnhancedThreeBackground } from '../shared/EnhancedThreeBackground';
import { SlideTransition, StaggeredAnimation } from '../shared/PageTransition';
import { UserRole } from '../../types/cbt.types';

export const CBTAuthPage: React.FC = () => {
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null);

  const roles = [
    {
      id: 'admin' as UserRole,
      title: 'ADMINISTRATOR',
      description: 'System administration and management',
      icon: '⚙️',
      color: 'from-gray-800 to-black'
    },
    {
      id: 'instructor' as UserRole,
      title: 'INSTRUCTOR',
      description: 'Create and manage tests and assessments',
      icon: '👨‍🏫',
      color: 'from-gray-700 to-gray-900'
    },
    {
      id: 'student' as UserRole,
      title: 'STUDENT',
      description: 'Take tests and view results',
      icon: '🎓',
      color: 'from-gray-600 to-gray-800'
    }
  ];

  const handleRoleSelect = (role: UserRole) => {
    setSelectedRole(role);
  };

  const handleBackToRoles = () => {
    setSelectedRole(null);
  };

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-black via-gray-900 to-black">
      <EnhancedThreeBackground particleCount={1500} shapeCount={25} lineCount={40} animated theme="auth" />
      
      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-black/80 via-gray-900/60 to-black/80" />
      
      {/* Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-6xl mx-auto">
          
          <AnimatePresence mode="wait">
            {!selectedRole ? (
              <motion.div
                key="role-selection"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 1.05 }}
                transition={{ duration: 0.4, ease: [0.25, 0.1, 0.25, 1] }}
                className="text-center"
              >
                {/* Header */}
                <SlideTransition direction="down" duration={0.6}>
                  <div className="mb-12">
                    <h1 className="text-6xl lg:text-8xl font-bold text-white mb-6 font-mono">
                      CBT SYSTEM
                    </h1>
                    <p className="text-xl lg:text-2xl text-gray-300 font-mono">
                      Computer-Based Testing Platform
                    </p>
                    <div className="w-32 h-1 bg-gradient-to-r from-transparent via-gray-500 to-transparent mx-auto mt-6"></div>
                  </div>
                </SlideTransition>

                {/* Role Selection */}
                <StaggeredAnimation
                  staggerDelay={0.1}
                  variant="slideUp"
                  className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto"
                >
                  {roles.map((role) => (
                    <motion.button
                      key={role.id}
                      onClick={() => handleRoleSelect(role.id)}
                      className={`bg-gradient-to-br ${role.color} p-8 rounded-2xl border border-gray-600/50 hover:border-gray-500 transition-all duration-300 group`}
                      whileHover={{ scale: 1.02, y: -5 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="text-4xl mb-4">{role.icon}</div>
                      <h3 className="text-xl font-bold text-white mb-2 font-mono">{role.title}</h3>
                      <p className="text-gray-400 text-sm font-mono">{role.description}</p>
                      <div className="mt-4 text-gray-500 group-hover:text-gray-400 transition-colors font-mono text-xs">
                        Click to continue →
                      </div>
                    </motion.button>
                  ))}
                </StaggeredAnimation>

                {/* Features */}
                <SlideTransition direction="up" duration={0.6} className="mt-16">
                  <div className="flex flex-wrap gap-4 justify-center">
                    <div className="bg-black/40 backdrop-blur-sm rounded-lg px-4 py-2 border border-gray-600">
                      <span className="text-white font-medium font-mono">🔒 Secure Testing</span>
                    </div>
                    <div className="bg-black/40 backdrop-blur-sm rounded-lg px-4 py-2 border border-gray-600">
                      <span className="text-white font-medium font-mono">📊 Real-time Analytics</span>
                    </div>
                    <div className="bg-black/40 backdrop-blur-sm rounded-lg px-4 py-2 border border-gray-600">
                      <span className="text-white font-medium font-mono">⚡ Anti-cheating</span>
                    </div>
                  </div>
                </SlideTransition>
              </motion.div>
            ) : (
              <motion.div
                key="login-form"
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -30 }}
                transition={{ duration: 0.4, ease: [0.25, 0.1, 0.25, 1] }}
                className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
              >
                {/* Left side - Role info */}
                <SlideTransition direction="left" duration={0.6}>
                  <div className="text-center lg:text-left">
                    <button
                      onClick={handleBackToRoles}
                      className="inline-flex items-center text-gray-400 hover:text-gray-300 transition-colors mb-8 font-mono"
                    >
                      ← Back to role selection
                    </button>
                    
                    <div className="text-6xl mb-6">
                      {roles.find(r => r.id === selectedRole)?.icon}
                    </div>
                    <h2 className="text-4xl font-bold text-white mb-4 font-mono">
                      {roles.find(r => r.id === selectedRole)?.title}
                    </h2>
                    <p className="text-xl text-gray-300 mb-8 font-mono">
                      {roles.find(r => r.id === selectedRole)?.description}
                    </p>
                    
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                        <span className="text-gray-400 font-mono">Secure authentication</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                        <span className="text-gray-400 font-mono">Role-based access control</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                        <span className="text-gray-400 font-mono">Session monitoring</span>
                      </div>
                    </div>
                  </div>
                </SlideTransition>

                {/* Right side - Login form */}
                <SlideTransition direction="right" duration={0.6}>
                  <CBTLoginForm role={selectedRole} />
                </SlideTransition>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Footer */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1, duration: 0.5 }}
        className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-center"
      >
        <p className="text-gray-400 text-sm font-mono">
          © 2024 CBT System. Professional Computer-Based Testing Platform
        </p>
      </motion.div>
    </div>
  );
};
