// Module system types
export type ModuleType = 
  | 'cbt' 
  | 'lms' 
  | 'attendance' 
  | 'gradebook' 
  | 'library' 
  | 'fee_management' 
  | 'timetable' 
  | 'calendar'
  | 'student_records'
  | 'staff_management'
  | 'inventory'
  | 'transport'
  | 'hostel'
  | 'communication';

export interface Module {
  id: string;
  name: string;
  description: string;
  type: ModuleType;
  icon: string;
  version: string;
  isActive: boolean;
  isInstalled: boolean;
  permissions: string[];
  settings: ModuleSettings;
  dependencies: string[];
  createdBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ModuleSettings {
  [key: string]: any;
  theme?: {
    primaryColor?: string;
    secondaryColor?: string;
    layout?: 'grid' | 'list' | 'card';
  };
  features?: {
    [featureName: string]: boolean;
  };
  notifications?: {
    email?: boolean;
    sms?: boolean;
    push?: boolean;
  };
  security?: {
    requireAuth?: boolean;
    roleBasedAccess?: boolean;
    auditLog?: boolean;
  };
}

// CBT Module specific types
export interface CBTModuleSettings extends ModuleSettings {
  examSettings: {
    defaultDuration: number;
    allowReview: boolean;
    shuffleQuestions: boolean;
    shuffleOptions: boolean;
    showTimer: boolean;
    fullScreenRequired: boolean;
    preventCheating: boolean;
  };
  securitySettings: {
    disableRightClick: boolean;
    disableCopy: boolean;
    disablePaste: boolean;
    disableDevTools: boolean;
    monitorTabSwitching: boolean;
    screenshotDetection: boolean;
  };
  gradingSettings: {
    passingScore: number;
    gradingScale: 'percentage' | 'points' | 'letter';
    showResults: 'immediate' | 'after_submission' | 'manual';
  };
}

// LMS Module specific types
export interface LMSModuleSettings extends ModuleSettings {
  courseSettings: {
    allowSelfEnrollment: boolean;
    requireApproval: boolean;
    certificateGeneration: boolean;
    progressTracking: boolean;
  };
  contentSettings: {
    allowDownloads: boolean;
    videoQuality: 'low' | 'medium' | 'high' | 'auto';
    maxFileSize: number;
    supportedFormats: string[];
  };
}

// Attendance Module specific types
export interface AttendanceModuleSettings extends ModuleSettings {
  trackingSettings: {
    method: 'manual' | 'biometric' | 'rfid' | 'qr_code';
    graceTime: number;
    lateThreshold: number;
    autoMarkAbsent: boolean;
  };
  reportSettings: {
    minimumAttendance: number;
    reportFrequency: 'daily' | 'weekly' | 'monthly';
    parentNotification: boolean;
  };
}

// Gradebook Module specific types
export interface GradebookModuleSettings extends ModuleSettings {
  gradingSettings: {
    scale: 'percentage' | 'points' | 'letter' | 'gpa';
    passingGrade: number;
    weightedGrades: boolean;
    extraCredit: boolean;
  };
  reportSettings: {
    transcriptFormat: 'standard' | 'detailed' | 'summary';
    includeComments: boolean;
    parentAccess: boolean;
  };
}

// Library Module specific types
export interface LibraryModuleSettings extends ModuleSettings {
  catalogSettings: {
    allowReservations: boolean;
    maxBooksPerUser: number;
    loanPeriod: number;
    renewalLimit: number;
  };
  digitalSettings: {
    allowDigitalAccess: boolean;
    downloadLimit: number;
    offlineAccess: boolean;
  };
}

// Fee Management Module specific types
export interface FeeManagementModuleSettings extends ModuleSettings {
  paymentSettings: {
    acceptedMethods: string[];
    lateFeePercentage: number;
    installmentAllowed: boolean;
    discountRules: any[];
  };
  reportSettings: {
    autoReminders: boolean;
    reminderFrequency: number;
    parentNotification: boolean;
  };
}

// Module capability interface
export interface ModuleCapability {
  name: string;
  description: string;
  required: boolean;
  permissions: string[];
}

// Module installation status
export type ModuleStatus = 'not_installed' | 'installing' | 'installed' | 'updating' | 'error';

// Module marketplace entry
export interface ModuleMarketplaceEntry {
  id: string;
  name: string;
  description: string;
  type: ModuleType;
  version: string;
  author: string;
  rating: number;
  downloads: number;
  price: number;
  screenshots: string[];
  features: string[];
  requirements: {
    minVersion: string;
    dependencies: string[];
    permissions: string[];
  };
  changelog: {
    version: string;
    date: Date;
    changes: string[];
  }[];
}

// Module API interface
export interface ModuleAPI {
  initialize(): Promise<void>;
  destroy(): Promise<void>;
  getSettings(): ModuleSettings;
  updateSettings(settings: Partial<ModuleSettings>): Promise<void>;
  getCapabilities(): ModuleCapability[];
  checkPermissions(permission: string): boolean;
  exportData(): Promise<any>;
  importData(data: any): Promise<void>;
  getHealthStatus(): Promise<{
    status: 'healthy' | 'warning' | 'error';
    message?: string;
    details?: any;
  }>;
}

// Module registry
export interface ModuleRegistry {
  modules: Map<string, Module>;
  apis: Map<string, ModuleAPI>;
  register(module: Module, api: ModuleAPI): void;
  unregister(moduleId: string): void;
  get(moduleId: string): { module: Module; api: ModuleAPI } | undefined;
  getByType(type: ModuleType): { module: Module; api: ModuleAPI }[];
  getActive(): { module: Module; api: ModuleAPI }[];
}

// Module events
export type ModuleEvent = 
  | 'module_installed'
  | 'module_uninstalled'
  | 'module_activated'
  | 'module_deactivated'
  | 'module_updated'
  | 'module_error'
  | 'settings_changed';

export interface ModuleEventData {
  moduleId: string;
  type: ModuleEvent;
  timestamp: Date;
  data?: any;
  error?: string;
}
