import * as THREE from 'three';

export const createParticleSystem = (count: number = 1000): THREE.Points => {
  const geometry = new THREE.BufferGeometry();
  const positions = new Float32Array(count * 3);
  const colors = new Float32Array(count * 3);

  for (let i = 0; i < count * 3; i += 3) {
    // Position
    positions[i] = (Math.random() - 0.5) * 2000;
    positions[i + 1] = (Math.random() - 0.5) * 2000;
    positions[i + 2] = (Math.random() - 0.5) * 2000;

    // Color - Monochromatic black/gray palette
    const color = new THREE.Color();
    // Use only grayscale colors for professional monochromatic look
    const grayValue = Math.random() * 0.4 + 0.3; // Range from dark gray to light gray
    color.setRGB(grayValue, grayValue, grayValue);
    colors[i] = color.r;
    colors[i + 1] = color.g;
    colors[i + 2] = color.b;
  }

  geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
  geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

  const material = new THREE.PointsMaterial({
    size: 2,
    vertexColors: true,
    transparent: true,
    opacity: 0.8
  });

  return new THREE.Points(geometry, material);
};

export const createWaveGeometry = (width: number = 100, height: number = 100): THREE.PlaneGeometry => {
  const geometry = new THREE.PlaneGeometry(width, height, 32, 32);
  const positions = geometry.attributes.position.array as Float32Array;

  for (let i = 0; i < positions.length; i += 3) {
    const x = positions[i];
    const y = positions[i + 1];
    positions[i + 2] = Math.sin(x * 0.1) * Math.cos(y * 0.1) * 5;
  }

  geometry.attributes.position.needsUpdate = true;
  geometry.computeVertexNormals();

  return geometry;
};

export const createGradientMaterial = (color1: string, color2: string): THREE.ShaderMaterial => {
  return new THREE.ShaderMaterial({
    uniforms: {
      color1: { value: new THREE.Color(color1) },
      color2: { value: new THREE.Color(color2) }
    },
    vertexShader: `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `,
    fragmentShader: `
      uniform vec3 color1;
      uniform vec3 color2;
      varying vec2 vUv;
      void main() {
        gl_FragColor = vec4(mix(color1, color2, vUv.y), 1.0);
      }
    `
  });
};

export const animateParticles = (particles: THREE.Points, time: number): void => {
  const positions = particles.geometry.attributes.position.array as Float32Array;

  for (let i = 0; i < positions.length; i += 3) {
    positions[i + 1] += Math.sin(time * 0.001 + positions[i] * 0.01) * 0.5;
  }

  particles.geometry.attributes.position.needsUpdate = true;
  particles.rotation.y += 0.002;
};

export const createFloatingCubes = (count: number = 50): THREE.Group => {
  const group = new THREE.Group();
  
  for (let i = 0; i < count; i++) {
    const geometry = new THREE.BoxGeometry(
      Math.random() * 2 + 1,
      Math.random() * 2 + 1,
      Math.random() * 2 + 1
    );
    
    const material = new THREE.MeshPhongMaterial({
      color: new THREE.Color().setRGB(Math.random() * 0.3 + 0.2, Math.random() * 0.3 + 0.2, Math.random() * 0.3 + 0.2),
      transparent: true,
      opacity: 0.5
    });
    
    const cube = new THREE.Mesh(geometry, material);
    
    cube.position.set(
      (Math.random() - 0.5) * 100,
      (Math.random() - 0.5) * 100,
      (Math.random() - 0.5) * 100
    );
    
    cube.rotation.set(
      Math.random() * Math.PI,
      Math.random() * Math.PI,
      Math.random() * Math.PI
    );
    
    group.add(cube);
  }
  
  return group;
};

export const createGeometricShapes = (count: number = 30): THREE.Group => {
  const group = new THREE.Group();

  const geometries = [
    () => new THREE.TetrahedronGeometry(1),
    () => new THREE.OctahedronGeometry(1),
    () => new THREE.IcosahedronGeometry(1),
    () => new THREE.DodecahedronGeometry(1)
  ];

  for (let i = 0; i < count; i++) {
    const geometry = geometries[Math.floor(Math.random() * geometries.length)]();

    const material = new THREE.MeshPhongMaterial({
      color: new THREE.Color().setRGB(Math.random() * 0.4 + 0.2, Math.random() * 0.4 + 0.2, Math.random() * 0.4 + 0.2),
      transparent: true,
      opacity: 0.4,
      wireframe: Math.random() > 0.5
    });

    const mesh = new THREE.Mesh(geometry, material);

    mesh.position.set(
      (Math.random() - 0.5) * 200,
      (Math.random() - 0.5) * 200,
      (Math.random() - 0.5) * 200
    );

    mesh.rotation.set(
      Math.random() * Math.PI,
      Math.random() * Math.PI,
      Math.random() * Math.PI
    );

    mesh.scale.setScalar(Math.random() * 2 + 0.5);

    group.add(mesh);
  }

  return group;
};

export const createNeonLines = (count: number = 50): THREE.Group => {
  const group = new THREE.Group();

  for (let i = 0; i < count; i++) {
    const points = [];
    const numPoints = Math.floor(Math.random() * 5) + 3;

    for (let j = 0; j < numPoints; j++) {
      points.push(new THREE.Vector3(
        (Math.random() - 0.5) * 100,
        (Math.random() - 0.5) * 100,
        (Math.random() - 0.5) * 100
      ));
    }

    const geometry = new THREE.BufferGeometry().setFromPoints(points);
    const material = new THREE.LineBasicMaterial({
      color: new THREE.Color().setRGB(Math.random() * 0.3 + 0.3, Math.random() * 0.3 + 0.3, Math.random() * 0.3 + 0.3),
      transparent: true,
      opacity: 0.4
    });

    const line = new THREE.Line(geometry, material);
    group.add(line);
  }

  return group;
};

export const animateGeometricShapes = (group: THREE.Group, time: number): void => {
  group.children.forEach((child, index) => {
    if (child instanceof THREE.Mesh) {
      child.rotation.x += 0.01 * (index % 2 === 0 ? 1 : -1);
      child.rotation.y += 0.015 * (index % 3 === 0 ? 1 : -1);
      child.rotation.z += 0.008 * (index % 4 === 0 ? 1 : -1);

      child.position.y += Math.sin(time * 0.001 + index) * 0.1;
    }
  });
};

export const resizeRenderer = (
  renderer: THREE.WebGLRenderer,
  camera: THREE.PerspectiveCamera,
  container: HTMLElement
): void => {
  const width = container.clientWidth;
  const height = container.clientHeight;

  renderer.setSize(width, height);
  camera.aspect = width / height;
  camera.updateProjectionMatrix();
};
