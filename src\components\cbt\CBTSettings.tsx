import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON><PERSON><PERSON>, 
  User, 
  Shield, 
  Bell, 
  Monitor, 
  Lock, 
  Eye,
  EyeOff,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Camera,
  Upload,
  Trash2,
  Edit3
} from 'lucide-react';
import { useCBT } from '../../contexts/CBTContext';
import { CBTUser, SecuritySettings } from '../../types/cbt.types';

interface SettingsSectionProps {
  title: string;
  description: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  children: React.ReactNode;
}

const SettingsSection: React.FC<SettingsSectionProps> = ({ title, description, icon: Icon, children }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-black/60 backdrop-blur-xl rounded-xl p-6 border border-gray-600/50"
    >
      <div className="flex items-center space-x-3 mb-4">
        <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center">
          <Icon className="text-white" size={20} />
        </div>
        <div>
          <h3 className="text-lg font-bold text-white font-mono">{title}</h3>
          <p className="text-gray-400 text-sm font-mono">{description}</p>
        </div>
      </div>
      {children}
    </motion.div>
  );
};

interface ToggleSwitchProps {
  enabled: boolean;
  onChange: (enabled: boolean) => void;
  label: string;
  description?: string;
}

const ToggleSwitch: React.FC<ToggleSwitchProps> = ({ enabled, onChange, label, description }) => {
  return (
    <div className="flex items-center justify-between py-3">
      <div className="flex-1">
        <label className="text-white font-mono font-medium">{label}</label>
        {description && (
          <p className="text-gray-400 text-sm font-mono mt-1">{description}</p>
        )}
      </div>
      <motion.button
        onClick={() => onChange(!enabled)}
        className={`relative w-12 h-6 rounded-full transition-colors duration-200 ${
          enabled ? 'bg-green-600' : 'bg-gray-700'
        }`}
        whileTap={{ scale: 0.95 }}
      >
        <motion.div
          className="absolute top-1 w-4 h-4 bg-white rounded-full shadow-lg"
          animate={{ x: enabled ? 26 : 2 }}
          transition={{ type: 'spring', stiffness: 500, damping: 30 }}
        />
      </motion.button>
    </div>
  );
};

export const CBTSettings: React.FC = () => {
  const { state } = useCBT();
  const [activeTab, setActiveTab] = useState<'profile' | 'security' | 'notifications' | 'system'>('profile');
  const [loading, setLoading] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  
  // Profile settings
  const [profileData, setProfileData] = useState({
    name: state.user?.name || '',
    email: state.user?.email || '',
    institution: state.user?.institution || '',
    department: state.user?.department || '',
    bio: '',
    avatar: state.user?.avatar || ''
  });

  // Security settings
  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    disableRightClick: true,
    disableCopy: true,
    disablePaste: true,
    disableDevTools: true,
    fullScreenRequired: true,
    monitorTabSwitching: true,
    allowedApplications: [],
    blockedWebsites: []
  });

  // Notification settings
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    testReminders: true,
    resultNotifications: true,
    systemAlerts: true,
    soundEnabled: true,
    desktopNotifications: true
  });

  // System settings
  const [systemSettings, setSystemSettings] = useState({
    autoSave: true,
    autoSubmit: false,
    showTimer: true,
    allowReview: true,
    shuffleQuestions: false,
    shuffleOptions: false,
    theme: 'dark',
    fontSize: 'medium'
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [showPassword, setShowPassword] = useState({
    current: false,
    new: false,
    confirm: false
  });

  const handleSaveSettings = async () => {
    setLoading(true);
    setSaveStatus('saving');
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSaveStatus('saved');
      setTimeout(() => setSaveStatus('idle'), 2000);
    } catch (error) {
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 2000);
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      alert('Passwords do not match');
      return;
    }
    
    setLoading(true);
    try {
      // Simulate password change API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      
      alert('Password changed successfully');
    } catch (error) {
      alert('Failed to change password');
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'system', label: 'System', icon: Settings }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white font-mono">Settings</h2>
          <p className="text-gray-400 font-mono">Manage your account and system preferences</p>
        </div>
        
        <div className="flex space-x-3">
          <motion.button
            onClick={handleSaveSettings}
            disabled={loading}
            className="px-6 py-2 bg-black hover:bg-gray-900 text-white rounded-lg transition-colors font-mono border border-gray-600 disabled:opacity-50"
            whileHover={{ scale: loading ? 1 : 1.05 }}
            whileTap={{ scale: loading ? 1 : 0.95 }}
          >
            {loading ? (
              <RefreshCw size={16} className="inline mr-2 animate-spin" />
            ) : (
              <Save size={16} className="inline mr-2" />
            )}
            {saveStatus === 'saving' ? 'Saving...' : 
             saveStatus === 'saved' ? 'Saved!' : 
             saveStatus === 'error' ? 'Error' : 'Save Changes'}
          </motion.button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-700">
        <nav className="flex space-x-8">
          {tabs.map(tab => {
            const IconComponent = tab.icon;
            return (
              <motion.button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm font-mono transition-colors ${
                  activeTab === tab.id
                    ? 'border-white text-white'
                    : 'border-transparent text-gray-400 hover:text-gray-300'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <IconComponent size={16} />
                <span>{tab.label}</span>
              </motion.button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'profile' && (
          <>
            <SettingsSection
              title="Profile Information"
              description="Update your personal information and profile details"
              icon={User}
            >
              <div className="space-y-4">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="w-20 h-20 bg-gradient-to-br from-gray-600 to-gray-800 rounded-full flex items-center justify-center">
                    {profileData.avatar ? (
                      <img src={profileData.avatar} alt="Avatar" className="w-full h-full rounded-full object-cover" />
                    ) : (
                      <User className="text-white" size={32} />
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <motion.button
                      className="px-3 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors font-mono text-sm"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Upload size={14} className="inline mr-2" />
                      Upload
                    </motion.button>
                    <motion.button
                      className="px-3 py-2 bg-red-900/30 hover:bg-red-800/30 text-red-400 border border-red-600/50 rounded-lg transition-colors font-mono text-sm"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Trash2 size={14} />
                    </motion.button>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Full Name</label>
                    <input
                      type="text"
                      value={profileData.name}
                      onChange={(e) => setProfileData(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Email</label>
                    <input
                      type="email"
                      value={profileData.email}
                      onChange={(e) => setProfileData(prev => ({ ...prev, email: e.target.value }))}
                      className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Institution</label>
                    <input
                      type="text"
                      value={profileData.institution}
                      onChange={(e) => setProfileData(prev => ({ ...prev, institution: e.target.value }))}
                      className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Department</label>
                    <input
                      type="text"
                      value={profileData.department}
                      onChange={(e) => setProfileData(prev => ({ ...prev, department: e.target.value }))}
                      className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
                    />
                  </div>
                </div>
              </div>
            </SettingsSection>

            <SettingsSection
              title="Change Password"
              description="Update your account password for security"
              icon={Lock}
            >
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Current Password</label>
                  <div className="relative">
                    <input
                      type={showPassword.current ? 'text' : 'password'}
                      value={passwordData.currentPassword}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                      className="w-full px-3 py-2 pr-10 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(prev => ({ ...prev, current: !prev.current }))}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                    >
                      {showPassword.current ? <EyeOff size={16} /> : <Eye size={16} />}
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">New Password</label>
                    <div className="relative">
                      <input
                        type={showPassword.new ? 'text' : 'password'}
                        value={passwordData.newPassword}
                        onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                        className="w-full px-3 py-2 pr-10 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(prev => ({ ...prev, new: !prev.new }))}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                      >
                        {showPassword.new ? <EyeOff size={16} /> : <Eye size={16} />}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Confirm Password</label>
                    <div className="relative">
                      <input
                        type={showPassword.confirm ? 'text' : 'password'}
                        value={passwordData.confirmPassword}
                        onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                        className="w-full px-3 py-2 pr-10 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(prev => ({ ...prev, confirm: !prev.confirm }))}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                      >
                        {showPassword.confirm ? <EyeOff size={16} /> : <Eye size={16} />}
                      </button>
                    </div>
                  </div>
                </div>

                <motion.button
                  onClick={handlePasswordChange}
                  disabled={loading || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}
                  className="px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors font-mono disabled:opacity-50"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Change Password
                </motion.button>
              </div>
            </SettingsSection>
          </>
        )}

        {activeTab === 'security' && (
          <SettingsSection
            title="Security Settings"
            description="Configure security measures for test taking"
            icon={Shield}
          >
            <div className="space-y-2">
              <ToggleSwitch
                enabled={securitySettings.disableRightClick}
                onChange={(enabled) => setSecuritySettings(prev => ({ ...prev, disableRightClick: enabled }))}
                label="Disable Right Click"
                description="Prevent right-click context menu during tests"
              />
              
              <ToggleSwitch
                enabled={securitySettings.disableCopy}
                onChange={(enabled) => setSecuritySettings(prev => ({ ...prev, disableCopy: enabled }))}
                label="Disable Copy"
                description="Prevent copying text during tests"
              />
              
              <ToggleSwitch
                enabled={securitySettings.disablePaste}
                onChange={(enabled) => setSecuritySettings(prev => ({ ...prev, disablePaste: enabled }))}
                label="Disable Paste"
                description="Prevent pasting text during tests"
              />
              
              <ToggleSwitch
                enabled={securitySettings.disableDevTools}
                onChange={(enabled) => setSecuritySettings(prev => ({ ...prev, disableDevTools: enabled }))}
                label="Disable Developer Tools"
                description="Block access to browser developer tools"
              />
              
              <ToggleSwitch
                enabled={securitySettings.fullScreenRequired}
                onChange={(enabled) => setSecuritySettings(prev => ({ ...prev, fullScreenRequired: enabled }))}
                label="Require Full Screen"
                description="Force full screen mode during tests"
              />
              
              <ToggleSwitch
                enabled={securitySettings.monitorTabSwitching}
                onChange={(enabled) => setSecuritySettings(prev => ({ ...prev, monitorTabSwitching: enabled }))}
                label="Monitor Tab Switching"
                description="Track when users switch browser tabs"
              />
            </div>
          </SettingsSection>
        )}

        {activeTab === 'notifications' && (
          <SettingsSection
            title="Notification Preferences"
            description="Control how and when you receive notifications"
            icon={Bell}
          >
            <div className="space-y-2">
              <ToggleSwitch
                enabled={notificationSettings.emailNotifications}
                onChange={(enabled) => setNotificationSettings(prev => ({ ...prev, emailNotifications: enabled }))}
                label="Email Notifications"
                description="Receive notifications via email"
              />
              
              <ToggleSwitch
                enabled={notificationSettings.testReminders}
                onChange={(enabled) => setNotificationSettings(prev => ({ ...prev, testReminders: enabled }))}
                label="Test Reminders"
                description="Get reminded about upcoming tests"
              />
              
              <ToggleSwitch
                enabled={notificationSettings.resultNotifications}
                onChange={(enabled) => setNotificationSettings(prev => ({ ...prev, resultNotifications: enabled }))}
                label="Result Notifications"
                description="Receive notifications when test results are available"
              />
              
              <ToggleSwitch
                enabled={notificationSettings.systemAlerts}
                onChange={(enabled) => setNotificationSettings(prev => ({ ...prev, systemAlerts: enabled }))}
                label="System Alerts"
                description="Get notified about system updates and maintenance"
              />
              
              <ToggleSwitch
                enabled={notificationSettings.soundEnabled}
                onChange={(enabled) => setNotificationSettings(prev => ({ ...prev, soundEnabled: enabled }))}
                label="Sound Notifications"
                description="Play sounds for notifications"
              />
              
              <ToggleSwitch
                enabled={notificationSettings.desktopNotifications}
                onChange={(enabled) => setNotificationSettings(prev => ({ ...prev, desktopNotifications: enabled }))}
                label="Desktop Notifications"
                description="Show desktop notifications"
              />
            </div>
          </SettingsSection>
        )}

        {activeTab === 'system' && (
          <SettingsSection
            title="System Preferences"
            description="Configure system behavior and appearance"
            icon={Monitor}
          >
            <div className="space-y-4">
              <div className="space-y-2">
                <ToggleSwitch
                  enabled={systemSettings.autoSave}
                  onChange={(enabled) => setSystemSettings(prev => ({ ...prev, autoSave: enabled }))}
                  label="Auto Save"
                  description="Automatically save answers while taking tests"
                />
                
                <ToggleSwitch
                  enabled={systemSettings.autoSubmit}
                  onChange={(enabled) => setSystemSettings(prev => ({ ...prev, autoSubmit: enabled }))}
                  label="Auto Submit"
                  description="Automatically submit test when time expires"
                />
                
                <ToggleSwitch
                  enabled={systemSettings.showTimer}
                  onChange={(enabled) => setSystemSettings(prev => ({ ...prev, showTimer: enabled }))}
                  label="Show Timer"
                  description="Display countdown timer during tests"
                />
                
                <ToggleSwitch
                  enabled={systemSettings.allowReview}
                  onChange={(enabled) => setSystemSettings(prev => ({ ...prev, allowReview: enabled }))}
                  label="Allow Review"
                  description="Allow reviewing answers before submission"
                />
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 pt-4 border-t border-gray-700">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Theme</label>
                  <select
                    value={systemSettings.theme}
                    onChange={(e) => setSystemSettings(prev => ({ ...prev, theme: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
                  >
                    <option value="dark">Dark</option>
                    <option value="light">Light</option>
                    <option value="auto">Auto</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Font Size</label>
                  <select
                    value={systemSettings.fontSize}
                    onChange={(e) => setSystemSettings(prev => ({ ...prev, fontSize: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
                  >
                    <option value="small">Small</option>
                    <option value="medium">Medium</option>
                    <option value="large">Large</option>
                  </select>
                </div>
              </div>
            </div>
          </SettingsSection>
        )}
      </div>
    </div>
  );
};
