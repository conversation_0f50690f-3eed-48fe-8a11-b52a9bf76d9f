import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

import { CBTProvider } from '../../contexts/CBTContext';
import { CBTDashboard } from '../../components/cbt/CBTDashboard';
import {
  createMockUser,
  createMockTest,
  createMockQuestion,
  createMockTestAttempt,
  createMockModule,
  mockElectronAPI,
  mockLocalStorage,
  simulateSecurityEvent,
  measurePerformance
} from '../utils/testUtils';

// Mock the Electron API
const mockElectron = mockElectronAPI();
(global as any).window.electronAPI = mockElectron;

// Mock localStorage
const mockStorage = mockLocalStorage();
Object.defineProperty(window, 'localStorage', { value: mockStorage });

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    form: ({ children, ...props }: any) => <form {...props}>{children}</form>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock Three.js components
jest.mock('../../components/shared/EnhancedThreeBackground', () => ({
  EnhancedThreeBackground: () => <div data-testid="three-background" />
}));

describe('Full System Integration Tests', () => {
  const mockStudent = createMockUser({
    id: 'student1',
    role: 'student',
    name: 'John Doe',
    email: '<EMAIL>'
  });

  const mockInstructor = createMockUser({
    id: 'instructor1',
    role: 'instructor',
    name: 'Jane Smith',
    email: '<EMAIL>'
  });

  const mockAdmin = createMockUser({
    id: 'admin1',
    role: 'admin',
    name: 'Admin User',
    email: '<EMAIL>'
  });

  const mockQuestions = [
    createMockQuestion({
      id: 'q1',
      type: 'multiple_choice',
      title: 'Programming Question',
      content: 'What is the output of console.log(2 + 2)?',
      points: 10,
      options: [
        { id: 'a', text: '3', isCorrect: false },
        { id: 'b', text: '4', isCorrect: true },
        { id: 'c', text: '22', isCorrect: false },
        { id: 'd', text: 'undefined', isCorrect: false }
      ]
    }),
    createMockQuestion({
      id: 'q2',
      type: 'true_false',
      title: 'JavaScript Concept',
      content: 'JavaScript is a compiled language.',
      points: 5,
      correctAnswer: 'false'
    }),
    createMockQuestion({
      id: 'q3',
      type: 'essay',
      title: 'Algorithm Explanation',
      content: 'Explain the difference between bubble sort and quick sort algorithms.',
      points: 15
    })
  ];

  const mockTest = createMockTest({
    id: 'test1',
    title: 'JavaScript Fundamentals Test',
    description: 'Test your knowledge of JavaScript basics',
    duration: 60,
    totalPoints: 30,
    passingScore: 70,
    questions: mockQuestions.map(q => q.id)
  });

  const mockModule = createMockModule({
    id: 'mod1',
    title: 'Web Development Fundamentals',
    code: 'WEB101',
    instructor: mockInstructor.id,
    tests: [mockTest.id],
    students: [mockStudent.id]
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mockStorage.clear();
    
    // Setup default mock responses
    mockElectron.auth.getCurrentUser.mockResolvedValue(null);
    mockElectron.security.enterTestMode.mockResolvedValue(true);
    mockElectron.security.exitTestMode.mockResolvedValue(true);
  });

  describe('Complete Student Test-Taking Workflow', () => {
    test('should complete full test-taking workflow successfully', async () => {
      // Mock successful authentication
      mockElectron.auth.login.mockResolvedValue({
        success: true,
        user: mockStudent,
        token: 'test-token'
      });

      // Mock test data retrieval
      mockElectron.auth.getCurrentUser.mockResolvedValue(mockStudent);

      const { container } = render(
        <CBTProvider>
          <CBTDashboard />
        </CBTProvider>
      );

      // Step 1: Login
      expect(screen.getByText(/secure login/i)).toBeInTheDocument();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await userEvent.type(emailInput, mockStudent.email);
      await userEvent.type(passwordInput, 'password123');
      await userEvent.click(loginButton);

      // Wait for authentication
      await waitFor(() => {
        expect(mockElectron.auth.login).toHaveBeenCalled();
      });

      // Step 2: Navigate to module selection
      await waitFor(() => {
        expect(screen.getByText(/dashboard/i)).toBeInTheDocument();
      });

      // Step 3: Select module and test
      const moduleButton = screen.getByText(mockModule.title);
      await userEvent.click(moduleButton);

      await waitFor(() => {
        expect(screen.getByText(mockTest.title)).toBeInTheDocument();
      });

      // Step 4: Start test
      const startTestButton = screen.getByRole('button', { name: /start test/i });
      await userEvent.click(startTestButton);

      // Should enter secure test mode
      await waitFor(() => {
        expect(mockElectron.security.enterTestMode).toHaveBeenCalled();
      });

      // Step 5: Answer questions
      await waitFor(() => {
        expect(screen.getByText(/question 1 of/i)).toBeInTheDocument();
      });

      // Answer multiple choice question
      const optionB = screen.getByLabelText(/option b/i);
      await userEvent.click(optionB);

      // Navigate to next question
      const nextButton = screen.getByRole('button', { name: /next/i });
      await userEvent.click(nextButton);

      // Answer true/false question
      await waitFor(() => {
        expect(screen.getByText(/question 2 of/i)).toBeInTheDocument();
      });

      const falseOption = screen.getByLabelText(/false/i);
      await userEvent.click(falseOption);

      // Navigate to essay question
      await userEvent.click(nextButton);

      await waitFor(() => {
        expect(screen.getByText(/question 3 of/i)).toBeInTheDocument();
      });

      const essayTextarea = screen.getByRole('textbox');
      await userEvent.type(essayTextarea, 'Bubble sort has O(n²) complexity while quick sort has O(n log n) average complexity.');

      // Step 6: Submit test
      const submitButton = screen.getByRole('button', { name: /submit test/i });
      await userEvent.click(submitButton);

      // Confirm submission
      const confirmSubmitButton = screen.getByRole('button', { name: /submit now/i });
      await userEvent.click(confirmSubmitButton);

      // Should exit secure test mode
      await waitFor(() => {
        expect(mockElectron.security.exitTestMode).toHaveBeenCalled();
      });

      // Step 7: View results
      await waitFor(() => {
        expect(screen.getByText(/test completed/i)).toBeInTheDocument();
      });
    }, 30000);

    test('should handle test interruption and recovery', async () => {
      mockElectron.auth.login.mockResolvedValue({
        success: true,
        user: mockStudent,
        token: 'test-token'
      });

      render(
        <CBTProvider>
          <CBTDashboard />
        </CBTProvider>
      );

      // Login and start test
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await userEvent.type(emailInput, mockStudent.email);
      await userEvent.type(passwordInput, 'password123');
      await userEvent.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText(/dashboard/i)).toBeInTheDocument();
      });

      // Start test
      const startTestButton = screen.getByRole('button', { name: /start test/i });
      await userEvent.click(startTestButton);

      // Simulate security violation (tab switching)
      simulateSecurityEvent('tab_switch');

      // Should log security event
      await waitFor(() => {
        expect(mockElectron.security.logEvent).toHaveBeenCalledWith(
          expect.objectContaining({
            type: 'security_violation',
            action: 'tab_switch_detected'
          })
        );
      });

      // Should show warning
      expect(screen.getByText(/security warning/i)).toBeInTheDocument();
    });
  });

  describe('Instructor Test Creation Workflow', () => {
    test('should complete test creation workflow', async () => {
      mockElectron.auth.login.mockResolvedValue({
        success: true,
        user: mockInstructor,
        token: 'instructor-token'
      });

      render(
        <CBTProvider>
          <CBTDashboard />
        </CBTProvider>
      );

      // Login as instructor
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await userEvent.type(emailInput, mockInstructor.email);
      await userEvent.type(passwordInput, 'password123');
      await userEvent.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText(/dashboard/i)).toBeInTheDocument();
      });

      // Navigate to test creation
      const createTestButton = screen.getByRole('button', { name: /create test/i });
      await userEvent.click(createTestButton);

      await waitFor(() => {
        expect(screen.getByText(/test builder/i)).toBeInTheDocument();
      });

      // Add questions using drag-drop builder
      const addQuestionButton = screen.getByRole('button', { name: /add question/i });
      await userEvent.click(addQuestionButton);

      // Select multiple choice question type
      const multipleChoiceButton = screen.getByText(/multiple choice/i);
      await userEvent.click(multipleChoiceButton);

      // Fill in question details
      const titleInput = screen.getByLabelText(/question title/i);
      const contentInput = screen.getByLabelText(/question content/i);

      await userEvent.type(titleInput, 'Test Question');
      await userEvent.type(contentInput, 'What is the correct answer?');

      // Add options
      const option1Input = screen.getByLabelText(/option 1/i);
      const option2Input = screen.getByLabelText(/option 2/i);

      await userEvent.type(option1Input, 'Correct Answer');
      await userEvent.type(option2Input, 'Wrong Answer');

      // Mark correct answer
      const correctCheckbox = screen.getByLabelText(/correct answer 1/i);
      await userEvent.click(correctCheckbox);

      // Save question
      const saveQuestionButton = screen.getByRole('button', { name: /save question/i });
      await userEvent.click(saveQuestionButton);

      // Configure test settings
      const testTitleInput = screen.getByLabelText(/test title/i);
      const durationInput = screen.getByLabelText(/duration/i);

      await userEvent.type(testTitleInput, 'New Test');
      await userEvent.clear(durationInput);
      await userEvent.type(durationInput, '60');

      // Publish test
      const publishButton = screen.getByRole('button', { name: /publish test/i });
      await userEvent.click(publishButton);

      await waitFor(() => {
        expect(screen.getByText(/test published successfully/i)).toBeInTheDocument();
      });
    });
  });

  describe('Admin Analytics and Reporting Workflow', () => {
    test('should display comprehensive analytics', async () => {
      mockElectron.auth.login.mockResolvedValue({
        success: true,
        user: mockAdmin,
        token: 'admin-token'
      });

      render(
        <CBTProvider>
          <CBTDashboard />
        </CBTProvider>
      );

      // Login as admin
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await userEvent.type(emailInput, mockAdmin.email);
      await userEvent.type(passwordInput, 'password123');
      await userEvent.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText(/dashboard/i)).toBeInTheDocument();
      });

      // Navigate to analytics
      const analyticsButton = screen.getByRole('button', { name: /analytics/i });
      await userEvent.click(analyticsButton);

      await waitFor(() => {
        expect(screen.getByText(/comprehensive reports/i)).toBeInTheDocument();
      });

      // Check key metrics are displayed
      expect(screen.getByText(/total attempts/i)).toBeInTheDocument();
      expect(screen.getByText(/avg score/i)).toBeInTheDocument();
      expect(screen.getByText(/pass rate/i)).toBeInTheDocument();

      // Test export functionality
      const exportButton = screen.getByRole('button', { name: /export/i });
      await userEvent.click(exportButton);

      const pdfExportButton = screen.getByRole('button', { name: /pdf report/i });
      await userEvent.click(pdfExportButton);

      // Should trigger export
      expect(screen.getByText(/export started/i)).toBeInTheDocument();
    });
  });

  describe('System Performance Under Load', () => {
    test('should handle large datasets efficiently', async () => {
      // Create large dataset
      const largeQuestionSet = Array.from({ length: 100 }, (_, i) =>
        createMockQuestion({ id: `q${i}`, title: `Question ${i}` })
      );

      const largeTest = createMockTest({
        questions: largeQuestionSet.map(q => q.id)
      });

      mockElectron.auth.login.mockResolvedValue({
        success: true,
        user: mockStudent,
        token: 'test-token'
      });

      const renderTime = await measurePerformance(async () => {
        render(
          <CBTProvider>
            <CBTDashboard />
          </CBTProvider>
        );
      });

      // Should render within performance threshold
      expect(renderTime).toBeLessThan(2000); // 2 seconds for large dataset

      // Login and verify system remains responsive
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      const interactionTime = await measurePerformance(async () => {
        await userEvent.type(emailInput, mockStudent.email);
        await userEvent.type(passwordInput, 'password123');
        await userEvent.click(loginButton);
      });

      // Interactions should remain fast even with large datasets
      expect(interactionTime).toBeLessThan(1000);
    });
  });

  describe('Security Integration', () => {
    test('should maintain security throughout entire workflow', async () => {
      const securityLogSpy = jest.spyOn(mockElectron.security, 'logEvent');

      mockElectron.auth.login.mockResolvedValue({
        success: true,
        user: mockStudent,
        token: 'test-token'
      });

      render(
        <CBTProvider>
          <CBTDashboard />
        </CBTProvider>
      );

      // Login
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await userEvent.type(emailInput, mockStudent.email);
      await userEvent.type(passwordInput, 'password123');
      await userEvent.click(loginButton);

      // Start test (should enter secure mode)
      await waitFor(() => {
        expect(screen.getByText(/dashboard/i)).toBeInTheDocument();
      });

      const startTestButton = screen.getByRole('button', { name: /start test/i });
      await userEvent.click(startTestButton);

      await waitFor(() => {
        expect(mockElectron.security.enterTestMode).toHaveBeenCalled();
      });

      // Test various security violations
      simulateSecurityEvent('right_click');
      simulateSecurityEvent('key_combination');
      simulateSecurityEvent('tab_switch');
      simulateSecurityEvent('focus_lost');

      // Should log all security events
      await waitFor(() => {
        expect(securityLogSpy).toHaveBeenCalledTimes(4);
      });

      // Complete test (should exit secure mode)
      const submitButton = screen.getByRole('button', { name: /submit test/i });
      await userEvent.click(submitButton);

      const confirmButton = screen.getByRole('button', { name: /submit now/i });
      await userEvent.click(confirmButton);

      await waitFor(() => {
        expect(mockElectron.security.exitTestMode).toHaveBeenCalled();
      });
    });
  });

  describe('Error Recovery and Resilience', () => {
    test('should recover from network failures gracefully', async () => {
      // Simulate network failure during login
      mockElectron.auth.login.mockRejectedValueOnce(new Error('Network timeout'));

      render(
        <CBTProvider>
          <CBTDashboard />
        </CBTProvider>
      );

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await userEvent.type(emailInput, '<EMAIL>');
      await userEvent.type(passwordInput, 'password');
      await userEvent.click(loginButton);

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/network timeout/i)).toBeInTheDocument();
      });

      // Should allow retry
      const retryButton = screen.getByRole('button', { name: /retry/i });
      expect(retryButton).toBeInTheDocument();

      // Mock successful retry
      mockElectron.auth.login.mockResolvedValueOnce({
        success: true,
        user: mockStudent,
        token: 'test-token'
      });

      await userEvent.click(retryButton);

      // Should succeed on retry
      await waitFor(() => {
        expect(screen.getByText(/dashboard/i)).toBeInTheDocument();
      });
    });
  });
});
