import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { CBTAppState, CBTUser, Module, Test, TestAttempt, UserRole } from '../types/cbt.types';
import { cbtService } from '../services/cbt.service';

// Action types
type CBTAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_USER'; payload: CBTUser | null }
  | { type: 'SET_AUTHENTICATED'; payload: boolean }
  | { type: 'SET_CURRENT_MODULE'; payload: Module | null }
  | { type: 'SET_CURRENT_TEST'; payload: Test | null }
  | { type: 'SET_CURRENT_ATTEMPT'; payload: TestAttempt | null }
  | { type: 'RESET_STATE' };

// Initial state
const initialState: CBTAppState = {
  user: null,
  currentModule: null,
  currentTest: null,
  currentAttempt: null,
  isAuthenticated: false,
  isLoading: true,
  error: null
};

// Reducer
function cbtReducer(state: CBTAppState, action: CBTAction): CBTAppState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_USER':
      return { ...state, user: action.payload };
    case 'SET_AUTHENTICATED':
      return { ...state, isAuthenticated: action.payload };
    case 'SET_CURRENT_MODULE':
      return { ...state, currentModule: action.payload };
    case 'SET_CURRENT_TEST':
      return { ...state, currentTest: action.payload };
    case 'SET_CURRENT_ATTEMPT':
      return { ...state, currentAttempt: action.payload };
    case 'RESET_STATE':
      return { ...initialState, isLoading: false };
    default:
      return state;
  }
}

// Context type
interface CBTContextType {
  state: CBTAppState;
  login: (email: string, password: string, role: UserRole) => Promise<boolean>;
  logout: () => Promise<void>;
  setCurrentModule: (module: Module | null) => void;
  setCurrentTest: (test: Test | null) => void;
  setCurrentAttempt: (attempt: TestAttempt | null) => void;
  clearError: () => void;
}

// Create context
const CBTContext = createContext<CBTContextType | undefined>(undefined);

// Provider component
export const CBTProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(cbtReducer, initialState);

  // Check for existing authentication on mount
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        const { user, token } = cbtService.getStoredAuthData();
        
        if (user && token) {
          dispatch({ type: 'SET_USER', payload: user });
          dispatch({ type: 'SET_AUTHENTICATED', payload: true });
        }
      } catch (error) {
        console.error('Error checking auth status:', error);
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    checkAuthStatus();
  }, []);

  // Login function
  const login = async (email: string, password: string, role: UserRole): Promise<boolean> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const response = await cbtService.login(email, password, role);
      
      if (response.success && response.data) {
        dispatch({ type: 'SET_USER', payload: response.data.user });
        dispatch({ type: 'SET_AUTHENTICATED', payload: true });
        return true;
      } else {
        dispatch({ type: 'SET_ERROR', payload: response.message });
        return false;
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Login failed. Please try again.' });
      return false;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Logout function
  const logout = async (): Promise<void> => {
    try {
      await cbtService.logout();
      dispatch({ type: 'RESET_STATE' });
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout even if there's an error
      dispatch({ type: 'RESET_STATE' });
    }
  };

  // Set current module
  const setCurrentModule = (module: Module | null) => {
    dispatch({ type: 'SET_CURRENT_MODULE', payload: module });
  };

  // Set current test
  const setCurrentTest = (test: Test | null) => {
    dispatch({ type: 'SET_CURRENT_TEST', payload: test });
  };

  // Set current attempt
  const setCurrentAttempt = (attempt: TestAttempt | null) => {
    dispatch({ type: 'SET_CURRENT_ATTEMPT', payload: attempt });
  };

  // Clear error
  const clearError = () => {
    dispatch({ type: 'SET_ERROR', payload: null });
  };

  const contextValue: CBTContextType = {
    state,
    login,
    logout,
    setCurrentModule,
    setCurrentTest,
    setCurrentAttempt,
    clearError
  };

  return (
    <CBTContext.Provider value={contextValue}>
      {children}
    </CBTContext.Provider>
  );
};

// Custom hook to use CBT context
export const useCBT = (): CBTContextType => {
  const context = useContext(CBTContext);
  if (context === undefined) {
    throw new Error('useCBT must be used within a CBTProvider');
  }
  return context;
};

// Security monitoring hook
export const useSecurityMonitoring = () => {
  const { state } = useCBT();

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        if (state.currentAttempt) {
          cbtService.logSecurityEvent({
            type: 'tab_switch',
            timestamp: new Date(),
            details: 'User switched tabs or minimized window',
            severity: 'medium'
          });
        } else {
          console.warn('Security Alert: Tab switching detected');
        }
      }
    };

    const handleBlur = () => {
      if (state.currentAttempt) {
        cbtService.logSecurityEvent({
          type: 'window_blur',
          timestamp: new Date(),
          details: 'Window lost focus',
          severity: 'low'
        });
      }
    };

    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault();
      if (state.currentAttempt) {
        cbtService.logSecurityEvent({
          type: 'right_click',
          timestamp: new Date(),
          details: 'Right-click attempted',
          severity: 'low'
        });
      }
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      // Disable common shortcuts
      if (
        (e.ctrlKey && (e.key === 'c' || e.key === 'v' || e.key === 'a' || e.key === 's' || e.key === 'u')) ||
        e.key === 'F12' ||
        (e.ctrlKey && e.shiftKey && e.key === 'I')
      ) {
        e.preventDefault();
        if (state.currentAttempt) {
          cbtService.logSecurityEvent({
            type: 'key_combination',
            timestamp: new Date(),
            details: `Blocked key combination: ${e.key}`,
            severity: 'medium'
          });
        }
      }
    };

    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('blur', handleBlur);
    document.addEventListener('contextmenu', handleContextMenu);
    document.addEventListener('keydown', handleKeyDown);

    // Cleanup
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('blur', handleBlur);
      document.removeEventListener('contextmenu', handleContextMenu);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [state.currentAttempt]);
};

// HOC for role-based access control
export const withRoleAccess = <P extends object>(
  Component: React.ComponentType<P>,
  allowedRoles: UserRole[]
) => {
  const WrappedComponent = (props: P) => {
    const { state } = useCBT();

    if (!state.isAuthenticated || !state.user) {
      return (
        <div className="min-h-screen bg-black flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-white mb-4 font-mono">ACCESS DENIED</h2>
            <p className="text-gray-400 font-mono">Please log in to access this page.</p>
          </div>
        </div>
      );
    }

    if (!allowedRoles.includes(state.user.role)) {
      return (
        <div className="min-h-screen bg-black flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-white mb-4 font-mono">INSUFFICIENT PERMISSIONS</h2>
            <p className="text-gray-400 font-mono">You don&apos;t have permission to access this page.</p>
          </div>
        </div>
      );
    }

    return <Component {...props} />;
  };

  WrappedComponent.displayName = `withRoleAccess(${Component.displayName || Component.name})`;
  return WrappedComponent;
};


