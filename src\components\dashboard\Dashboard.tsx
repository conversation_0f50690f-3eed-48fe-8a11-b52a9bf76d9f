import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { DashboardContent } from './DashboardContent';
import { EnhancedThreeBackground } from '../shared/EnhancedThreeBackground';
import { PageTransition } from '../shared/PageTransition';
import { User } from '../../types/auth.types';

interface DashboardProps {
  user: User;
  onLogout: () => void;
}

export const Dashboard: React.FC<DashboardProps> = ({ user, onLogout }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [activeItem, setActiveItem] = useState('dashboard');

  const handleSidebarToggle = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const handleItemClick = (itemId: string) => {
    setActiveItem(itemId);
  };

  const handleSearch = (query: string) => {
    console.log('Search query:', query);
    // Implement search functionality
  };

  const handleNotificationClick = () => {
    console.log('Notifications clicked');
    // Implement notifications
  };

  const handleProfileClick = () => {
    console.log('Profile clicked');
    // Implement profile management
  };

  const renderContent = () => {
    switch (activeItem) {
      case 'dashboard':
        return <DashboardContent user={user} />;
      case 'users':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-white mb-4">Users Management</h2>
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
              <p className="text-gray-300">Users management interface would go here.</p>
            </div>
          </div>
        );
      case 'analytics':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-white mb-4">Analytics</h2>
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
              <p className="text-gray-300">Analytics dashboard would go here.</p>
            </div>
          </div>
        );
      case 'documents':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-white mb-4">Documents</h2>
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
              <p className="text-gray-300">Document management interface would go here.</p>
            </div>
          </div>
        );
      case 'notifications':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-white mb-4">Notifications</h2>
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
              <p className="text-gray-300">Notifications center would go here.</p>
            </div>
          </div>
        );
      case 'settings':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-white mb-4">Settings</h2>
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
              <p className="text-gray-300">Application settings would go here.</p>
            </div>
          </div>
        );
      default:
        return <DashboardContent user={user} />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800 relative overflow-hidden">
      <EnhancedThreeBackground
        particleCount={800}
        shapeCount={15}
        lineCount={20}
        animated
        theme="dashboard"
      />

      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900/60 via-gray-900/50 to-slate-800/60" />
      
      {/* Main Layout */}
      <div className="relative z-10 flex h-screen">
        {/* Sidebar */}
        <Sidebar
          isCollapsed={sidebarCollapsed}
          onToggle={handleSidebarToggle}
          activeItem={activeItem}
          onItemClick={handleItemClick}
          onLogout={onLogout}
        />

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <Header
            user={user}
            onSearch={handleSearch}
            onNotificationClick={handleNotificationClick}
            onProfileClick={handleProfileClick}
          />

          {/* Content Area */}
          <main className="flex-1 overflow-auto">
            <PageTransition
              key={activeItem}
              variant="slide"
              duration={0.4}
              className="h-full"
            >
              {renderContent()}
            </PageTransition>
          </main>
        </div>
      </div>
    </div>
  );
};
