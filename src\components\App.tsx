import React, { useState, useEffect } from 'react';
import { AnimatePresence } from 'framer-motion';
import { ThemeProvider } from '../contexts/ThemeContext';
import { AnimatedWelcomeScreen } from './welcome/AnimatedWelcomeScreen';
import { LoginScreen } from './auth/LoginScreen';
import { StudentDashboard } from './dashboards/StudentDashboard';
import { InstructorDashboard } from './dashboards/InstructorDashboard';
import { AdminDashboard } from './dashboards/AdminDashboard';
import { LoadingScreen } from './shared/LoadingScreen';
import { cbtApiService } from '../services/cbt-api.service';
import '../styles/theme.css';

type AppState = 'welcome' | 'login' | 'dashboard';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'student' | 'instructor' | 'admin';
  avatar?: string;
}

interface LoginCredentials {
  identifier: string;
  password: string;
  rememberMe: boolean;
}

interface LoginResult {
  success: boolean;
  user?: User;
  message?: string;
}

// Main App Content Component
const AppContent: React.FC = () => {
  const [appState, setAppState] = useState<AppState>('welcome');
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Initialize app and check for existing authentication
  useEffect(() => {
    const initializeApp = async () => {
      try {
        console.log('🚀 Starting CBT System Application...');

        // Check if user is already authenticated
        const storedUser = cbtApiService.getStoredUser();
        if (storedUser && cbtApiService.isAuthenticated()) {
          // Verify token is still valid
          const verifyResult = await cbtApiService.verifyToken();
          if (verifyResult.success) {
            setUser(storedUser);
            setAppState('dashboard');
            console.log('✅ User automatically logged in');
          } else {
            // Token invalid, clear storage
            localStorage.removeItem('cbt_token');
            localStorage.removeItem('cbt_user');
          }
        }

        console.log('✅ CBT System Application initialized successfully');
        console.log('🎯 Ready for user authentication and testing');
      } catch (error) {
        console.error('❌ Failed to initialize CBT application:', error);
      }
    };

    initializeApp();
  }, []);

  const handleWelcomeComplete = () => {
    setAppState('login');
  };

  const handleWelcomeSkip = () => {
    setAppState('login');
  };

  const handleLogin = async (credentials: LoginCredentials): Promise<LoginResult> => {
    setIsLoading(true);

    try {
      const result = await cbtApiService.login(credentials.identifier, credentials.password);

      if (result.success && result.data) {
        const userData: User = {
          id: result.data.user.id,
          name: result.data.user.name,
          email: result.data.user.email,
          role: result.data.user.role,
          avatar: result.data.user.avatar,
        };

        setUser(userData);
        setAppState('dashboard');

        return { success: true, user: userData };
      } else {
        return {
          success: false,
          message: result.message || 'Invalid credentials. Please check your email and password.'
        };
      }
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        message: 'Login failed. Please check your connection and try again.'
      };
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await cbtApiService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      setAppState('welcome');
    }
  };

  const handleBackToWelcome = () => {
    // If user is logged in, don't go back to welcome, go to login instead
    if (user) {
      handleLogout();
    } else {
      setAppState('welcome');
    }
  };

  const renderDashboard = () => {
    if (!user) return null;

    switch (user.role) {
      case 'student':
        return (
          <StudentDashboard
            user={user}
            onBack={handleBackToWelcome}
            onLogout={handleLogout}
          />
        );
      case 'instructor':
        return (
          <InstructorDashboard
            user={user}
            onBack={handleBackToWelcome}
            onLogout={handleLogout}
          />
        );
      case 'admin':
        return (
          <AdminDashboard
            user={user}
            onBack={handleBackToWelcome}
            onLogout={handleLogout}
          />
        );
      default:
        return null;
    }
  };

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <div className="app">
      <AnimatePresence mode="wait">
        {appState === 'welcome' && (
          <AnimatedWelcomeScreen
            key="welcome"
            onComplete={handleWelcomeComplete}
            onSkip={handleWelcomeSkip}
          />
        )}

        {appState === 'login' && (
          <LoginScreen
            key="login"
            onLogin={handleLogin}
            onBack={handleBackToWelcome}
          />
        )}

        {appState === 'dashboard' && user && (
          <div key={`dashboard-${user.role}`}>
            {renderDashboard()}
          </div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Main App Component with Providers
export const App: React.FC = () => {
  return (
    <ThemeProvider>
      <AppContent />
    </ThemeProvider>
  );
};
