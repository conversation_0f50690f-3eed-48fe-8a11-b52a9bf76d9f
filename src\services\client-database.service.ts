// Client-side database service for browser compatibility
import { CBTUser, Test, TestAttempt, Question } from '../types/cbt.types';
import { Module } from '../types/module.types';

// Mock database for client-side operations
class ClientDatabaseService {
  private isElectron = typeof window !== 'undefined' && (window as any).electronAPI;
  private initialized = false;

  // Initialize the database service
  async initialize(): Promise<void> {
    if (this.initialized) {
      console.log('📊 CBT Database already initialized');
      return;
    }

    try {
      console.log('🚀 Initializing CBT Database Service...');

      if (typeof window !== 'undefined') {
        // Browser environment
        console.log('✅ CBT Database initialized with localStorage');
        console.log('📊 Database Type: Client-side localStorage');
        console.log('🔧 Environment: Browser');

        // Check if Electron API is available
        if (this.isElectron) {
          console.log('🖥️  Electron API detected - Enhanced features available');
          try {
            const health = await (window as any).electronAPI.database.checkHealth();
            console.log('🏥 Electron Database Health:', health);
          } catch (error) {
            console.warn('⚠️  Electron database health check failed, using fallback:', error);
          }
        } else {
          console.log('🌐 Web browser mode - Using localStorage fallback');
        }

        // Initialize default data if needed
        await this.initializeDefaultData();
        console.log('✅ Default data initialized successfully');

      } else {
        // Node.js environment
        console.log('✅ CBT Database initialized with in-memory storage');
        console.log('📊 Database Type: In-memory');
        console.log('🔧 Environment: Node.js');
      }

      // Log connection status
      const health = await this.checkHealth();
      console.log('🏥 Database Health Check:', health);

      this.initialized = true;
      console.log('🎉 CBT Database Service initialization complete!');

    } catch (error) {
      console.error('❌ Failed to initialize CBT database:', error);
      throw error;
    }
  }

  // Check database health
  async checkHealth(): Promise<{ status: string; timestamp: string; details: any }> {
    try {
      const users = this.getStoredUsers();
      const modules = this.getStoredModules();

      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        details: {
          userCount: users.length,
          moduleCount: modules.length,
          storageType: this.isElectron ? 'electron+localStorage' : 'localStorage',
          electronAPI: !!this.isElectron
        }
      };
    } catch (error) {
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }

  // Initialize default data
  private async initializeDefaultData(): Promise<void> {
    try {
      // Check if users already exist
      const existingUsers = this.getStoredUsers();
      if (existingUsers.length === 0) {
        console.log('📝 Creating default users...');
        const defaultUsers = this.getDefaultUsers();
        localStorage.setItem('cbt_users', JSON.stringify(defaultUsers));
        console.log(`✅ Created ${defaultUsers.length} default users`);
      } else {
        console.log(`📊 Found ${existingUsers.length} existing users`);
      }

      // Check if modules already exist
      const existingModules = this.getStoredModules();
      if (existingModules.length === 0) {
        console.log('📦 Creating default modules...');
        const defaultModules = this.getDefaultModules();
        localStorage.setItem('cbt_modules', JSON.stringify(defaultModules));
        console.log(`✅ Created ${defaultModules.length} default modules`);
      } else {
        console.log(`📦 Found ${existingModules.length} existing modules`);
      }
    } catch (error) {
      console.error('❌ Failed to initialize default data:', error);
      throw error;
    }
  }

  // User operations
  async createUser(userData: Omit<CBTUser, 'id' | 'createdAt' | 'updatedAt'> & { password: string }): Promise<CBTUser> {
    if (this.isElectron && (window as any).electronAPI?.database?.createUser) {
      try {
        return await (window as any).electronAPI.database.createUser(userData);
      } catch (error) {
        console.warn('Electron database operation failed, using local storage:', error);
      }
    }
    
    // Mock implementation for web
    const user: CBTUser = {
      ...userData,
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Store in localStorage for demo
    const users = this.getStoredUsers();
    users.push(user);
    localStorage.setItem('cbt_users', JSON.stringify(users));
    
    return user;
  }

  async getUserById(id: string): Promise<CBTUser | null> {
    if (this.isElectron) {
      return await (window as any).electronAPI.database.getUserById(id);
    }
    
    const users = this.getStoredUsers();
    return users.find(u => u.id === id) || null;
  }

  async getUserByEmail(email: string): Promise<CBTUser | null> {
    if (this.isElectron) {
      return await (window as any).electronAPI.database.getUserByEmail(email);
    }
    
    const users = this.getStoredUsers();
    return users.find(u => u.email === email) || null;
  }

  async updateUser(id: string, updates: Partial<CBTUser>): Promise<CBTUser | null> {
    if (this.isElectron) {
      return await (window as any).electronAPI.database.updateUser(id, updates);
    }
    
    const users = this.getStoredUsers();
    const userIndex = users.findIndex(u => u.id === id);
    
    if (userIndex === -1) return null;
    
    users[userIndex] = { ...users[userIndex], ...updates, updatedAt: new Date() };
    localStorage.setItem('cbt_users', JSON.stringify(users));
    
    return users[userIndex];
  }

  async updateLastLogin(id: string): Promise<void> {
    if (this.isElectron) {
      return await (window as any).electronAPI.database.updateLastLogin(id);
    }
    
    await this.updateUser(id, { lastLogin: new Date() });
  }

  async verifyPassword(email: string, password: string): Promise<CBTUser | null> {
    if (this.isElectron) {
      return await (window as any).electronAPI.database.verifyPassword(email, password);
    }
    
    // Mock password verification for demo
    const user = await this.getUserByEmail(email);
    if (user && password === 'demo123') { // Demo password
      return user;
    }
    return null;
  }

  // Module operations
  async createModule(moduleData: Omit<Module, 'id' | 'createdAt' | 'updatedAt'>): Promise<Module> {
    if (this.isElectron) {
      return await (window as any).electronAPI.database.createModule(moduleData);
    }
    
    const module: Module = {
      ...moduleData,
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const modules = this.getStoredModules();
    modules.push(module);
    localStorage.setItem('cbt_modules', JSON.stringify(modules));
    
    return module;
  }

  async getModuleById(id: string): Promise<Module | null> {
    if (this.isElectron) {
      return await (window as any).electronAPI.database.getModuleById(id);
    }
    
    const modules = this.getStoredModules();
    return modules.find(m => m.id === id) || null;
  }

  async getModulesByType(type: string): Promise<Module[]> {
    if (this.isElectron) {
      return await (window as any).electronAPI.database.getModulesByType(type);
    }
    
    const modules = this.getStoredModules();
    return modules.filter(m => m.type === type && m.isActive);
  }

  async getAllModules(): Promise<Module[]> {
    if (this.isElectron) {
      return await (window as any).electronAPI.database.getAllModules();
    }
    
    const modules = this.getStoredModules();
    return modules.filter(m => m.isActive);
  }

  async updateModule(id: string, updates: Partial<Module>): Promise<Module | null> {
    if (this.isElectron) {
      return await (window as any).electronAPI.database.updateModule(id, updates);
    }
    
    const modules = this.getStoredModules();
    const moduleIndex = modules.findIndex(m => m.id === id);
    
    if (moduleIndex === -1) return null;
    
    modules[moduleIndex] = { ...modules[moduleIndex], ...updates, updatedAt: new Date() };
    localStorage.setItem('cbt_modules', JSON.stringify(modules));
    
    return modules[moduleIndex];
  }

  async createTest(testData: Omit<Test, 'id' | 'createdAt' | 'updatedAt'>): Promise<Test> {
    if (this.isElectron) {
      return await (window as any).electronAPI.database.createTest(testData);
    }
    
    const test: Test = {
      ...testData,
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const tests = this.getStoredTests();
    tests.push(test);
    localStorage.setItem('cbt_tests', JSON.stringify(tests));
    
    return test;
  }

  // Sync operations (mock for client-side)
  async syncToCloud(data: any, collection: string): Promise<void> {
    if (this.isElectron) {
      return await (window as any).electronAPI.database.syncToCloud(data, collection);
    }
    
    // Mock cloud sync
    console.log(`Syncing ${collection} to cloud:`, data);
    
    // Store sync data locally
    const syncData = this.getStoredSyncData();
    syncData.push({
      collection,
      data,
      timestamp: new Date(),
      synced: false
    });
    localStorage.setItem('cbt_sync_queue', JSON.stringify(syncData));
  }

  async syncFromCloud(collection: string, lastSyncTime?: Date): Promise<any[]> {
    if (this.isElectron) {
      return await (window as any).electronAPI.database.syncFromCloud(collection, lastSyncTime);
    }
    
    // Mock cloud sync
    console.log(`Syncing ${collection} from cloud since:`, lastSyncTime);
    return [];
  }

  // Health check
  async checkDatabaseHealth(): Promise<{ mysql: boolean; mongodb: boolean }> {
    if (this.isElectron) {
      return await (window as any).electronAPI.database.checkHealth();
    }
    
    // Mock health check for browser
    return { mysql: true, mongodb: true };
  }

  // Helper methods
  private getStoredUsers(): CBTUser[] {
    try {
      const stored = localStorage.getItem('cbt_users');
      return stored ? JSON.parse(stored) : this.getDefaultUsers();
    } catch {
      return this.getDefaultUsers();
    }
  }

  private getStoredModules(): Module[] {
    try {
      const stored = localStorage.getItem('cbt_modules');
      return stored ? JSON.parse(stored) : this.getDefaultModules();
    } catch {
      return this.getDefaultModules();
    }
  }

  private getStoredTests(): Test[] {
    try {
      const stored = localStorage.getItem('cbt_tests');
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }

  private getStoredSyncData(): any[] {
    try {
      const stored = localStorage.getItem('cbt_sync_queue');
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }

  private getDefaultUsers(): CBTUser[] {
    return [
      {
        id: 'admin-1',
        email: '<EMAIL>',
        name: 'System Administrator',
        role: 'admin',
        permissions: ['*'],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'instructor-1',
        email: '<EMAIL>',
        name: 'John Instructor',
        role: 'instructor',
        permissions: ['test.create', 'test.manage', 'student.view'],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'student-1',
        email: '<EMAIL>',
        name: 'Jane Student',
        role: 'student',
        permissions: ['test.take', 'result.view'],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
  }

  private getDefaultModules(): Module[] {
    return [
      {
        id: 'cbt-core',
        name: 'CBT Exam Module',
        description: 'Computer-Based Testing with advanced security features',
        type: 'cbt',
        icon: 'BookOpen',
        version: '1.0.0',
        isActive: true,
        isInstalled: true,
        permissions: ['exam.create', 'exam.take', 'exam.grade'],
        settings: {},
        dependencies: [],
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 11);
  }
}

export const clientDatabaseService = new ClientDatabaseService();
