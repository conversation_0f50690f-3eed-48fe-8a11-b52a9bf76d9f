'use client';

import React, { useState, useEffect } from 'react';
import { Wifi, WifiOff, Server, Users, Activity, Database } from 'lucide-react';

interface NetworkStats {
  isOnline: boolean;
  connectedNodes: number;
  lastSync: string | null;
  queueLength: number;
  role: 'master' | 'slave';
  nodeId: string;
}

export const NetworkStatus: React.FC = () => {
  const [stats, setStats] = useState<NetworkStats>({
    isOnline: false,
    connectedNodes: 0,
    lastSync: null,
    queueLength: 0,
    role: 'slave',
    nodeId: 'unknown'
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchNetworkStatus();
    const interval = setInterval(fetchNetworkStatus, 5000);
    return () => clearInterval(interval);
  }, []);

  const fetchNetworkStatus = async () => {
    try {
      const response = await fetch('/api/network-sync/status');
      if (response.ok) {
        const data = await response.json();
        setStats(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch network status:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-4">
        <div className="animate-pulse flex space-x-4">
          <div className="rounded-full bg-gray-200 h-10 w-10"></div>
          <div className="flex-1 space-y-2 py-1">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold flex items-center">
          <Activity className="w-5 h-5 mr-2" />
          Network Status
        </h3>
        <div className="flex items-center">
          {stats.isOnline ? (
            <Wifi className="w-5 h-5 text-green-500" />
          ) : (
            <WifiOff className="w-5 h-5 text-red-500" />
          )}
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center">
          <div className={`text-2xl font-bold ${stats.isOnline ? 'text-green-600' : 'text-red-600'}`}>
            {stats.isOnline ? 'Online' : 'Offline'}
          </div>
          <div className="text-sm text-gray-600">Connection</div>
        </div>

        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600 flex items-center justify-center">
            <Users className="w-6 h-6 mr-1" />
            {stats.connectedNodes}
          </div>
          <div className="text-sm text-gray-600">Connected Nodes</div>
        </div>

        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600 flex items-center justify-center">
            <Server className="w-6 h-6 mr-1" />
            {stats.role === 'master' ? 'Master' : 'Slave'}
          </div>
          <div className="text-sm text-gray-600">Node Role</div>
        </div>

        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600 flex items-center justify-center">
            <Database className="w-6 h-6 mr-1" />
            {stats.queueLength}
          </div>
          <div className="text-sm text-gray-600">Sync Queue</div>
        </div>
      </div>

      <div className="mt-4 pt-4 border-t">
        <div className="flex justify-between items-center text-sm text-gray-600">
          <span>Node ID: {stats.nodeId}</span>
          <span>
            Last Sync: {stats.lastSync ? new Date(stats.lastSync).toLocaleTimeString() : 'Never'}
          </span>
        </div>
      </div>

      <div className="mt-4 flex space-x-2">
        <button
          onClick={fetchNetworkStatus}
          className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
        >
          Refresh
        </button>
        <button
          onClick={() => window.location.href = '/network-manager'}
          className="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700"
        >
          Manage Network
        </button>
      </div>
    </div>
  );
};
