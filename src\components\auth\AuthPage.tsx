import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { LoginForm } from './LoginForm';
import { RegisterForm } from './RegisterForm';
import { EnhancedThreeBackground } from '../shared/EnhancedThreeBackground';
import { SlideTransition, StaggeredAnimation } from '../shared/PageTransition';
import { authService } from '../../services/auth.service';
import { LoginCredentials, RegisterCredentials } from '../../types/auth.types';

interface AuthPageProps {
  onAuthSuccess: () => void;
}

export const AuthPage: React.FC<AuthPageProps> = ({ onAuthSuccess }) => {
  const [isLogin, setIsLogin] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleLogin = async (credentials: LoginCredentials) => {
    setLoading(true);
    setError(null);

    try {
      const response = await authService.login(credentials);
      
      if (response.success && response.user && response.token) {
        authService.saveAuthData(response.user, response.token);
        onAuthSuccess();
      } else {
        setError(response.message || 'Login failed');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleRegister = async (credentials: RegisterCredentials) => {
    setLoading(true);
    setError(null);

    try {
      const response = await authService.register(credentials);
      
      if (response.success && response.user && response.token) {
        authService.saveAuthData(response.user, response.token);
        onAuthSuccess();
      } else {
        setError(response.message || 'Registration failed');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const switchToRegister = () => {
    setIsLogin(false);
    setError(null);
  };

  const switchToLogin = () => {
    setIsLogin(true);
    setError(null);
  };

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-black via-gray-900 to-black">
      <EnhancedThreeBackground particleCount={1500} shapeCount={25} lineCount={40} animated theme="auth" />

      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-black/80 via-gray-900/60 to-black/80" />
      
      {/* Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Branding */}
          <SlideTransition direction="left" duration={0.6} className="text-center lg:text-left">
            <StaggeredAnimation
              staggerDelay={0.15}
              variant="slideUp"
              className="space-y-6"
            >
              <h1 className="text-5xl lg:text-7xl font-bold text-white font-mono">
                CBT SYSTEM
              </h1>
              <p className="text-xl lg:text-2xl text-gray-300 font-mono">
                Professional Computer-Based Testing Platform for Educational Excellence
              </p>
              <div className="flex flex-wrap gap-4 justify-center lg:justify-start">
                <div className="bg-black/40 backdrop-blur-sm rounded-lg px-4 py-2 border border-gray-600">
                  <span className="text-white font-medium font-mono">📝 Testing</span>
                </div>
                <div className="bg-black/40 backdrop-blur-sm rounded-lg px-4 py-2 border border-gray-600">
                  <span className="text-white font-medium font-mono">📊 Analytics</span>
                </div>
                <div className="bg-black/40 backdrop-blur-sm rounded-lg px-4 py-2 border border-gray-600">
                  <span className="text-white font-medium font-mono">🔒 Secure</span>
                </div>
              </div>
            </StaggeredAnimation>
          </SlideTransition>

          {/* Right side - Auth Forms */}
          <div className="flex justify-center lg:justify-end">
            <AnimatePresence mode="wait">
              {isLogin ? (
                <motion.div
                  key="login"
                  initial={{ opacity: 0, x: 30, scale: 0.95 }}
                  animate={{ opacity: 1, x: 0, scale: 1 }}
                  exit={{ opacity: 0, x: -30, scale: 1.05 }}
                  transition={{
                    duration: 0.4,
                    ease: [0.25, 0.1, 0.25, 1]
                  }}
                >
                  <LoginForm
                    onSubmit={handleLogin}
                    loading={loading}
                    error={error || undefined}
                    onSwitchToRegister={switchToRegister}
                  />
                </motion.div>
              ) : (
                <motion.div
                  key="register"
                  initial={{ opacity: 0, x: 30, scale: 0.95 }}
                  animate={{ opacity: 1, x: 0, scale: 1 }}
                  exit={{ opacity: 0, x: -30, scale: 1.05 }}
                  transition={{
                    duration: 0.4,
                    ease: [0.25, 0.1, 0.25, 1]
                  }}
                >
                  <RegisterForm
                    onSubmit={handleRegister}
                    loading={loading}
                    error={error || undefined}
                    onSwitchToLogin={switchToLogin}
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Footer */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1, duration: 0.5 }}
        className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-center"
      >
        <p className="text-gray-400 text-sm font-mono">
          © 2024 CBT System. Professional Computer-Based Testing Platform
        </p>
      </motion.div>
    </div>
  );
};
