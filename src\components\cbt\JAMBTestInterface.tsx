import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Clock,
  User,
  MapPin,
  ChevronLeft,
  ChevronRight,
  Flag,
  AlertTriangle,
  CheckCircle,
  Edit,
  Send,
  BookOpen
} from 'lucide-react';

interface Question {
  id: number;
  subject: string;
  question: string;
  options: {
    A: string;
    B: string;
    C: string;
    D: string;
  };
  correctAnswer: string;
  userAnswer?: string;
  flagged?: boolean;
}

interface TestSession {
  examTitle: string;
  candidateId: string;
  candidateName: string;
  location: string;
  duration: number; // in minutes
  totalQuestions: number;
  currentQuestion: number;
  timeRemaining: number; // in seconds
  questions: Question[];
}

interface JAMBTestInterfaceProps {
  onSubmitTest: (answers: Record<number, string>) => void;
  onExitTest: () => void;
}

export const JAMBTestInterface: React.FC<JAMBTestInterfaceProps> = ({
  onSubmitTest,
  onExitTest
}) => {
  const [testSession, setTestSession] = useState<TestSession>({
    examTitle: 'Computer-Based Test',
    candidateId: '1903589PG',
    candidateName: 'Sesan <PERSON> Sunday',
    location: 'Osogbo',
    duration: 60,
    totalQuestions: 50,
    currentQuestion: 1,
    timeRemaining: 3600, // 60 minutes in seconds
    questions: generateSampleQuestions()
  });

  const [answers, setAnswers] = useState<Record<number, string>>({});
  const [showSubmitDialog, setShowSubmitDialog] = useState(false);

  // Timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setTestSession(prev => {
        if (prev.timeRemaining <= 0) {
          clearInterval(timer);
          handleSubmitTest();
          return prev;
        }
        return { ...prev, timeRemaining: prev.timeRemaining - 1 };
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const currentQuestion = testSession.questions[testSession.currentQuestion - 1];

  const handleAnswerSelect = (answer: string) => {
    setAnswers(prev => ({
      ...prev,
      [testSession.currentQuestion]: answer
    }));
  };

  const handleQuestionNavigation = (questionNumber: number) => {
    setTestSession(prev => ({
      ...prev,
      currentQuestion: questionNumber
    }));
  };

  const handlePrevious = () => {
    if (testSession.currentQuestion > 1) {
      setTestSession(prev => ({
        ...prev,
        currentQuestion: prev.currentQuestion - 1
      }));
    }
  };

  const handleNext = () => {
    if (testSession.currentQuestion < testSession.totalQuestions) {
      setTestSession(prev => ({
        ...prev,
        currentQuestion: prev.currentQuestion + 1
      }));
    }
  };

  const handleFlagQuestion = () => {
    setTestSession(prev => ({
      ...prev,
      questions: prev.questions.map(q => 
        q.id === testSession.currentQuestion 
          ? { ...q, flagged: !q.flagged }
          : q
      )
    }));
  };

  const handleSubmitTest = () => {
    onSubmitTest(answers);
  };

  const getQuestionStatus = (questionNumber: number) => {
    const hasAnswer = answers[questionNumber];
    const isFlagged = testSession.questions[questionNumber - 1]?.flagged;
    
    if (hasAnswer && isFlagged) return 'answered-flagged';
    if (hasAnswer) return 'answered';
    if (isFlagged) return 'flagged';
    return 'unanswered';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'answered': return 'bg-green-500 text-white';
      case 'flagged': return 'bg-yellow-500 text-white';
      case 'answered-flagged': return 'bg-orange-500 text-white';
      default: return 'bg-gray-200 text-gray-700 hover:bg-gray-300';
    }
  };

  const answeredCount = Object.keys(answers).length;
  const flaggedCount = testSession.questions.filter(q => q.flagged).length;

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm border-b-2 border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Left side - Exam info */}
          <div className="flex items-center space-x-8">
            <div className="flex items-center space-x-2">
              <BookOpen className="text-blue-600" size={20} />
              <span className="font-semibold text-gray-800">Examination</span>
              <span className="text-gray-600">{testSession.examTitle}</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <User className="text-green-600" size={20} />
              <span className="font-semibold text-gray-800">Candidate</span>
              <span className="text-gray-600">{testSession.candidateId} {testSession.candidateName}</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <MapPin className="text-purple-600" size={20} />
              <span className="font-semibold text-gray-800">Location</span>
              <span className="text-gray-600">{testSession.location}</span>
            </div>
          </div>

          {/* Right side - Timer and photo */}
          <div className="flex items-center space-x-6">
            <div className="text-center">
              <div className="text-sm text-gray-600 mb-1">Remaining Time</div>
              <div className="text-2xl font-bold text-red-600">
                {formatTime(testSession.timeRemaining)}
              </div>
            </div>
            
            <div className="flex space-x-2">
              <button className="px-3 py-1 bg-green-500 text-white text-xs rounded">Instructions</button>
              <button className="px-3 py-1 bg-blue-500 text-white text-xs rounded flex items-center space-x-1">
                <Edit size={12} />
                <span>Edit</span>
              </button>
              <button
                onClick={() => setShowSubmitDialog(true)}
                className="px-3 py-1 bg-red-500 text-white text-xs rounded flex items-center space-x-1"
              >
                <Send size={12} />
                <span>Submit</span>
              </button>
            </div>
            
            {/* Candidate photo placeholder */}
            <div className="w-16 h-16 bg-red-200 rounded border-2 border-red-300 flex items-center justify-center">
              <User size={32} className="text-red-600" />
            </div>
          </div>
        </div>
      </header>

      {/* Subject tabs */}
      <div className="bg-white border-b border-gray-200 px-6">
        <div className="flex space-x-1">
          {['Mathematics', 'English', 'Physics', 'Use of English'].map((subject, index) => (
            <button
              key={subject}
              className={`px-4 py-2 text-sm font-medium border-b-2 ${
                index === 0 
                  ? 'border-blue-500 text-blue-600 bg-blue-50' 
                  : 'border-transparent text-gray-600 hover:text-gray-800'
              }`}
            >
              {subject}
            </button>
          ))}
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex">
        {/* Question area */}
        <div className="flex-1 p-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 h-full">
            {/* Question instruction */}
            <div className="mb-6">
              <p className="text-gray-700 font-medium">
                In the question below, pick the word that most suitably completes the sentence.
              </p>
            </div>

            {/* Question number */}
            <div className="mb-4">
              <span className="text-red-600 font-semibold">Question {testSession.currentQuestion}</span>
            </div>

            {/* Question text */}
            <div className="mb-8">
              <p className="text-gray-800 text-lg leading-relaxed">
                {currentQuestion?.question}
              </p>
            </div>

            {/* Options */}
            <div className="space-y-3 mb-8">
              {currentQuestion && Object.entries(currentQuestion.options).map(([key, value]) => (
                <motion.label
                  key={key}
                  whileHover={{ scale: 1.01 }}
                  className={`flex items-center p-3 rounded-lg border-2 cursor-pointer transition-all ${
                    answers[testSession.currentQuestion] === key
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <input
                    type="radio"
                    name={`question-${testSession.currentQuestion}`}
                    value={key}
                    checked={answers[testSession.currentQuestion] === key}
                    onChange={() => handleAnswerSelect(key)}
                    className="mr-3 text-blue-600"
                  />
                  <span className="font-semibold text-gray-700 mr-2">{key}.</span>
                  <span className="text-gray-800">{value}</span>
                </motion.label>
              ))}
            </div>

            {/* Navigation buttons */}
            <div className="flex justify-between items-center">
              <button
                onClick={handlePrevious}
                disabled={testSession.currentQuestion === 1}
                className="flex items-center space-x-2 px-4 py-2 bg-gray-500 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600 transition-colors"
              >
                <ChevronLeft size={16} />
                <span>Previous</span>
              </button>

              <button
                onClick={handleFlagQuestion}
                className={`flex items-center space-x-2 px-4 py-2 rounded transition-colors ${
                  currentQuestion?.flagged
                    ? 'bg-yellow-500 text-white hover:bg-yellow-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                <Flag size={16} />
                <span>{currentQuestion?.flagged ? 'Unflag' : 'Flag'}</span>
              </button>

              <button
                onClick={handleNext}
                disabled={testSession.currentQuestion === testSession.totalQuestions}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-600 transition-colors"
              >
                <span>Next</span>
                <ChevronRight size={16} />
              </button>
            </div>
          </div>
        </div>

        {/* Question navigator */}
        <div className="w-80 p-6 bg-gray-50 border-l border-gray-200">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <h3 className="font-semibold text-gray-800 mb-4">Question Navigator</h3>
            
            {/* Stats */}
            <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{answeredCount}</div>
                <div className="text-gray-600">Answered</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{flaggedCount}</div>
                <div className="text-gray-600">Flagged</div>
              </div>
            </div>

            {/* Question grid */}
            <div className="grid grid-cols-5 gap-2">
              {Array.from({ length: testSession.totalQuestions }, (_, i) => i + 1).map((num) => (
                <button
                  key={num}
                  onClick={() => handleQuestionNavigation(num)}
                  className={`w-10 h-10 text-sm font-medium rounded transition-all ${
                    num === testSession.currentQuestion
                      ? 'ring-2 ring-blue-500 ring-offset-1'
                      : ''
                  } ${getStatusColor(getQuestionStatus(num))}`}
                >
                  {num}
                </button>
              ))}
            </div>

            {/* Legend */}
            <div className="mt-4 space-y-2 text-xs">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-green-500 rounded"></div>
                <span>Answered</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-yellow-500 rounded"></div>
                <span>Flagged</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-gray-200 rounded"></div>
                <span>Not Answered</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Submit Dialog */}
      <AnimatePresence>
        {showSubmitDialog && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg p-6 max-w-md w-full mx-4"
            >
              <div className="flex items-center space-x-3 mb-4">
                <AlertTriangle className="text-red-500" size={24} />
                <h3 className="text-lg font-semibold">Submit Test</h3>
              </div>
              
              <p className="text-gray-600 mb-6">
                Are you sure you want to submit your test? You have answered {answeredCount} out of {testSession.totalQuestions} questions.
              </p>
              
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowSubmitDialog(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSubmitTest}
                  className="flex-1 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                >
                  Submit Test
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Sample questions generator
function generateSampleQuestions(): Question[] {
  const sampleQuestions = [
    {
      id: 1,
      subject: 'English',
      question: 'Tekena visited the children and _______ presents for them.',
      options: {
        A: 'brought',
        B: 'bring',
        C: 'bringeth',
        D: 'bringed'
      },
      correctAnswer: 'A'
    },
    {
      id: 2,
      subject: 'Mathematics',
      question: 'If 2x + 3 = 11, what is the value of x?',
      options: {
        A: '3',
        B: '4',
        C: '5',
        D: '6'
      },
      correctAnswer: 'B'
    }
    // Add more questions as needed
  ];

  // Generate 50 questions by repeating and modifying
  const questions: Question[] = [];
  for (let i = 0; i < 50; i++) {
    const baseQuestion = sampleQuestions[i % sampleQuestions.length];
    questions.push({
      ...baseQuestion,
      id: i + 1,
      question: `${baseQuestion.question} (Question ${i + 1})`
    });
  }

  return questions;
}
