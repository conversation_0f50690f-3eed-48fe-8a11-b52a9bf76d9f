import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Users,
  BookOpen,
  Settings,
  Shield,
  Database,
  Activity,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Server,
  Wifi,
  HardDrive,
  Monitor,
  UserPlus,
  FileText,
  Download
} from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { NavigationHeader, generateBreadcrumbs } from '../shared/NavigationHeader';

interface AdminDashboardProps {
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
    avatar?: string;
  };
  onBack: () => void;
  onLogout: () => void;
}

interface SystemStats {
  totalUsers: number;
  activeUsers: number;
  totalTests: number;
  systemUptime: string;
  databaseHealth: 'healthy' | 'warning' | 'critical';
  syncStatus: 'active' | 'inactive' | 'error';
  storageUsed: number;
  storageTotal: number;
}

export const AdminDashboard: React.FC<AdminDashboardProps> = ({
  user,
  onBack,
  onLogout
}) => {
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState<'overview' | 'users' | 'system' | 'modules'>('overview');
  
  const [stats, setStats] = useState<SystemStats>({
    totalUsers: 1247,
    activeUsers: 89,
    totalTests: 156,
    systemUptime: '15d 8h 23m',
    databaseHealth: 'healthy',
    syncStatus: 'active',
    storageUsed: 45.2,
    storageTotal: 100
  });

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getHealthIcon = (health: string) => {
    switch (health) {
      case 'healthy': return <CheckCircle size={16} />;
      case 'warning': return <AlertTriangle size={16} />;
      case 'critical': return <AlertTriangle size={16} />;
      default: return <Activity size={16} />;
    }
  };

  const StatCard = ({ icon: Icon, title, value, subtitle, color, status }: any) => (
    <motion.div
      whileHover={{ scale: 1.02 }}
      className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700"
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400 font-mono">{title}</p>
          <p className={`text-2xl font-bold ${color} font-mono`}>{value}</p>
          {subtitle && <p className="text-xs text-gray-500 dark:text-gray-500 font-mono">{subtitle}</p>}
        </div>
        <div className={`p-3 rounded-lg ${color.replace('text-', 'bg-').replace('600', '100')} dark:bg-opacity-20`}>
          <Icon size={24} className={color} />
        </div>
      </div>
      {status && (
        <div className="mt-4 flex items-center space-x-2">
          {getHealthIcon(status)}
          <span className={`text-sm font-medium ${getHealthColor(status)} font-mono capitalize`}>
            {status}
          </span>
        </div>
      )}
    </motion.div>
  );

  const breadcrumbs = generateBreadcrumbs([activeTab], user.role);

  const systemStatusAction = (
    <div className="flex items-center space-x-2">
      <div className={`w-2 h-2 rounded-full ${stats.databaseHealth === 'healthy' ? 'bg-green-500' : 'bg-red-500'}`} />
      <span className="text-sm text-gray-600 dark:text-gray-400 font-mono">System Status</span>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Navigation Header */}
      <NavigationHeader
        title="Admin Dashboard"
        breadcrumbs={breadcrumbs}
        user={user}
        onBack={onBack}
        onLogout={onLogout}
        notificationCount={3} // System alerts
        actions={systemStatusAction}
      />

      {/* Navigation Tabs */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview', icon: Monitor },
              { id: 'users', label: 'Users', icon: Users },
              { id: 'system', label: 'System', icon: Server },
              { id: 'modules', label: 'Modules', icon: Settings }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm font-mono transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
              >
                <tab.icon size={16} />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-8"
          >
            {/* System Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <StatCard
                icon={Users}
                title="Total Users"
                value={stats.totalUsers}
                subtitle={`${stats.activeUsers} active now`}
                color="text-blue-600"
              />
              <StatCard
                icon={BookOpen}
                title="Total Tests"
                value={stats.totalTests}
                color="text-green-600"
              />
              <StatCard
                icon={Database}
                title="Database"
                value="Healthy"
                subtitle="All connections active"
                color="text-purple-600"
                status={stats.databaseHealth}
              />
              <StatCard
                icon={Activity}
                title="Uptime"
                value={stats.systemUptime}
                color="text-orange-600"
              />
            </div>

            {/* System Health */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white font-mono">System Health</h2>
                </div>
                <div className="p-6 space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Database size={20} className="text-green-600" />
                      <span className="font-medium text-gray-900 dark:text-white font-mono">Database</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle size={16} className="text-green-600" />
                      <span className="text-green-600 font-mono">Healthy</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Wifi size={20} className="text-green-600" />
                      <span className="font-medium text-gray-900 dark:text-white font-mono">Sync Service</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle size={16} className="text-green-600" />
                      <span className="text-green-600 font-mono">Active</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <HardDrive size={20} className="text-yellow-600" />
                      <span className="font-medium text-gray-900 dark:text-white font-mono">Storage</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <AlertTriangle size={16} className="text-yellow-600" />
                      <span className="text-yellow-600 font-mono">{stats.storageUsed}% Used</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white font-mono">Recent Activity</h2>
                </div>
                <div className="p-6 space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2" />
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white font-mono">New user registered</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 font-mono">2 minutes ago</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white font-mono">Test completed by 15 students</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 font-mono">5 minutes ago</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mt-2" />
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white font-mono">System backup completed</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 font-mono">1 hour ago</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <motion.button
                whileHover={{ scale: 1.02 }}
                className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700 text-left"
              >
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                    <UserPlus size={24} className="text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white font-mono">Add User</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 font-mono">Create new account</p>
                  </div>
                </div>
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.02 }}
                className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700 text-left"
              >
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
                    <Database size={24} className="text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white font-mono">Backup</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 font-mono">Create system backup</p>
                  </div>
                </div>
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.02 }}
                className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700 text-left"
              >
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                    <FileText size={24} className="text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white font-mono">Reports</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 font-mono">Generate reports</p>
                  </div>
                </div>
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.02 }}
                className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700 text-left"
              >
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
                    <Settings size={24} className="text-orange-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white font-mono">Settings</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 font-mono">System configuration</p>
                  </div>
                </div>
              </motion.button>
            </div>
          </motion.div>
        )}

        {/* Add other tab content here */}
      </main>
    </div>
  );
};
