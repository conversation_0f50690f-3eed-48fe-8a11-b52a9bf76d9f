import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus, 
  Save, 
  Eye, 
  Trash2, 
  Co<PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Clock, 
  Users, 
  FileText,
  Image,
  Video,
  Mic,
  Upload,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { useCBT } from '../../contexts/CBTContext';
import { Test, Question, QuestionType, DifficultyLevel } from '../../types/cbt.types';
import { cbtService } from '../../services/cbt.service';

interface QuestionFormProps {
  question?: Question;
  onSave: (question: Partial<Question>) => void;
  onCancel: () => void;
}

const QuestionForm: React.FC<QuestionFormProps> = ({ question, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    type: question?.type || 'multiple_choice' as QuestionType,
    title: question?.title || '',
    content: question?.content || '',
    points: question?.points || 1,
    difficulty: question?.difficulty || 'medium' as DifficultyLevel,
    timeLimit: question?.timeLimit || 60,
    options: question?.options || [
      { id: '1', text: '', isCorrect: false },
      { id: '2', text: '', isCorrect: false },
      { id: '3', text: '', isCorrect: false },
      { id: '4', text: '', isCorrect: false }
    ],
    correctAnswer: question?.correctAnswer || '',
    explanation: question?.explanation || '',
    tags: question?.tags?.join(', ') || '',
    category: question?.category || ''
  });

  const questionTypes = [
    { value: 'multiple_choice', label: 'Multiple Choice', icon: '◉' },
    { value: 'true_false', label: 'True/False', icon: '✓' },
    { value: 'essay', label: 'Essay', icon: '📝' },
    { value: 'fill_blank', label: 'Fill in the Blank', icon: '___' },
    { value: 'matching', label: 'Matching', icon: '⟷' },
    { value: 'ordering', label: 'Ordering', icon: '↕' },
    { value: 'numeric', label: 'Numeric', icon: '123' },
    { value: 'file_upload', label: 'File Upload', icon: '📎' }
  ];

  const difficulties = [
    { value: 'easy', label: 'Easy', color: 'text-green-400' },
    { value: 'medium', label: 'Medium', color: 'text-yellow-400' },
    { value: 'hard', label: 'Hard', color: 'text-red-400' }
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const questionData: Partial<Question> = {
      ...formData,
      tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
      options: formData.type === 'multiple_choice' ? formData.options : undefined
    };

    onSave(questionData);
  };

  const addOption = () => {
    setFormData(prev => ({
      ...prev,
      options: [
        ...prev.options,
        { id: Date.now().toString(), text: '', isCorrect: false }
      ]
    }));
  };

  const removeOption = (optionId: string) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.filter(opt => opt.id !== optionId)
    }));
  };

  const updateOption = (optionId: string, text: string, isCorrect?: boolean) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map(opt => 
        opt.id === optionId 
          ? { ...opt, text, ...(isCorrect !== undefined && { isCorrect }) }
          : opt
      )
    }));
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className="bg-black/80 backdrop-blur-xl rounded-xl p-6 border border-gray-600/50"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-bold text-white font-mono">
            {question ? 'Edit Question' : 'Create New Question'}
          </h3>
          <div className="flex space-x-2">
            <motion.button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors font-mono"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Cancel
            </motion.button>
            <motion.button
              type="submit"
              className="px-4 py-2 bg-black hover:bg-gray-900 text-white rounded-lg transition-colors font-mono border border-gray-600"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Save size={16} className="inline mr-2" />
              Save Question
            </motion.button>
          </div>
        </div>

        {/* Question Type and Basic Info */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Question Type</label>
            <select
              value={formData.type}
              onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as QuestionType }))}
              className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
            >
              {questionTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.icon} {type.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Difficulty</label>
            <select
              value={formData.difficulty}
              onChange={(e) => setFormData(prev => ({ ...prev, difficulty: e.target.value as DifficultyLevel }))}
              className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
            >
              {difficulties.map(diff => (
                <option key={diff.value} value={diff.value}>
                  {diff.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Points</label>
            <input
              type="number"
              min="1"
              max="100"
              value={formData.points}
              onChange={(e) => setFormData(prev => ({ ...prev, points: parseInt(e.target.value) }))}
              className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Time Limit (seconds)</label>
            <input
              type="number"
              min="30"
              max="3600"
              value={formData.timeLimit}
              onChange={(e) => setFormData(prev => ({ ...prev, timeLimit: parseInt(e.target.value) }))}
              className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
            />
          </div>
        </div>

        {/* Question Title */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Question Title</label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
            placeholder="Enter a brief title for this question"
            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
            required
          />
        </div>

        {/* Question Content */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Question Content</label>
          <textarea
            value={formData.content}
            onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
            placeholder="Enter the full question text here..."
            rows={4}
            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono resize-none"
            required
          />
        </div>

        {/* Question Type Specific Fields */}
        {formData.type === 'multiple_choice' && (
          <div>
            <div className="flex items-center justify-between mb-4">
              <label className="block text-sm font-medium text-gray-300 font-mono">Answer Options</label>
              <motion.button
                type="button"
                onClick={addOption}
                className="px-3 py-1 bg-gray-800 hover:bg-gray-700 text-white rounded text-sm font-mono"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Plus size={14} className="inline mr-1" />
                Add Option
              </motion.button>
            </div>
            
            <div className="space-y-3">
              {formData.options.map((option, index) => (
                <div key={option.id} className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="correctAnswer"
                    checked={option.isCorrect}
                    onChange={() => {
                      setFormData(prev => ({
                        ...prev,
                        options: prev.options.map(opt => ({
                          ...opt,
                          isCorrect: opt.id === option.id
                        }))
                      }));
                    }}
                    className="text-gray-600 focus:ring-gray-500"
                  />
                  <input
                    type="text"
                    value={option.text}
                    onChange={(e) => updateOption(option.id, e.target.value)}
                    placeholder={`Option ${index + 1}`}
                    className="flex-1 px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
                  />
                  {formData.options.length > 2 && (
                    <motion.button
                      type="button"
                      onClick={() => removeOption(option.id)}
                      className="p-2 text-red-400 hover:text-red-300 transition-colors"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <Trash2 size={16} />
                    </motion.button>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {formData.type === 'true_false' && (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Correct Answer</label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="trueFalse"
                  value="true"
                  checked={formData.correctAnswer === 'true'}
                  onChange={(e) => setFormData(prev => ({ ...prev, correctAnswer: e.target.value }))}
                  className="text-gray-600 focus:ring-gray-500"
                />
                <span className="ml-2 text-white font-mono">True</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="trueFalse"
                  value="false"
                  checked={formData.correctAnswer === 'false'}
                  onChange={(e) => setFormData(prev => ({ ...prev, correctAnswer: e.target.value }))}
                  className="text-gray-600 focus:ring-gray-500"
                />
                <span className="ml-2 text-white font-mono">False</span>
              </label>
            </div>
          </div>
        )}

        {(formData.type === 'fill_blank' || formData.type === 'numeric') && (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Correct Answer</label>
            <input
              type={formData.type === 'numeric' ? 'number' : 'text'}
              value={formData.correctAnswer}
              onChange={(e) => setFormData(prev => ({ ...prev, correctAnswer: e.target.value }))}
              placeholder="Enter the correct answer"
              className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
              required
            />
          </div>
        )}

        {/* Explanation */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Explanation (Optional)</label>
          <textarea
            value={formData.explanation}
            onChange={(e) => setFormData(prev => ({ ...prev, explanation: e.target.value }))}
            placeholder="Provide an explanation for the correct answer..."
            rows={3}
            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono resize-none"
          />
        </div>

        {/* Tags and Category */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Category</label>
            <input
              type="text"
              value={formData.category}
              onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
              placeholder="e.g., Mathematics, Science, History"
              className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Tags (comma-separated)</label>
            <input
              type="text"
              value={formData.tags}
              onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
              placeholder="e.g., algebra, equations, basic"
              className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono"
            />
          </div>
        </div>

        {/* Media Upload Section */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2 font-mono">Media Attachments</label>
          <div className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center">
            <div className="flex justify-center space-x-4 mb-4">
              <motion.button
                type="button"
                className="p-3 bg-gray-800 hover:bg-gray-700 rounded-lg text-gray-300 hover:text-white transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Image size={20} />
              </motion.button>
              <motion.button
                type="button"
                className="p-3 bg-gray-800 hover:bg-gray-700 rounded-lg text-gray-300 hover:text-white transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Video size={20} />
              </motion.button>
              <motion.button
                type="button"
                className="p-3 bg-gray-800 hover:bg-gray-700 rounded-lg text-gray-300 hover:text-white transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Mic size={20} />
              </motion.button>
              <motion.button
                type="button"
                className="p-3 bg-gray-800 hover:bg-gray-700 rounded-lg text-gray-300 hover:text-white transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Upload size={20} />
              </motion.button>
            </div>
            <p className="text-gray-400 font-mono text-sm">
              Drag and drop files here or click to upload
            </p>
            <p className="text-gray-500 font-mono text-xs mt-2">
              Supported: Images, Videos, Audio files (Max 10MB)
            </p>
          </div>
        </div>
      </form>
    </motion.div>
  );
};

export const TestCreation: React.FC = () => {
  const { state } = useCBT();
  const [test, setTest] = useState<Partial<Test>>({
    title: '',
    description: '',
    instructions: '',
    duration: 60,
    totalPoints: 0,
    passingScore: 70,
    maxAttempts: 3,
    shuffleQuestions: true,
    shuffleOptions: true,
    showResults: true,
    allowReview: true,
    isPublished: false,
    questions: [],
    categories: []
  });
  
  const [questions, setQuestions] = useState<Question[]>([]);
  const [showQuestionForm, setShowQuestionForm] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null);
  const [activeTab, setActiveTab] = useState<'basic' | 'questions' | 'settings' | 'preview'>('basic');

  const handleSaveQuestion = (questionData: Partial<Question>) => {
    if (editingQuestion) {
      // Update existing question
      setQuestions(prev => prev.map(q => 
        q.id === editingQuestion.id 
          ? { ...q, ...questionData, updatedAt: new Date() }
          : q
      ));
    } else {
      // Add new question
      const newQuestion: Question = {
        id: Date.now().toString(),
        ...questionData,
        createdBy: state.user?.id || '',
        createdAt: new Date(),
        updatedAt: new Date()
      } as Question;
      
      setQuestions(prev => [...prev, newQuestion]);
    }
    
    setShowQuestionForm(false);
    setEditingQuestion(null);
    
    // Update test total points
    const totalPoints = questions.reduce((sum, q) => sum + (q.points || 0), 0) + (questionData.points || 0);
    setTest(prev => ({ ...prev, totalPoints }));
  };

  const handleDeleteQuestion = (questionId: string) => {
    setQuestions(prev => prev.filter(q => q.id !== questionId));
    const totalPoints = questions.filter(q => q.id !== questionId).reduce((sum, q) => sum + (q.points || 0), 0);
    setTest(prev => ({ ...prev, totalPoints }));
  };

  const handleSaveTest = async () => {
    try {
      const testData: Partial<Test> = {
        ...test,
        questions,
        createdBy: state.user?.id || '',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const response = await cbtService.createTest(testData as Test);
      if (response.success) {
        // Handle success - maybe navigate back or show success message
        console.log('Test saved successfully');
      }
    } catch (error) {
      console.error('Error saving test:', error);
    }
  };

  const tabs = [
    { id: 'basic', label: 'Basic Info', icon: FileText },
    { id: 'questions', label: 'Questions', icon: Plus },
    { id: 'settings', label: 'Settings', icon: Settings },
    { id: 'preview', label: 'Preview', icon: Eye }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white font-mono">Create New Test</h2>
          <p className="text-gray-400 font-mono">Build a comprehensive test with multiple question types</p>
        </div>
        
        <div className="flex space-x-3">
          <motion.button
            onClick={handleSaveTest}
            className="px-6 py-2 bg-black hover:bg-gray-900 text-white rounded-lg transition-colors font-mono border border-gray-600"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Save size={16} className="inline mr-2" />
            Save Test
          </motion.button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-700">
        <nav className="flex space-x-8">
          {tabs.map(tab => {
            const IconComponent = tab.icon;
            return (
              <motion.button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm font-mono transition-colors ${
                  activeTab === tab.id
                    ? 'border-white text-white'
                    : 'border-transparent text-gray-400 hover:text-gray-300'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <IconComponent size={16} />
                <span>{tab.label}</span>
                {tab.id === 'questions' && questions.length > 0 && (
                  <span className="bg-gray-700 text-white text-xs px-2 py-1 rounded-full">
                    {questions.length}
                  </span>
                )}
              </motion.button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <AnimatePresence mode="wait">
        {activeTab === 'basic' && (
          <motion.div
            key="basic"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            className="space-y-6"
          >
            {/* Basic test information form would go here */}
            <div className="bg-black/60 backdrop-blur-xl rounded-xl p-6 border border-gray-600/50">
              <h3 className="text-lg font-bold text-white mb-4 font-mono">Test Information</h3>
              {/* Form fields for basic test info */}
            </div>
          </motion.div>
        )}

        {activeTab === 'questions' && (
          <motion.div
            key="questions"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            className="space-y-6"
          >
            {/* Questions management */}
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-bold text-white font-mono">Test Questions ({questions.length})</h3>
              <motion.button
                onClick={() => setShowQuestionForm(true)}
                className="px-4 py-2 bg-black hover:bg-gray-900 text-white rounded-lg transition-colors font-mono border border-gray-600"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Plus size={16} className="inline mr-2" />
                Add Question
              </motion.button>
            </div>

            {showQuestionForm && (
              <QuestionForm
                question={editingQuestion || undefined}
                onSave={handleSaveQuestion}
                onCancel={() => {
                  setShowQuestionForm(false);
                  setEditingQuestion(null);
                }}
              />
            )}

            {/* Questions list */}
            <div className="space-y-4">
              {questions.map((question, index) => (
                <motion.div
                  key={question.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-black/60 backdrop-blur-xl rounded-lg p-4 border border-gray-600/50"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <span className="text-gray-400 font-mono text-sm">Q{index + 1}</span>
                        <span className="px-2 py-1 bg-gray-800 text-gray-300 rounded text-xs font-mono">
                          {question.type.replace('_', ' ').toUpperCase()}
                        </span>
                        <span className="px-2 py-1 bg-gray-800 text-gray-300 rounded text-xs font-mono">
                          {question.points} pts
                        </span>
                        <span className={`px-2 py-1 rounded text-xs font-mono ${
                          question.difficulty === 'easy' ? 'bg-green-900/30 text-green-400' :
                          question.difficulty === 'medium' ? 'bg-yellow-900/30 text-yellow-400' :
                          'bg-red-900/30 text-red-400'
                        }`}>
                          {question.difficulty.toUpperCase()}
                        </span>
                      </div>
                      <h4 className="text-white font-medium font-mono mb-1">{question.title}</h4>
                      <p className="text-gray-400 text-sm font-mono line-clamp-2">{question.content}</p>
                    </div>
                    
                    <div className="flex space-x-2">
                      <motion.button
                        onClick={() => {
                          setEditingQuestion(question);
                          setShowQuestionForm(true);
                        }}
                        className="p-2 text-gray-400 hover:text-white transition-colors"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <FileText size={16} />
                      </motion.button>
                      <motion.button
                        onClick={() => handleDeleteQuestion(question.id)}
                        className="p-2 text-red-400 hover:text-red-300 transition-colors"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <Trash2 size={16} />
                      </motion.button>
                    </div>
                  </div>
                </motion.div>
              ))}
              
              {questions.length === 0 && (
                <div className="text-center py-12">
                  <Plus className="mx-auto text-gray-600 mb-4" size={48} />
                  <h3 className="text-xl font-bold text-gray-400 mb-2 font-mono">No questions yet</h3>
                  <p className="text-gray-500 font-mono">Click &quot;Add Question&quot; to get started</p>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
