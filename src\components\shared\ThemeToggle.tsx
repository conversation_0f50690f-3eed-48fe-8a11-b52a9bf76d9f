import React from 'react';
import { motion } from 'framer-motion';
import { Sun, Moon, Monitor } from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';

interface ThemeToggleProps {
  className?: string;
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({ 
  className = '', 
  showLabel = false,
  size = 'md' 
}) => {
  const { theme, toggleTheme, getAnimationDuration } = useTheme();

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12'
  };

  const iconSizes = {
    sm: 16,
    md: 20,
    lg: 24
  };

  const handleToggle = () => {
    toggleTheme();
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <motion.button
        onClick={handleToggle}
        className={`
          ${sizeClasses[size]} 
          rounded-lg border-2 border-gray-300 dark:border-gray-600
          bg-white dark:bg-gray-800 
          hover:bg-gray-50 dark:hover:bg-gray-700
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
          transition-colors duration-200
          flex items-center justify-center
          relative overflow-hidden
        `}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        transition={{ 
          duration: getAnimationDuration('fast'),
          ease: "easeInOut"
        }}
        aria-label={`Switch to ${theme.mode === 'light' ? 'dark' : 'light'} mode`}
      >
        {/* Background animation */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 dark:from-blue-600 dark:to-purple-600"
          initial={{ opacity: 0 }}
          animate={{ opacity: theme.mode === 'light' ? 0.1 : 0.2 }}
          transition={{ duration: getAnimationDuration('normal') }}
        />
        
        {/* Icon container */}
        <motion.div
          className="relative z-10"
          animate={{ 
            rotate: theme.mode === 'light' ? 0 : 180,
            scale: [1, 1.2, 1]
          }}
          transition={{ 
            duration: getAnimationDuration('normal'),
            ease: "easeInOut"
          }}
        >
          {theme.mode === 'light' ? (
            <Sun 
              size={iconSizes[size]} 
              className="text-yellow-600 dark:text-yellow-400" 
            />
          ) : (
            <Moon 
              size={iconSizes[size]} 
              className="text-blue-600 dark:text-blue-400" 
            />
          )}
        </motion.div>

        {/* Ripple effect */}
        <motion.div
          className="absolute inset-0 rounded-lg"
          initial={{ scale: 0, opacity: 0.5 }}
          animate={{ scale: 0, opacity: 0 }}
          whileTap={{ scale: 1.5, opacity: 0.3 }}
          transition={{ duration: 0.3 }}
          style={{
            background: theme.mode === 'light' 
              ? 'radial-gradient(circle, rgba(251, 191, 36, 0.3) 0%, transparent 70%)'
              : 'radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%)'
          }}
        />
      </motion.button>

      {showLabel && (
        <motion.span
          className="text-sm font-mono text-gray-700 dark:text-gray-300"
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: getAnimationDuration('normal') }}
        >
          {theme.mode === 'light' ? 'Light' : 'Dark'}
        </motion.span>
      )}
    </div>
  );
};

// Advanced theme selector with multiple options
export const ThemeSelector: React.FC<{ className?: string }> = ({ className = '' }) => {
  const { theme, setThemeMode, getAnimationDuration } = useTheme();

  const themes = [
    { mode: 'light' as const, icon: Sun, label: 'Light', color: 'text-yellow-600' },
    { mode: 'dark' as const, icon: Moon, label: 'Dark', color: 'text-blue-600' }
  ];

  return (
    <div className={`flex items-center gap-1 p-1 bg-gray-100 dark:bg-gray-800 rounded-lg ${className}`}>
      {themes.map(({ mode, icon: Icon, label, color }) => (
        <motion.button
          key={mode}
          onClick={() => setThemeMode(mode)}
          className={`
            relative px-3 py-2 rounded-md text-sm font-mono
            transition-all duration-200
            ${theme.mode === mode 
              ? 'bg-white dark:bg-gray-700 shadow-sm text-gray-900 dark:text-gray-100' 
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
            }
          `}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          transition={{ duration: getAnimationDuration('fast') }}
        >
          <div className="flex items-center gap-2">
            <Icon size={16} className={theme.mode === mode ? color : ''} />
            <span>{label}</span>
          </div>
          
          {theme.mode === mode && (
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-md"
              layoutId="theme-selector-bg"
              transition={{ duration: getAnimationDuration('normal') }}
            />
          )}
        </motion.button>
      ))}
    </div>
  );
};
