import React from 'react';
import { motion } from 'framer-motion';
import { Users, TrendingUp, FileText, DollarSign, Activity, Clock } from 'lucide-react';
import { StatsCard } from './StatsCard';
import { User } from '../../types/auth.types';

interface DashboardContentProps {
  user: User;
}

export const DashboardContent: React.FC<DashboardContentProps> = ({ user }) => {
  const statsData = [
    {
      title: 'Total Users',
      value: '12,345',
      change: { value: 12, type: 'increase' as const },
      icon: Users,
      color: 'blue' as const
    },
    {
      title: 'Revenue',
      value: '$45,678',
      change: { value: 8, type: 'increase' as const },
      icon: DollarSign,
      color: 'green' as const
    },
    {
      title: 'Active Sessions',
      value: '1,234',
      change: { value: 5, type: 'decrease' as const },
      icon: Activity,
      color: 'purple' as const
    },
    {
      title: 'Documents',
      value: '567',
      change: { value: 15, type: 'increase' as const },
      icon: FileText,
      color: 'orange' as const
    }
  ];

  const recentActivities = [
    { id: 1, action: 'User <PERSON> logged in', time: '2 minutes ago', type: 'login' },
    { id: 2, action: 'New document uploaded', time: '5 minutes ago', type: 'upload' },
    { id: 3, action: 'System backup completed', time: '10 minutes ago', type: 'system' },
    { id: 4, action: 'User Jane Smith updated profile', time: '15 minutes ago', type: 'update' },
    { id: 5, action: 'New user registered', time: '20 minutes ago', type: 'register' }
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, ease: [0.25, 0.1, 0.25, 1] }}
        className="bg-gradient-to-r from-slate-800/30 to-gray-800/30 backdrop-blur-xl rounded-xl p-6 border border-slate-700/50 ring-1 ring-slate-600/20"
      >
        <h1 className="text-3xl font-bold text-white mb-2">
          Welcome back, {user.name}! 👋
        </h1>
        <p className="text-gray-300">
          Here&apos;s what&apos;s happening with your application today.
        </p>
      </motion.div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsData.map((stat, index) => (
          <StatsCard
            key={stat.title}
            {...stat}
            delay={index * 0.1}
          />
        ))}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Chart Placeholder */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2, duration: 0.4, ease: [0.25, 0.1, 0.25, 1] }}
          className="bg-slate-900/40 backdrop-blur-xl rounded-xl p-6 border border-slate-700/50 ring-1 ring-slate-600/20"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-semibold text-white">Analytics Overview</h3>
            <TrendingUp className="text-green-400" size={24} />
          </div>
          <div className="h-64 bg-gradient-to-br from-gray-500/20 to-slate-500/20 rounded-lg flex items-center justify-center">
            <p className="text-gray-300">Chart visualization would go here</p>
          </div>
        </motion.div>

        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3, duration: 0.4, ease: [0.25, 0.1, 0.25, 1] }}
          className="bg-slate-900/40 backdrop-blur-xl rounded-xl p-6 border border-slate-700/50 ring-1 ring-slate-600/20"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-semibold text-white">Recent Activity</h3>
            <Clock className="text-slate-400" size={24} />
          </div>
          <div className="space-y-3">
            {recentActivities.map((activity, index) => (
              <motion.div
                key={activity.id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 + index * 0.05, duration: 0.3, ease: [0.25, 0.1, 0.25, 1] }}
                className="flex items-center space-x-3 p-3 rounded-lg bg-slate-800/30 hover:bg-slate-700/40 transition-colors backdrop-blur-sm"
              >
                <div className={`w-2 h-2 rounded-full ${
                  activity.type === 'login' ? 'bg-gray-400' :
                  activity.type === 'upload' ? 'bg-slate-400' :
                  activity.type === 'system' ? 'bg-gray-600' :
                  activity.type === 'update' ? 'bg-slate-600' :
                  'bg-gray-500'
                }`} />
                <div className="flex-1">
                  <p className="text-white text-sm">{activity.action}</p>
                  <p className="text-gray-400 text-xs">{activity.time}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4, duration: 0.4, ease: [0.25, 0.1, 0.25, 1] }}
        className="bg-slate-900/40 backdrop-blur-xl rounded-xl p-6 border border-slate-700/50 ring-1 ring-slate-600/20"
      >
        <h3 className="text-xl font-semibold text-white mb-4">Quick Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            { label: 'Add User', icon: Users, color: 'bg-slate-600' },
            { label: 'Upload File', icon: FileText, color: 'bg-gray-600' },
            { label: 'View Reports', icon: TrendingUp, color: 'bg-slate-700' },
            { label: 'Settings', icon: Activity, color: 'bg-gray-700' }
          ].map((action, index) => (
            <motion.button
              key={action.label}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5 + index * 0.05, duration: 0.3, ease: [0.25, 0.1, 0.25, 1] }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex flex-col items-center p-4 rounded-lg bg-slate-800/30 hover:bg-slate-700/40 transition-colors backdrop-blur-sm"
            >
              <div className={`p-3 rounded-lg ${action.color} mb-2`}>
                <action.icon size={20} className="text-white" />
              </div>
              <span className="text-white text-sm font-medium">{action.label}</span>
            </motion.button>
          ))}
        </div>
      </motion.div>
    </div>
  );
};
