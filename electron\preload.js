const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {

  database: {
    createUser: (userData) => ipcRenderer.invoke('database:createUser', userData),
    getUserById: (id) => ipcRenderer.invoke('database:getUserById', id),
    getUserByEmail: (email) => ipcRenderer.invoke('database:getUserByEmail', email),
    updateUser: (id, updates) => ipcRenderer.invoke('database:updateUser', id, updates),
    updateLastLogin: (id) => ipcRenderer.invoke('database:updateLastLogin', id),
    verifyPassword: (email, password) => ipcRenderer.invoke('database:verifyPassword', email, password),
    createModule: (moduleData) => ipcRenderer.invoke('database:createModule', moduleData),
    getModuleById: (id) => ipcRenderer.invoke('database:getModuleById', id),
    getModulesByType: (type) => ipcRenderer.invoke('database:getModulesByType', type),
    getAllModules: () => ipcRenderer.invoke('database:getAllModules'),
    updateModule: (id, updates) => ipcRenderer.invoke('database:updateModule', id, updates),
    createTest: (testData) => ipcRenderer.invoke('database:createTest', testData),
    syncToCloud: (data, collection) => ipcRenderer.invoke('database:syncToCloud', data, collection),
    syncFromCloud: (collection, lastSyncTime) => ipcRenderer.invoke('database:syncFromCloud', collection, lastSyncTime),
    checkHealth: () => ipcRenderer.invoke('database:checkHealth')
  },

  sync: {
    getAvailableMethods: () => ipcRenderer.invoke('sync:getAvailableMethods'),
    startDiscovery: () => ipcRenderer.invoke('sync:startDiscovery'),
    syncWithDevice: (deviceId, dataTypes) => ipcRenderer.invoke('sync:syncWithDevice', deviceId, dataTypes),
    getDevices: () => ipcRenderer.invoke('sync:getDevices'),
    getOperations: () => ipcRenderer.invoke('sync:getOperations')
  },

  modules: {
    getInstalled: () => ipcRenderer.invoke('modules:getInstalled'),
    install: (module) => ipcRenderer.invoke('modules:install', module),
    uninstall: (moduleId) => ipcRenderer.invoke('modules:uninstall', moduleId),
    activate: (moduleId) => ipcRenderer.invoke('modules:activate', moduleId),
    deactivate: (moduleId) => ipcRenderer.invoke('modules:deactivate', moduleId),
    updateSettings: (moduleId, settings) => ipcRenderer.invoke('modules:updateSettings', moduleId, settings)
  },

  auth: {
    login: (credentials) => ipcRenderer.invoke('auth:login', credentials),
    logout: () => ipcRenderer.invoke('auth:logout'),
    getCurrentUser: () => ipcRenderer.invoke('auth:getCurrentUser'),
    verifyToken: (token) => ipcRenderer.invoke('auth:verifyToken', token)
  },

  files: {
    selectFile: (options) => ipcRenderer.invoke('files:selectFile', options),
    saveFile: (data, options) => ipcRenderer.invoke('files:saveFile', data, options),
    readFile: (filePath) => ipcRenderer.invoke('files:readFile', filePath),
    writeFile: (filePath, data) => ipcRenderer.invoke('files:writeFile', filePath, data)
  },

  system: {
    getInfo: () => ipcRenderer.invoke('system:getInfo'),
    openExternal: (url) => ipcRenderer.invoke('system:openExternal', url),
    showNotification: (options) => ipcRenderer.invoke('system:showNotification', options),
    setFullscreen: (fullscreen) => ipcRenderer.invoke('system:setFullscreen', fullscreen),
    minimize: () => ipcRenderer.invoke('system:minimize'),
    maximize: () => ipcRenderer.invoke('system:maximize'),
    close: () => ipcRenderer.invoke('system:close')
  },

  on: (channel, callback) => {
    const validChannels = [
      'sync:device-discovered',
      'sync:operation-progress',
      'sync:operation-completed',
      'auth:user-updated',
      'module:status-changed',
      'system:notification'
    ];
    
    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, callback);
    }
  },

  off: (channel, callback) => {
    ipcRenderer.removeListener(channel, callback);
  },

  isElectron: true,
  
  getAppVersion: () => ipcRenderer.invoke('app:getVersion'),
  getAppPath: () => ipcRenderer.invoke('app:getPath'),
  
  isDev: process.env.NODE_ENV === 'development',
  platform: process.platform
});

contextBridge.exposeInMainWorld('isElectron', true);

console.log('Electron preload script loaded successfully');
