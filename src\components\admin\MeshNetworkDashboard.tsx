import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Wifi,
  Bluetooth,
  Ethernet,
  Monitor,
  Users,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  Settings,
  RefreshCw
} from 'lucide-react';
import MeshNetworkService, { NetworkNode, NetworkRole, SyncMethod } from '../../services/mesh-network.service';

interface MeshNetworkDashboardProps {
  meshService: MeshNetworkService;
}

export const MeshNetworkDashboard: React.FC<MeshNetworkDashboardProps> = ({ meshService }) => {
  const [networkStatus, setNetworkStatus] = useState(meshService.getNetworkStatus());
  const [nodes, setNodes] = useState<NetworkNode[]>([]);
  const [selectedNode, setSelectedNode] = useState<NetworkNode | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    const updateStatus = () => {
      setNetworkStatus(meshService.getNetworkStatus());
      setNodes(meshService.getNodes());
    };

    // Initial load
    updateStatus();

    // Set up event listeners
    meshService.on('network-initialized', updateStatus);
    meshService.on('sync-completed', updateStatus);
    meshService.on('node-offline', updateStatus);

    // Refresh every 5 seconds
    const interval = setInterval(updateStatus, 5000);

    return () => {
      clearInterval(interval);
      meshService.removeListener('network-initialized', updateStatus);
      meshService.removeListener('sync-completed', updateStatus);
      meshService.removeListener('node-offline', updateStatus);
    };
  }, [meshService]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Trigger network discovery
    setTimeout(() => {
      setNetworkStatus(meshService.getNetworkStatus());
      setNodes(meshService.getNodes());
      setIsRefreshing(false);
    }, 2000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-500';
      case 'offline': return 'text-red-500';
      case 'syncing': return 'text-blue-500';
      case 'error': return 'text-orange-500';
      default: return 'text-gray-500';
    }
  };

  const getRoleIcon = (role: NetworkRole) => {
    switch (role) {
      case 'master': return <Monitor className="w-4 h-4" />;
      case 'slave': return <Users className="w-4 h-4" />;
      case 'relay': return <Zap className="w-4 h-4" />;
    }
  };

  const getSyncMethodIcon = (method: SyncMethod) => {
    switch (method) {
      case 'wifi': return <Wifi className="w-4 h-4" />;
      case 'bluetooth': return <Bluetooth className="w-4 h-4" />;
      case 'ethernet': return <Ethernet className="w-4 h-4" />;
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Mesh Network Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Monitor and manage the local CBT network
          </p>
        </div>
        <button
          onClick={handleRefresh}
          disabled={isRefreshing}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {/* Network Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Network Status</p>
              <p className={`text-lg font-semibold ${networkStatus.isRunning ? 'text-green-500' : 'text-red-500'}`}>
                {networkStatus.isRunning ? 'Online' : 'Offline'}
              </p>
            </div>
            <Activity className={`w-8 h-8 ${networkStatus.isRunning ? 'text-green-500' : 'text-red-500'}`} />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Nodes</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {networkStatus.nodeCount}
              </p>
            </div>
            <Monitor className="w-8 h-8 text-blue-500" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Online Nodes</p>
              <p className="text-lg font-semibold text-green-500">
                {networkStatus.onlineNodes}
              </p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Pending Sync</p>
              <p className="text-lg font-semibold text-orange-500">
                {networkStatus.pendingOperations}
              </p>
            </div>
            <Clock className="w-8 h-8 text-orange-500" />
          </div>
        </motion.div>
      </div>

      {/* Nodes Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Nodes List */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-lg"
        >
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Network Nodes
            </h2>
          </div>
          <div className="p-6">
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {nodes.map((node) => (
                <motion.div
                  key={node.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className={`p-4 border rounded-lg cursor-pointer transition-all ${
                    selectedNode?.id === node.id
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedNode(node)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getRoleIcon(node.role)}
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {node.name}
                        </p>
                        <p className="text-sm text-gray-500">
                          {node.ipAddress}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`text-sm font-medium ${getStatusColor(node.status)}`}>
                        {node.status}
                      </span>
                      {node.status === 'online' && (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      )}
                      {node.status === 'offline' && (
                        <AlertTriangle className="w-4 h-4 text-red-500" />
                      )}
                    </div>
                  </div>
                  
                  <div className="mt-3 flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      {node.syncMethods.map((method) => (
                        <div key={method} className="text-gray-400">
                          {getSyncMethodIcon(method)}
                        </div>
                      ))}
                    </div>
                    <span className="text-xs text-gray-500">
                      Last seen: {new Date(node.lastSeen).toLocaleTimeString()}
                    </span>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Node Details */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-lg"
        >
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Node Details
            </h2>
          </div>
          <div className="p-6">
            {selectedNode ? (
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Node Name
                  </label>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {selectedNode.name}
                  </p>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Role
                  </label>
                  <div className="flex items-center space-x-2 mt-1">
                    {getRoleIcon(selectedNode.role)}
                    <span className="text-gray-900 dark:text-white capitalize">
                      {selectedNode.role}
                    </span>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Network Information
                  </label>
                  <div className="mt-1 space-y-1">
                    <p className="text-sm text-gray-900 dark:text-white">
                      IP: {selectedNode.ipAddress}
                    </p>
                    <p className="text-sm text-gray-900 dark:text-white">
                      MAC: {selectedNode.macAddress}
                    </p>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Sync Methods
                  </label>
                  <div className="flex items-center space-x-2 mt-1">
                    {selectedNode.syncMethods.map((method) => (
                      <div key={method} className="flex items-center space-x-1 px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">
                        {getSyncMethodIcon(method)}
                        <span className="text-xs text-gray-700 dark:text-gray-300 capitalize">
                          {method}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Status
                  </label>
                  <p className={`text-lg font-semibold ${getStatusColor(selectedNode.status)}`}>
                    {selectedNode.status}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Data Version
                  </label>
                  <p className="text-gray-900 dark:text-white">
                    v{selectedNode.dataVersion}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Pending Operations
                  </label>
                  <p className="text-gray-900 dark:text-white">
                    {selectedNode.pendingOperations}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Last Seen
                  </label>
                  <p className="text-gray-900 dark:text-white">
                    {new Date(selectedNode.lastSeen).toLocaleString()}
                  </p>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <Monitor className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Select a node to view details</p>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
};
