import * as XLSX from 'xlsx';
import { parseString } from 'xml2js';
import { Question, QuestionType, DifficultyLevel, Test } from '../types/cbt.types';
import { databaseService } from './database.service';

export type ImportFormat = 'xml' | 'excel' | 'csv' | 'json';
export type AssessmentType = 'exam' | 'test' | 'quiz' | 'practice';

export interface ImportResult {
  success: boolean;
  totalQuestions: number;
  importedQuestions: number;
  errors: string[];
  warnings: string[];
  duplicates: number;
}

export interface QuestionImportData {
  question: string;
  type: QuestionType;
  options?: string[];
  correctAnswer: string | number | string[];
  explanation?: string;
  difficulty: DifficultyLevel;
  category?: string;
  tags?: string[];
  points?: number;
  timeLimit?: number;
}

export interface AssessmentTemplate {
  id: string;
  name: string;
  type: AssessmentType;
  description: string;
  settings: {
    duration: number;
    passingScore: number;
    allowReview: boolean;
    shuffleQuestions: boolean;
    shuffleOptions: boolean;
    showTimer: boolean;
    immediateResults: boolean;
    maxAttempts: number;
  };
  security: {
    fullScreenRequired: boolean;
    disableRightClick: boolean;
    disableCopy: boolean;
    preventTabSwitching: boolean;
    screenshotDetection: boolean;
  };
  questionDistribution: {
    [category: string]: {
      count: number;
      difficulty: {
        easy: number;
        medium: number;
        hard: number;
      };
    };
  };
}

class ContentService {
  // Question Import Methods
  async importQuestions(file: File, format: ImportFormat): Promise<ImportResult> {
    const result: ImportResult = {
      success: false,
      totalQuestions: 0,
      importedQuestions: 0,
      errors: [],
      warnings: [],
      duplicates: 0
    };

    try {
      let questions: QuestionImportData[] = [];

      switch (format) {
        case 'xml':
          questions = await this.parseXMLFile(file);
          break;
        case 'excel':
          questions = await this.parseExcelFile(file);
          break;
        case 'csv':
          questions = await this.parseCSVFile(file);
          break;
        case 'json':
          questions = await this.parseJSONFile(file);
          break;
        default:
          throw new Error(`Unsupported format: ${format}`);
      }

      result.totalQuestions = questions.length;

      // Validate and import questions
      for (const questionData of questions) {
        try {
          const validation = this.validateQuestion(questionData);
          if (!validation.isValid) {
            result.errors.push(`Question "${questionData.question.substring(0, 50)}...": ${validation.errors.join(', ')}`);
            continue;
          }

          // Check for duplicates
          const isDuplicate = await this.checkDuplicate(questionData);
          if (isDuplicate) {
            result.duplicates++;
            result.warnings.push(`Duplicate question skipped: "${questionData.question.substring(0, 50)}..."`);
            continue;
          }

          // Convert to Question format and save
          const question = this.convertToQuestion(questionData);
          await this.saveQuestion(question);
          result.importedQuestions++;

        } catch (error) {
          result.errors.push(`Error importing question: ${error instanceof Error ? error.message : String(error)}`);
        }
      }

      result.success = result.importedQuestions > 0;
      return result;

    } catch (error) {
      result.errors.push(`Import failed: ${error instanceof Error ? error.message : String(error)}`);
      return result;
    }
  }

  private async parseXMLFile(file: File): Promise<QuestionImportData[]> {
    const text = await file.text();
    
    return new Promise((resolve, reject) => {
      parseString(text, (err, result) => {
        if (err) {
          reject(new Error(`XML parsing error: ${err.message}`));
          return;
        }

        try {
          const questions: QuestionImportData[] = [];
          const xmlQuestions = result.questions?.question || [];

          for (const xmlQ of xmlQuestions) {
            const question: QuestionImportData = {
              question: xmlQ.text?.[0] || '',
              type: this.mapQuestionType(xmlQ.type?.[0] || 'multiple_choice'),
              options: xmlQ.options?.[0]?.option || [],
              correctAnswer: xmlQ.answer?.[0] || '',
              explanation: xmlQ.explanation?.[0] || '',
              difficulty: this.mapDifficulty(xmlQ.difficulty?.[0] || 'medium'),
              category: xmlQ.category?.[0] || 'General',
              tags: xmlQ.tags?.[0]?.tag || [],
              points: parseInt(xmlQ.points?.[0] || '1'),
              timeLimit: parseInt(xmlQ.timeLimit?.[0] || '60')
            };
            questions.push(question);
          }

          resolve(questions);
        } catch (error) {
          reject(new Error(`XML structure error: ${error instanceof Error ? error.message : String(error)}`));
        }
      });
    });
  }

  private async parseExcelFile(file: File): Promise<QuestionImportData[]> {
    const buffer = await file.arrayBuffer();
    const workbook = XLSX.read(buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);

    const questions: QuestionImportData[] = [];

    for (const row of data as any[]) {
      const question: QuestionImportData = {
        question: row['Question'] || row['question'] || '',
        type: this.mapQuestionType(row['Type'] || row['type'] || 'multiple_choice'),
        options: this.parseOptions(row['Options'] || row['options'] || ''),
        correctAnswer: row['Correct Answer'] || row['correct_answer'] || row['answer'] || '',
        explanation: row['Explanation'] || row['explanation'] || '',
        difficulty: this.mapDifficulty(row['Difficulty'] || row['difficulty'] || 'medium'),
        category: row['Category'] || row['category'] || 'General',
        tags: this.parseTags(row['Tags'] || row['tags'] || ''),
        points: parseInt(row['Points'] || row['points'] || '1'),
        timeLimit: parseInt(row['Time Limit'] || row['time_limit'] || '60')
      };
      questions.push(question);
    }

    return questions;
  }

  private async parseCSVFile(file: File): Promise<QuestionImportData[]> {
    const text = await file.text();
    const lines = text.split('\n');
    const headers = lines[0].split(',').map(h => h.trim());
    const questions: QuestionImportData[] = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim());
      if (values.length < headers.length) continue;

      const row: any = {};
      headers.forEach((header, index) => {
        row[header] = values[index];
      });

      const question: QuestionImportData = {
        question: row['Question'] || row['question'] || '',
        type: this.mapQuestionType(row['Type'] || row['type'] || 'multiple_choice'),
        options: this.parseOptions(row['Options'] || row['options'] || ''),
        correctAnswer: row['Correct Answer'] || row['correct_answer'] || row['answer'] || '',
        explanation: row['Explanation'] || row['explanation'] || '',
        difficulty: this.mapDifficulty(row['Difficulty'] || row['difficulty'] || 'medium'),
        category: row['Category'] || row['category'] || 'General',
        tags: this.parseTags(row['Tags'] || row['tags'] || ''),
        points: parseInt(row['Points'] || row['points'] || '1'),
        timeLimit: parseInt(row['Time Limit'] || row['time_limit'] || '60')
      };
      questions.push(question);
    }

    return questions;
  }

  private async parseJSONFile(file: File): Promise<QuestionImportData[]> {
    const text = await file.text();
    const data = JSON.parse(text);
    
    if (!Array.isArray(data)) {
      throw new Error('JSON file must contain an array of questions');
    }

    return data.map(item => ({
      question: item.question || '',
      type: this.mapQuestionType(item.type || 'multiple_choice'),
      options: Array.isArray(item.options) ? item.options : this.parseOptions(item.options || ''),
      correctAnswer: item.correctAnswer || item.answer || '',
      explanation: item.explanation || '',
      difficulty: this.mapDifficulty(item.difficulty || 'medium'),
      category: item.category || 'General',
      tags: Array.isArray(item.tags) ? item.tags : this.parseTags(item.tags || ''),
      points: parseInt(item.points || '1'),
      timeLimit: parseInt(item.timeLimit || '60')
    }));
  }

  // Assessment Template Methods
  getAssessmentTemplates(): AssessmentTemplate[] {
    return [
      {
        id: 'exam-template',
        name: 'Standard Exam',
        type: 'exam',
        description: 'High-stakes examination with strict security',
        settings: {
          duration: 120,
          passingScore: 60,
          allowReview: false,
          shuffleQuestions: true,
          shuffleOptions: true,
          showTimer: true,
          immediateResults: false,
          maxAttempts: 1
        },
        security: {
          fullScreenRequired: true,
          disableRightClick: true,
          disableCopy: true,
          preventTabSwitching: true,
          screenshotDetection: true
        },
        questionDistribution: {
          'Mathematics': { count: 20, difficulty: { easy: 5, medium: 10, hard: 5 } },
          'Science': { count: 15, difficulty: { easy: 3, medium: 8, hard: 4 } },
          'English': { count: 15, difficulty: { easy: 4, medium: 7, hard: 4 } }
        }
      },
      {
        id: 'test-template',
        name: 'Regular Test',
        type: 'test',
        description: 'Standard classroom test',
        settings: {
          duration: 60,
          passingScore: 50,
          allowReview: true,
          shuffleQuestions: false,
          shuffleOptions: false,
          showTimer: true,
          immediateResults: true,
          maxAttempts: 2
        },
        security: {
          fullScreenRequired: false,
          disableRightClick: false,
          disableCopy: true,
          preventTabSwitching: false,
          screenshotDetection: false
        },
        questionDistribution: {
          'General': { count: 25, difficulty: { easy: 10, medium: 10, hard: 5 } }
        }
      },
      {
        id: 'quiz-template',
        name: 'Quick Quiz',
        type: 'quiz',
        description: 'Short assessment for quick evaluation',
        settings: {
          duration: 15,
          passingScore: 70,
          allowReview: true,
          shuffleQuestions: true,
          shuffleOptions: false,
          showTimer: false,
          immediateResults: true,
          maxAttempts: 3
        },
        security: {
          fullScreenRequired: false,
          disableRightClick: false,
          disableCopy: false,
          preventTabSwitching: false,
          screenshotDetection: false
        },
        questionDistribution: {
          'General': { count: 10, difficulty: { easy: 5, medium: 4, hard: 1 } }
        }
      },
      {
        id: 'practice-template',
        name: 'Practice Session',
        type: 'practice',
        description: 'Unlimited practice with immediate feedback',
        settings: {
          duration: 0, // Unlimited
          passingScore: 0,
          allowReview: true,
          shuffleQuestions: false,
          shuffleOptions: false,
          showTimer: false,
          immediateResults: true,
          maxAttempts: 0 // Unlimited
        },
        security: {
          fullScreenRequired: false,
          disableRightClick: false,
          disableCopy: false,
          preventTabSwitching: false,
          screenshotDetection: false
        },
        questionDistribution: {
          'General': { count: 50, difficulty: { easy: 20, medium: 20, hard: 10 } }
        }
      }
    ];
  }

  async createTestFromTemplate(templateId: string, customSettings?: Partial<AssessmentTemplate>): Promise<Test> {
    const template = this.getAssessmentTemplates().find(t => t.id === templateId);
    if (!template) {
      throw new Error('Template not found');
    }

    // Merge custom settings with template
    const finalTemplate = customSettings ? { ...template, ...customSettings } : template;

    // Generate questions based on distribution
    const questions = await this.generateQuestionsFromDistribution(finalTemplate.questionDistribution);

    const test: Omit<Test, 'id' | 'createdAt' | 'updatedAt'> = {
      title: finalTemplate.name,
      description: finalTemplate.description,
      moduleId: 'cbt-core',
      createdBy: 'system', // Should be current user
      duration: finalTemplate.settings.duration,
      totalQuestions: questions.length,
      passingScore: finalTemplate.settings.passingScore,
      isActive: true,
      settings: {
        allowReview: finalTemplate.settings.allowReview,
        shuffleQuestions: finalTemplate.settings.shuffleQuestions,
        shuffleOptions: finalTemplate.settings.shuffleOptions,
        showTimer: finalTemplate.settings.showTimer,
        timePerQuestion: Math.floor(finalTemplate.settings.duration / questions.length),
        negativeMarking: false,
        negativeMarkingValue: 0,
        partialMarking: false
      },
      security: finalTemplate.security,
      questions: questions,
      instructions: `This is a ${finalTemplate.type} with ${questions.length} questions. You have ${finalTemplate.settings.duration} minutes to complete.`,
      tags: [finalTemplate.type, 'auto-generated']
    };

    return await databaseService.createTest(test);
  }

  // Helper Methods
  private mapQuestionType(type: string): QuestionType {
    const typeMap: { [key: string]: QuestionType } = {
      'multiple_choice': 'multiple_choice',
      'mcq': 'multiple_choice',
      'true_false': 'true_false',
      'boolean': 'true_false',
      'essay': 'essay',
      'text': 'essay',
      'fill_blank': 'fill_blank',
      'fill_in_blank': 'fill_blank',
      'matching': 'matching',
      'ordering': 'ordering',
      'numeric': 'numeric',
      'number': 'numeric',
      'file_upload': 'file_upload',
      'upload': 'file_upload'
    };
    
    return typeMap[type.toLowerCase()] || 'multiple_choice';
  }

  private mapDifficulty(difficulty: string): DifficultyLevel {
    const diffMap: { [key: string]: DifficultyLevel } = {
      'easy': 'easy',
      'medium': 'medium',
      'hard': 'hard',
      'difficult': 'hard'
    };
    
    return diffMap[difficulty.toLowerCase()] || 'medium';
  }

  private parseOptions(optionsStr: string): string[] {
    if (!optionsStr) return [];
    
    // Handle different separators
    const separators = [';', '|', '\n', ','];
    for (const sep of separators) {
      if (optionsStr.includes(sep)) {
        return optionsStr.split(sep).map(opt => opt.trim()).filter(opt => opt.length > 0);
      }
    }
    
    return [optionsStr.trim()];
  }

  private parseTags(tagsStr: string): string[] {
    if (!tagsStr) return [];
    return tagsStr.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
  }

  private validateQuestion(question: QuestionImportData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!question.question || question.question.trim().length === 0) {
      errors.push('Question text is required');
    }

    if (question.type === 'multiple_choice' && (!question.options || question.options.length < 2)) {
      errors.push('Multiple choice questions must have at least 2 options');
    }

    if (!question.correctAnswer) {
      errors.push('Correct answer is required');
    }

    if (question.points && (question.points < 0 || question.points > 100)) {
      errors.push('Points must be between 0 and 100');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  private async checkDuplicate(question: QuestionImportData): Promise<boolean> {
    // Simple duplicate check based on question text
    // In a real implementation, you might use more sophisticated matching
    const existingQuestions = await this.searchQuestions(question.question.substring(0, 50));
    return existingQuestions.length > 0;
  }

  private convertToQuestion(data: QuestionImportData): Omit<Question, 'id' | 'createdAt' | 'updatedAt'> {
    return {
      text: data.question,
      type: data.type,
      options: (data.options || []).map((option, index) => ({
        id: `opt_${index}`,
        text: option,
        isCorrect: false
      })),
      correctAnswer: data.correctAnswer,
      explanation: data.explanation,
      difficulty: data.difficulty,
      category: data.category || 'General',
      tags: data.tags || [],
      points: data.points || 1,
      timeLimit: data.timeLimit,
      moduleId: 'cbt-core',
      createdBy: 'system' // Should be current user
    };
  }

  private async saveQuestion(question: Omit<Question, 'id' | 'createdAt' | 'updatedAt'>): Promise<Question> {
    // This would save to database
    // For now, return a mock question with generated ID
    return {
      ...question,
      id: Math.random().toString(36).substr(2, 9),
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  private async searchQuestions(query: string): Promise<Question[]> {
    // This would search in database
    // For now, return empty array
    return [];
  }

  private async generateQuestionsFromDistribution(distribution: any): Promise<Question[]> {
    // This would generate questions based on the distribution
    // For now, return empty array
    return [];
  }

  // Export template methods
  generateImportTemplate(format: ImportFormat): string {
    const headers = [
      'Question',
      'Type',
      'Options',
      'Correct Answer',
      'Explanation',
      'Difficulty',
      'Category',
      'Tags',
      'Points',
      'Time Limit'
    ];

    const sampleData = [
      'What is 2 + 2?',
      'multiple_choice',
      'A) 3; B) 4; C) 5; D) 6',
      'B',
      'Basic addition: 2 + 2 = 4',
      'easy',
      'Mathematics',
      'arithmetic, basic',
      '1',
      '30'
    ];

    switch (format) {
      case 'csv':
        return [headers.join(','), sampleData.join(',')].join('\n');
      case 'excel':
        // Return instructions for Excel format
        return 'Excel template: Create columns with headers: ' + headers.join(', ');
      case 'xml':
        return `<?xml version="1.0" encoding="UTF-8"?>
<questions>
  <question>
    <text>${sampleData[0]}</text>
    <type>${sampleData[1]}</type>
    <options>
      <option>A) 3</option>
      <option>B) 4</option>
      <option>C) 5</option>
      <option>D) 6</option>
    </options>
    <answer>${sampleData[3]}</answer>
    <explanation>${sampleData[4]}</explanation>
    <difficulty>${sampleData[5]}</difficulty>
    <category>${sampleData[6]}</category>
    <tags>
      <tag>arithmetic</tag>
      <tag>basic</tag>
    </tags>
    <points>${sampleData[8]}</points>
    <timeLimit>${sampleData[9]}</timeLimit>
  </question>
</questions>`;
      default:
        return 'Unsupported format';
    }
  }
}

export const contentService = new ContentService();
