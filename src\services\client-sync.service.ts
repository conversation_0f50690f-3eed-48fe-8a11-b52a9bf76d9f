import { EventEmitter } from 'events';

export type SyncMethod = 'wifi' | 'bluetooth' | 'usb' | 'file';
export type SyncStatus = 'idle' | 'discovering' | 'connecting' | 'syncing' | 'success' | 'error';

export interface SyncDevice {
  id: string;
  name: string;
  type: SyncMethod;
  address: string;
  isOnline: boolean;
  lastSeen: Date;
  capabilities: string[];
}

export interface SyncOperation {
  id: string;
  method: SyncMethod;
  deviceId: string;
  status: SyncStatus;
  progress: number;
  startTime: Date;
  endTime?: Date;
  error?: string;
  dataTypes: string[];
  recordsCount: number;
}

class ClientSyncService extends EventEmitter {
  private operations: Map<string, SyncOperation> = new Map();
  private devices: Map<string, SyncDevice> = new Map();
  private isDiscovering = false;
  private isElectron = typeof window !== 'undefined' && (window as any).electronAPI;

  constructor() {
    super();
    this.initializeSync();
  }

  private async initializeSync() {
    if (this.isElectron) {
      // Use Electron IPC for sync operations
      await this.initializeElectronSync();
    } else {
      // Browser-based sync simulation
      await this.initializeBrowserSync();
    }
  }

  private async initializeElectronSync() {
    try {
      // Check if electronAPI exists and has sync methods
      if ((window as any).electronAPI?.sync?.getAvailableMethods) {
        const methods = await (window as any).electronAPI.sync.getAvailableMethods();
        this.emit('methods-detected', methods);
      } else {
        // Fallback to browser sync if Electron API not available
        console.log('Electron API not available, falling back to browser sync');
        await this.initializeBrowserSync();
      }
    } catch (error) {
      console.warn('Failed to initialize Electron sync, using browser fallback:', error);
      await this.initializeBrowserSync();
    }
  }

  private async initializeBrowserSync() {
    // Simulate available methods in browser
    const methods: SyncMethod[] = ['file'];
    
    // Check for Web Bluetooth API
    if ('bluetooth' in navigator) {
      methods.push('bluetooth');
    }
    
    // Check for WebUSB API
    if ('usb' in navigator) {
      methods.push('usb');
    }
    
    // WiFi is always available (network requests)
    if (navigator.onLine) {
      methods.push('wifi');
    }
    
    this.emit('methods-detected', methods);
    
    // Add some mock devices for demo
    this.addMockDevices();
  }

  private addMockDevices() {
    const mockDevices: SyncDevice[] = [
      {
        id: 'wifi-*************',
        name: 'CBT Device (*************)',
        type: 'wifi',
        address: '*************',
        isOnline: true,
        lastSeen: new Date(),
        capabilities: ['sync', 'realtime']
      },
      {
        id: 'wifi-*************',
        name: 'CBT Device (*************)',
        type: 'wifi',
        address: '*************',
        isOnline: false,
        lastSeen: new Date(Date.now() - 300000), // 5 minutes ago
        capabilities: ['sync']
      },
      {
        id: 'file-export',
        name: 'File Export/Import',
        type: 'file',
        address: 'local',
        isOnline: true,
        lastSeen: new Date(),
        capabilities: ['sync', 'export', 'import']
      }
    ];

    mockDevices.forEach(device => {
      this.devices.set(device.id, device);
      this.emit('device-discovered', device);
    });
  }

  async startDeviceDiscovery(): Promise<void> {
    if (this.isDiscovering) return;

    this.isDiscovering = true;
    this.emit('discovery-started');

    try {
      if (this.isElectron) {
        await (window as any).electronAPI.sync.startDiscovery();
      } else {
        // Browser-based discovery simulation
        await this.simulateDiscovery();
      }
    } catch (error) {
      console.error('Device discovery error:', error);
      this.emit('discovery-error', error);
    } finally {
      this.isDiscovering = false;
      this.emit('discovery-completed');
    }
  }

  private async simulateDiscovery(): Promise<void> {
    // Simulate discovery delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Update device status
    for (const device of this.devices.values()) {
      if (device.type === 'wifi') {
        // Randomly update online status
        device.isOnline = Math.random() > 0.3;
        device.lastSeen = new Date();
        this.emit('device-discovered', device);
      }
    }
  }

  async syncWithDevice(deviceId: string, dataTypes: string[] = ['all']): Promise<string> {
    const device = this.devices.get(deviceId);
    if (!device) {
      throw new Error('Device not found');
    }

    const operationId = this.generateId();
    const operation: SyncOperation = {
      id: operationId,
      method: device.type,
      deviceId,
      status: 'connecting',
      progress: 0,
      startTime: new Date(),
      dataTypes,
      recordsCount: 0
    };

    this.operations.set(operationId, operation);
    this.emit('sync-started', operation);

    try {
      if (this.isElectron) {
        await (window as any).electronAPI.sync.syncWithDevice(deviceId, dataTypes);
      } else {
        await this.simulateSync(operation);
      }

      operation.status = 'success';
      operation.progress = 100;
      operation.endTime = new Date();
      this.emit('sync-completed', operation);

    } catch (error) {
      operation.status = 'error';
      operation.error = error instanceof Error ? error.message : 'Unknown error';
      operation.endTime = new Date();
      this.emit('sync-error', operation);
      throw error;
    }

    return operationId;
  }

  private async simulateSync(operation: SyncOperation): Promise<void> {
    const device = this.devices.get(operation.deviceId);
    if (!device) throw new Error('Device not found');

    // Simulate sync progress
    const steps = [
      { progress: 10, status: 'connecting', delay: 500 },
      { progress: 30, status: 'syncing', delay: 1000 },
      { progress: 60, status: 'syncing', delay: 1500 },
      { progress: 90, status: 'syncing', delay: 1000 }
    ];

    for (const step of steps) {
      await new Promise(resolve => setTimeout(resolve, step.delay));
      operation.progress = step.progress;
      operation.status = step.status as SyncStatus;
      this.emit('sync-progress', operation);
    }

    // Simulate data transfer
    operation.recordsCount = Math.floor(Math.random() * 100) + 50;

    // Handle different sync methods
    switch (device.type) {
      case 'file':
        await this.handleFileSync();
        break;
      case 'wifi':
        await this.handleWiFiSync(device);
        break;
      case 'bluetooth':
        await this.handleBluetoothSync(device);
        break;
      case 'usb':
        await this.handleUSBSync(device);
        break;
    }
  }

  private async handleFileSync(): Promise<void> {
    // Create and download sync file
    const syncData = {
      timestamp: new Date(),
      source: 'cbt-system',
      data: this.getMockSyncData(),
      checksum: this.calculateChecksum('mock-data')
    };

    const blob = new Blob([JSON.stringify(syncData, null, 2)], { 
      type: 'application/json' 
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `cbt-sync-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  private async handleWiFiSync(device: SyncDevice): Promise<void> {
    // Simulate WiFi sync
    console.log(`Syncing via WiFi with ${device.address}`);
  }

  private async handleBluetoothSync(device: SyncDevice): Promise<void> {
    // Simulate Bluetooth sync
    console.log(`Syncing via Bluetooth with ${device.name}`);
  }

  private async handleUSBSync(device: SyncDevice): Promise<void> {
    // Simulate USB sync
    console.log(`Syncing via USB with ${device.name}`);
  }

  private getMockSyncData(): any[] {
    return [
      { type: 'users', records: [] },
      { type: 'tests', records: [] },
      { type: 'questions', records: [] }
    ];
  }

  private calculateChecksum(data: string): string {
    // Simple checksum calculation
    return btoa(data).slice(0, 16);
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 11);
  }

  // Public API
  getDevices(): SyncDevice[] {
    return Array.from(this.devices.values());
  }

  getOperations(): SyncOperation[] {
    return Array.from(this.operations.values());
  }

  getOperation(id: string): SyncOperation | undefined {
    return this.operations.get(id);
  }

  // File import handler
  async importSyncFile(file: File): Promise<void> {
    try {
      const text = await file.text();
      const syncData = JSON.parse(text);
      
      // Validate sync data
      if (!syncData.timestamp || !syncData.data) {
        throw new Error('Invalid sync file format');
      }
      
      // Process imported data
      console.log('Importing sync data:', syncData);
      
      // Emit import success
      this.emit('import-completed', {
        recordsCount: syncData.data.length,
        timestamp: syncData.timestamp
      });
      
    } catch (error) {
      console.error('Import error:', error);
      this.emit('import-error', error);
      throw error;
    }
  }
}

export const clientSyncService = new ClientSyncService();
