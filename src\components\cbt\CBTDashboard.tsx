import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { CBTSidebar } from './CBTSidebar';
import { CBTHeader } from './CBTHeader';
import { ModuleSelection } from './ModuleSelection';
import { TestCreation } from './TestCreation';
import { TestTaking } from './TestTaking';
import { ResultsAnalytics } from './ResultsAnalytics';
import { CBTSettings } from './CBTSettings';
import { EnhancedThreeBackground } from '../shared/EnhancedThreeBackground';
import { PageTransition } from '../shared/PageTransition';
import { useCBT, useSecurityMonitoring } from '../../contexts/CBTContext';
import { CBTRoute } from '../../types/cbt.types';

export const CBTDashboard: React.FC = () => {
  const { state, logout } = useCBT();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [activeRoute, setActiveRoute] = useState<CBTRoute>('dashboard');

  // Enable security monitoring
  useSecurityMonitoring();

  const handleSidebarToggle = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const handleRouteChange = (route: CBTRoute) => {
    setActiveRoute(route);
  };

  const handleSearch = (query: string) => {
    console.log('Search query:', query);
    // Implement search functionality
  };

  const handleNotificationClick = () => {
    console.log('Notifications clicked');
    // Implement notifications
  };

  const handleProfileClick = () => {
    setActiveRoute('profile');
  };

  const renderContent = () => {
    switch (activeRoute) {
      case 'dashboard':
      case 'modules':
        return <ModuleSelection />;
      case 'test-creation':
        return <TestCreation />;
      case 'test-taking':
        return <TestTaking />;
      case 'results':
      case 'analytics':
        return <ResultsAnalytics />;
      case 'settings':
      case 'profile':
        return <CBTSettings />;
      default:
        return <ModuleSelection />;
    }
  };

  const getPageTitle = () => {
    switch (activeRoute) {
      case 'dashboard':
        return 'Dashboard Overview';
      case 'modules':
        return 'Module Selection';
      case 'test-creation':
        return 'Test Creation';
      case 'test-taking':
        return 'Test Environment';
      case 'results':
        return 'Test Results';
      case 'analytics':
        return 'Analytics & Reports';
      case 'settings':
        return 'System Settings';
      case 'profile':
        return 'User Profile';
      default:
        return 'CBT System';
    }
  };

  if (!state.user) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4 font-mono">ACCESS DENIED</h2>
          <p className="text-gray-400 font-mono">User session not found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black relative overflow-hidden">
      <EnhancedThreeBackground 
        particleCount={800} 
        shapeCount={15} 
        lineCount={20} 
        animated 
        theme="dashboard" 
      />
      
      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-black/80 via-gray-900/60 to-black/80" />
      
      {/* Main Layout */}
      <div className="relative z-10 flex h-screen">
        {/* Sidebar */}
        <CBTSidebar
          isCollapsed={sidebarCollapsed}
          onToggle={handleSidebarToggle}
          activeRoute={activeRoute}
          onRouteChange={handleRouteChange}
          userRole={state.user.role}
          onLogout={logout}
        />

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <CBTHeader
            user={state.user}
            pageTitle={getPageTitle()}
            onSearch={handleSearch}
            onNotificationClick={handleNotificationClick}
            onProfileClick={handleProfileClick}
          />

          {/* Content Area */}
          <main className="flex-1 overflow-auto">
            <PageTransition
              key={activeRoute}
              variant="slide"
              duration={0.4}
              className="h-full"
            >
              <div className="p-6">
                {renderContent()}
              </div>
            </PageTransition>
          </main>
        </div>
      </div>

      {/* Security Overlay for Test Taking */}
      {activeRoute === 'test-taking' && state.currentAttempt && (
        <div className="fixed inset-0 bg-black/95 z-50 flex items-center justify-center">
          <div className="text-center">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="bg-gray-900/90 backdrop-blur-xl rounded-2xl p-8 border border-gray-600/50"
            >
              <h2 className="text-2xl font-bold text-white mb-4 font-mono">SECURE TEST MODE</h2>
              <p className="text-gray-300 font-mono mb-6">
                You are now in a secure testing environment.
              </p>
              <div className="space-y-2 text-sm text-gray-400 font-mono">
                <p>• Right-click is disabled</p>
                <p>• Copy/paste is disabled</p>
                <p>• Tab switching is monitored</p>
                <p>• Developer tools are blocked</p>
              </div>
            </motion.div>
          </div>
        </div>
      )}
    </div>
  );
};
